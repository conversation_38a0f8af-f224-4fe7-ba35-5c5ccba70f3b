services:
  # Основной сервис - планировщик
  scheduler:
    build: .
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
      - /etc/localtime:/etc/localtime:ro
    command: python -m src.presentation.scheduler.main
    restart: unless-stopped
    environment:
      - TZ=Europe/Kiev
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

  # Сервис Telegram бота
  telegram-bot:
    build: .
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
      - /etc/localtime:/etc/localtime:ro
    command: python -m src.presentation.telegram.bot
    restart: unless-stopped
    environment:
      - TZ=Europe/Kiev
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

  # Сервис для ручного запуска парсера (не запускается автоматически)
  parser:
    build: .
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
      - /etc/localtime:/etc/localtime:ro
    command: python -m src.presentation.cli.main parse
    profiles:
      - manual
    environment:
      - TZ=Europe/Kiev
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1

  # Сервис метрик Prometheus (опционально)
  metrics:
    build: .
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
      - /etc/localtime:/etc/localtime:ro
    command: python scripts/start_metrics_server.py
    ports:
      - "8000:8000"
    profiles:
      - monitoring
    environment:
      - TZ=Europe/Kiev
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1