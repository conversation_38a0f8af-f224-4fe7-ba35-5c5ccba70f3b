---
services:
  parser-olx:
    git_url: **************:Vnedrenec/Parser_olx.git
    git_branch: main
    build_root: ''
    dockerfile_path: Dockerfile
    command: python -m src.presentation.scheduler.main
    ports: []
    volumes:
      - "/data/parser_olx/data:/app/data:rw"
      - "/data/parser_olx/logs:/app/logs:rw"
      - "/data/parser_olx/.env:/app/.env:rw"
    traffic_matches: []
    env_vars:
      TZ: Europe/Kiev
      PYTHONUNBUFFERED: '1'
      PYTHONDONTWRITEBYTECODE: '1'
    restart_on_deploy: true

  telegram-bot:
    git_url: **************:Vnedrenec/Parser_olx.git
    git_branch: main
    build_root: ''
    dockerfile_path: Dockerfile
    command: python -m src.presentation.telegram.bot
    ports: []
    volumes:
      - "/data/parser_olx/data:/app/data:rw"
      - "/data/parser_olx/logs:/app/logs:rw"
      - "/data/parser_olx/.env:/app/.env:rw"
    traffic_matches: []
    env_vars:
      TZ: Europe/Kiev
      PYTHONUNBUFFERED: '1'
      PYTHONDONTWRITEBYTECODE: '1'
    restart_on_deploy: true

  metrics:
    git_url: **************:Vnedrenec/Parser_olx.git
    git_branch: main
    build_root: ''
    dockerfile_path: Dockerfile
    command: python scripts/start_metrics_server.py
    ports:
      - container: 8000
        http: 8000
        https: 8000
    volumes:
      - "/data/parser_olx/data:/app/data:rw"
      - "/data/parser_olx/logs:/app/logs:rw"
      - "/data/parser_olx/.env:/app/.env:rw"
    traffic_matches: []
    env_vars:
      TZ: Europe/Kiev
      PYTHONUNBUFFERED: '1'
      PYTHONDONTWRITEBYTECODE: '1'
    restart_on_deploy: true
