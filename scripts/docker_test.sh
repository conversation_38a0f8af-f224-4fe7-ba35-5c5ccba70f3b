#!/bin/bash

# Скрипт для локального тестирования Docker

# Проверка наличия .env файла
if [ ! -f .env ]; then
    echo "Файл .env не найден. Создаем из примера..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "Создан файл .env из .env.example. Пожалуйста, отредактируйте его перед запуском."
        exit 1
    else
        echo "Файл .env.example не найден. Пожалуйста, создайте файл .env вручную."
        exit 1
    fi
fi

# Создание директорий для данных и логов
mkdir -p data logs

# Сборка Docker образа
echo "Сборка Docker образа..."
docker build -t parser_olx .

# Проверка успешности сборки
if [ $? -ne 0 ]; then
    echo "Ошибка при сборке Docker образа."
    exit 1
fi

echo "Docker образ успешно собран."

# Запуск тестового контейнера
echo "Запуск тестового контейнера..."
docker run --rm -it \
    -v "$(pwd)/data:/app/data" \
    -v "$(pwd)/logs:/app/logs" \
    -v "$(pwd)/.env:/app/.env" \
    -e "PYTHONUNBUFFERED=1" \
    -e "PYTHONDONTWRITEBYTECODE=1" \
    -e "TZ=Europe/Kiev" \
    parser_olx python -m src.presentation.cli.main --help

echo "Тестирование завершено."
echo "Для запуска всех сервисов используйте: docker-compose up -d"
echo "Для запуска только планировщика: docker-compose up -d scheduler"
echo "Для запуска только Telegram бота: docker-compose up -d telegram-bot"
echo "Для ручного запуска Telegram бота: docker run --rm -it -v \"$(pwd)/data:/app/data\" -v \"$(pwd)/logs:/app/logs\" -v \"$(pwd)/.env:/app/.env\" -e \"PYTHONUNBUFFERED=1\" parser_olx python -m src.presentation.telegram"
echo "Для запуска сервиса метрик: docker-compose --profile monitoring up -d"
