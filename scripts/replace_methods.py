import re
import sys

def replace_answer_calls(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Регулярное выражение для поиска вызовов query.answer
    pattern = r'await\s+query\.answer\(([^)]*)\)'
    
    # Функция для замены найденных вызовов
    def replace_match(match):
        args = match.group(1).strip()
        if args:
            return f'await PropertyBot.safe_answer(query, {args})'
        else:
            return f'await PropertyBot.safe_answer(query, "")'
    
    # Заменяем все найденные вызовы
    new_content = re.sub(pattern, replace_match, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f'Файл {file_path} успешно обновлен (answer).')

def replace_edit_message_text_calls(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Регулярное выражение для поиска вызовов query.edit_message_text
    pattern = r'await\s+query\.edit_message_text\(\s*([^,]+)(?:,\s*reply_markup=([^,\)]+))?\s*\)'
    
    # Функция для замены найденных вызовов
    def replace_match(match):
        text = match.group(1).strip()
        reply_markup = match.group(2) if match.group(2) else 'None'
        
        return f'await PropertyBot.safe_edit_message_text(self.application.bot, query.message.chat_id, query.message.message_id, {text}, {reply_markup})'
    
    # Заменяем все найденные вызовы
    new_content = re.sub(pattern, replace_match, content)
    
    # Регулярное выражение для поиска вызовов self.application.bot.edit_message_text
    pattern = r'await\s+self\.application\.bot\.edit_message_text\(\s*text=([^,]+),\s*chat_id=([^,]+),\s*message_id=([^,]+)(?:,\s*reply_markup=([^,\)]+))?\s*\)'
    
    # Функция для замены найденных вызовов
    def replace_match(match):
        text = match.group(1).strip()
        chat_id = match.group(2).strip()
        message_id = match.group(3).strip()
        reply_markup = match.group(4) if match.group(4) else 'None'
        
        return f'await PropertyBot.safe_edit_message_text(self.application.bot, {chat_id}, {message_id}, {text}, {reply_markup})'
    
    # Заменяем все найденные вызовы
    new_content = re.sub(pattern, replace_match, new_content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f'Файл {file_path} успешно обновлен (edit_message_text).')

def replace_send_message_calls(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Регулярное выражение для поиска вызовов self.application.bot.send_message
    pattern = r'await\s+self\.application\.bot\.send_message\(\s*chat_id=([^,]+),\s*text=([^,]+)(?:,\s*reply_markup=([^,\)]+))?\s*\)'
    
    # Функция для замены найденных вызовов
    def replace_match(match):
        chat_id = match.group(1).strip()
        text = match.group(2).strip()
        reply_markup = match.group(3) if match.group(3) else 'None'
        
        return f'await PropertyBot.safe_send_message(self.application.bot, {chat_id}, {text}, {reply_markup})'
    
    # Заменяем все найденные вызовы
    new_content = re.sub(pattern, replace_match, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f'Файл {file_path} успешно обновлен (send_message).')

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print('Использование: python replace_methods.py <путь_к_файлу>')
        sys.exit(1)
    
    file_path = sys.argv[1]
    replace_answer_calls(file_path)
    replace_edit_message_text_calls(file_path)
    replace_send_message_calls(file_path)
