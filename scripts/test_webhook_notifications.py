#!/usr/bin/env python3
"""
Скрипт для тестирования отправки уведомлений на вебхук.
"""
import argparse
import asyncio
import logging
import os
import sys

# Добавляем корневую директорию проекта в путь для импорта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import settings
from src.infrastructure.external.webhook.notification_sender import WebhookNotificationSender
from src.infrastructure.logging.logger import setup_logger


async def test_webhook_notifications(webhook_url: str, message: str, verbose: bool = False):
    """
    Тестирует отправку уведомлений на вебхук.

    Args:
        webhook_url: URL вебхука
        message: Сообщение для отправки
        verbose: Подробный вывод
    """
    # Настраиваем логирование
    log_level = logging.DEBUG if verbose else logging.INFO
    logger = setup_logger("webhook_test", log_level=log_level)

    # Создаем отправитель уведомлений
    webhook_sender = WebhookNotificationSender(webhook_url=webhook_url, logger=logger)

    # Отправляем текстовое сообщение
    logger.info(f"Отправка текстового сообщения на вебхук: {webhook_url}")
    result = await webhook_sender.send(
        message=message,
        chat_id="test",
        parse_mode="HTML"
    )

    if result:
        logger.info("Сообщение успешно отправлено")
    else:
        logger.error("Ошибка при отправке сообщения")

    # Отправляем сообщение с изображением
    logger.info("Отправка сообщения с изображением")
    result = await webhook_sender.send_with_image(
        message=f"{message} (с изображением)",
        image_url="https://via.placeholder.com/300",
        chat_id="test",
        parse_mode="HTML"
    )

    if result:
        logger.info("Сообщение с изображением успешно отправлено")
    else:
        logger.error("Ошибка при отправке сообщения с изображением")

    # Отправляем сообщение с несколькими изображениями
    logger.info("Отправка сообщения с несколькими изображениями")
    result = await webhook_sender.send_with_images(
        message=f"{message} (с несколькими изображениями)",
        image_urls=[
            "https://via.placeholder.com/300/FF0000",
            "https://via.placeholder.com/300/00FF00",
            "https://via.placeholder.com/300/0000FF"
        ],
        chat_id="test",
        parse_mode="HTML"
    )

    if result:
        logger.info("Сообщение с несколькими изображениями успешно отправлено")
    else:
        logger.error("Ошибка при отправке сообщения с несколькими изображениями")

    # Отправляем уведомление об ошибке
    logger.info("Отправка уведомления об ошибке")
    result = await webhook_sender.send_error(
        message=f"Тестовая ошибка: {message}",
        error_chat_id="test_error",
        parse_mode="HTML"
    )

    if result:
        logger.info("Уведомление об ошибке успешно отправлено")
    else:
        logger.error("Ошибка при отправке уведомления об ошибке")


def main():
    """
    Точка входа для скрипта.
    """
    parser = argparse.ArgumentParser(description='Тестирование отправки уведомлений на вебхук')
    parser.add_argument('--url', '-u', help='URL вебхука')
    parser.add_argument('--message', '-m', default='Тестовое сообщение', help='Сообщение для отправки')
    parser.add_argument('--verbose', '-v', action='store_true', help='Подробный вывод')
    args = parser.parse_args()

    # Получаем URL вебхука
    webhook_url = args.url or settings.get_webhook_url()
    if not webhook_url:
        print("Ошибка: Не указан URL вебхука")
        return 1

    # Запускаем тестирование
    asyncio.run(test_webhook_notifications(webhook_url, args.message, args.verbose))
    return 0


if __name__ == '__main__':
    sys.exit(main())
