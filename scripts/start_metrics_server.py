#!/usr/bin/env python
"""
Скрипт для запуска сервера метрик Prometheus.
"""
import sys
import os
import time
import logging

# Добавляем директорию проекта в путь поиска модулей
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.monitoring.server import start_metrics_server
from src.monitoring.metrics import PARSED_URLS, FAILED_URLS, PROCESSED_PROPERTIES, SENT_NOTIFICATIONS, ACTIVE_PARSERS

# Инициализируем метрики для теста
PARSED_URLS.inc(10)
FAILED_URLS.inc(2)
PROCESSED_PROPERTIES.inc(8)
SENT_NOTIFICATIONS.inc(5)
ACTIVE_PARSERS.set(0)

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

if __name__ == '__main__':
    port = 8000
    logger.info(f"Запуск сервера метрик на порту {port}...")

    try:
        # Запуск сервера метрик
        server = start_metrics_server(port=port)

        if server is None:
            logger.error("Не удалось запустить сервер метрик. Возможно, не установлен prometheus_client.")
            logger.info("Установите prometheus_client: pip install prometheus_client")
            sys.exit(1)

        logger.info(f"Сервер метрик запущен и доступен по адресу http://localhost:{port}/metrics")

        # Бесконечный цикл для поддержания работы сервера
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Получен сигнал завершения. Остановка сервера метрик...")
        if 'server' in locals() and server is not None:
            server.shutdown()
        logger.info("Сервер метрик остановлен.")
    except Exception as e:
        logger.error(f"Произошла ошибка при запуске сервера метрик: {e}", exc_info=True)
        sys.exit(1)
