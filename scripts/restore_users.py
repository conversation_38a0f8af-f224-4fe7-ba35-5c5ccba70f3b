#!/usr/bin/env python
"""
Скрипт для восстановления данных о пользователях в файле access_requests.json.
"""
import json
import os
import sys

# Добавляем корневую директорию проекта в путь для импорта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config.paths import get_data_path

def restore_users():
    """
    Восстанавливает данные о пользователях в файле access_requests.json.
    """
    # Путь к файлу с запросами на доступ
    access_requests_path = get_data_path("access_requests.json")

    # Проверяем, существует ли файл
    if not os.path.exists(access_requests_path):
        print(f"Файл {access_requests_path} не найден.")
        return

    # Загружаем текущие данные
    with open(access_requests_path, 'r', encoding='utf-8') as f:
        access_requests = json.load(f)

    # Путь к файлу с резервной копией
    backup_path = get_data_path("access_requests_backup.json")

    # Проверяем, существует ли файл с резервной копией
    if not os.path.exists(backup_path):
        print(f"Файл с резервной копией {backup_path} не найден.")
        return

    # Загружаем данные из резервной копии
    with open(backup_path, 'r', encoding='utf-8') as f:
        backup_data = json.load(f)

    # Заменяем данные из резервной копии
    new_access_requests = backup_data

    # Сохраняем обновленные данные
    with open(access_requests_path, 'w', encoding='utf-8') as f:
        json.dump(new_access_requests, f, ensure_ascii=False, indent=2)

    print(f"Данные о пользователях в файле {access_requests_path} восстановлены.")

if __name__ == "__main__":
    restore_users()
