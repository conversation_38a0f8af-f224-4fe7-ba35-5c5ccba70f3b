#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Тестовый скрипт для проверки отправки уведомлений об ошибках в отдельный чат Telegram.
"""

import asyncio
import logging
import sys
import os

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config.app_settings import settings
from src.application.services.notification import NotificationService
from src.infrastructure.external.telegram.notification_sender import TelegramNotificationSender


async def test_error_notification():
    """
    Тестирует отправку уведомлений об ошибках в отдельный чат Telegram.
    """
    # Настраиваем логирование
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    # Получаем настройки
    telegram_token = settings.get_telegram_token()
    error_chat_id = settings.get_telegram_error_chat_id()

    if not telegram_token:
        logger.error("Токен Telegram не указан. Уведомления не будут отправляться.")
        return False

    if not error_chat_id:
        logger.error("ID чата для отправки уведомлений об ошибках не указан.")
        return False

    logger.info(f"Токен Telegram: {telegram_token[:5]}...{telegram_token[-5:]}")
    logger.info(f"ID чата для отправки уведомлений об ошибках: {error_chat_id}")

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=telegram_token, logger=logger)

    # Создаем сервис уведомлений
    notification_service = NotificationService(notification_sender=notification_sender, logger=logger)

    # Тестовое сообщение об ошибке
    error_message = (
        "Тестовая ошибка парсера!\n\n"
        "Категория: apartments_sale\n"
        "Ошибка: ConnectionError: Не удалось подключиться к серверу OLX\n\n"
        "Это тестовое сообщение для проверки функционала отправки уведомлений об ошибках в отдельный чат Telegram."
    )

    # Отправляем уведомление об ошибке
    logger.info("Отправка тестового уведомления об ошибке...")
    result = await notification_service.notify_parser_error(error_message, error_chat_id)

    if result:
        logger.info("Тестовое уведомление об ошибке успешно отправлено!")
    else:
        logger.error("Не удалось отправить тестовое уведомление об ошибке.")

    return result


async def test_direct_error_notification():
    """
    Тестирует прямую отправку уведомлений об ошибках через TelegramNotificationSender.
    """
    # Настраиваем логирование
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    # Получаем настройки
    telegram_token = settings.get_telegram_token()
    error_chat_id = settings.get_telegram_error_chat_id()

    if not telegram_token:
        logger.error("Токен Telegram не указан. Уведомления не будут отправляться.")
        return False

    if not error_chat_id:
        logger.error("ID чата для отправки уведомлений об ошибках не указан.")
        return False

    logger.info(f"Токен Telegram: {telegram_token[:5]}...{telegram_token[-5:]}")
    logger.info(f"ID чата для отправки уведомлений об ошибках: {error_chat_id}")

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=telegram_token, logger=logger)

    # Тестовое сообщение об ошибке
    error_message = (
        "<b>ТЕСТОВАЯ ОШИБКА ПАРСЕРА!</b>\n\n"
        "Категория: apartments_sale\n"
        "Ошибка: ConnectionError: Не удалось подключиться к серверу OLX\n\n"
        "Это тестовое сообщение для проверки прямой отправки уведомлений об ошибках через TelegramNotificationSender."
    )

    # Отправляем уведомление об ошибке напрямую через send_error
    logger.info("Отправка тестового уведомления об ошибке напрямую...")
    result = await notification_sender.send_error(error_message, error_chat_id, parse_mode="HTML")

    if result:
        logger.info("Тестовое уведомление об ошибке успешно отправлено напрямую!")
    else:
        logger.error("Не удалось отправить тестовое уведомление об ошибке напрямую.")

    return result


if __name__ == "__main__":
    # Запускаем тесты
    asyncio.run(test_error_notification())
    asyncio.run(test_direct_error_notification())
