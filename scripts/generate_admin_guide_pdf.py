#!/usr/bin/env python3
"""
Скрипт для генерации PDF-версии инструкции администратора
"""
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, ListFlowable, ListItem

# Создаем директорию для PDF, если она не существует
os.makedirs('docs/pdf', exist_ok=True)

# Создаем PDF документ
pdf_path = 'docs/pdf/admin_guide.pdf'
doc = SimpleDocTemplate(pdf_path, pagesize=A4)

# Получаем стили
styles = getSampleStyleSheet()

# Модифицируем существующие стили
styles['Title'].fontSize = 18
styles['Title'].spaceAfter = 12

styles['Heading2'].fontSize = 14
styles['Heading2'].spaceBefore = 12
styles['Heading2'].spaceAfter = 6

styles['Heading3'].fontSize = 12
styles['Heading3'].spaceBefore = 10
styles['Heading3'].spaceAfter = 5

styles['Code'].fontSize = 9
styles['Code'].fontName = 'Courier'
styles['Code'].spaceAfter = 8

# Добавляем новый стиль
styles.add(ParagraphStyle(name='ListItem',
                         parent=styles['Normal'],
                         fontSize=10,
                         leftIndent=20))

# Создаем содержимое документа
content = []

# Заголовок
content.append(Paragraph("Инструкция для администратора Telegram бота OLX Parser", styles['Title']))
content.append(Spacer(1, 12))

# Содержание
content.append(Paragraph("Содержание", styles['Heading2']))
toc_items = [
    "1. Введение",
    "2. Доступ администратора",
    "3. Команды администратора",
    "4. Управление пользователями",
    "5. Управление запросами на доступ",
    "6. Рассылка сообщений",
    "7. Просмотр статистики",
    "8. Меню администратора",
    "9. Часто задаваемые вопросы"
]
for item in toc_items:
    content.append(Paragraph(item, styles['ListItem']))
content.append(Spacer(1, 12))

# Введение
content.append(Paragraph("1. Введение", styles['Heading2']))
content.append(Paragraph(
    "Данная инструкция предназначена для администраторов Telegram бота OLX Parser. "
    "Бот предназначен для отслеживания новых объявлений на сайте OLX и отправки уведомлений "
    "пользователям о новых объявлениях в выбранных категориях недвижимости.",
    styles['Normal']))
content.append(Paragraph(
    "Как администратор, вы имеете расширенные возможности по управлению ботом, "
    "пользователями и настройками.",
    styles['Normal']))
content.append(Spacer(1, 12))

# Доступ администратора
content.append(Paragraph("2. Доступ администратора", styles['Heading2']))
content.append(Paragraph("Для получения прав администратора необходимо:", styles['Normal']))

admin_access_items = [
    Paragraph("Ваш ID пользователя Telegram должен быть добавлен в список администраторов в файле конфигурации <code>config/telegram/bot.yaml</code> или в переменной окружения <code>TELEGRAM_ADMIN_USERS</code>.", styles['Normal']),
    Paragraph("Пример конфигурации в файле <code>bot.yaml</code>:", styles['Normal']),
    Paragraph("# Администраторы бота (список ID пользователей, которые могут подтверждать доступ новых пользователей)\nadmin_users:\n  - 144049944  # Ваш ID пользователя", styles['Code']),
    Paragraph("Пример конфигурации в файле <code>.env</code>:", styles['Normal']),
    Paragraph("TELEGRAM_ADMIN_USERS=144049944,987654321", styles['Code'])
]

# Создаем список для первого элемента
if admin_access_items:
    first_item = admin_access_items[0]
    if isinstance(first_item, Paragraph):
        content.append(Paragraph("1. " + first_item.text, styles['ListItem']))

    # Добавляем остальные элементы
    for item in admin_access_items[1:]:
        content.append(item)

content.append(Spacer(1, 12))

# Команды администратора
content.append(Paragraph("3. Команды администратора", styles['Heading2']))
content.append(Paragraph("Как администратор, вы имеете доступ к следующим специальным командам:", styles['Normal']))

# Таблица команд
commands_data = [
    ["Команда", "Описание"],
    ["/admin", "Открывает меню администратора с кнопками для управления ботом"],
    ["/users", "Управление пользователями (список, блокировка, разблокировка)"],
    ["/requests", "Просмотр и управление запросами на доступ"],
    ["/broadcast", "Отправка сообщений всем пользователям"],
    ["/stats", "Просмотр статистики использования бота"]
]

commands_table = Table(commands_data, colWidths=[100, 300])
commands_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 10),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('LEFTPADDING', (0, 0), (-1, -1), 6),
    ('RIGHTPADDING', (0, 0), (-1, -1), 6),
]))

content.append(commands_table)
content.append(Spacer(1, 12))

# Управление пользователями
content.append(Paragraph("4. Управление пользователями", styles['Heading2']))
content.append(Paragraph(
    "Для управления пользователями используйте команду <code>/users</code> или кнопку \"👥 Пользователи\" в меню администратора.",
    styles['Normal']))

content.append(Paragraph("Просмотр списка пользователей", styles['Heading3']))
view_users_items = [
    Paragraph("Отправьте команду <code>/users</code>", styles['Normal']),
    Paragraph("Нажмите на кнопку \"📋 Список пользователей\"", styles['Normal']),
    Paragraph("Бот отобразит список всех пользователей с их ID и именами", styles['Normal'])
]
for i, item in enumerate(view_users_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Блокировка пользователя", styles['Heading3']))
block_users_items = [
    Paragraph("Отправьте команду <code>/users</code>", styles['Normal']),
    Paragraph("Нажмите на кнопку \"➖ Удалить пользователя\"", styles['Normal']),
    Paragraph("Введите ID пользователя, которого нужно заблокировать", styles['Normal']),
    Paragraph("Подтвердите действие", styles['Normal'])
]
for i, item in enumerate(block_users_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Добавление пользователя", styles['Heading3']))
add_users_items = [
    Paragraph("Отправьте команду <code>/users</code>", styles['Normal']),
    Paragraph("Нажмите на кнопку \"➕ Добавить пользователя\"", styles['Normal']),
    Paragraph("Введите ID пользователя, которого нужно добавить", styles['Normal']),
    Paragraph("Подтвердите действие", styles['Normal'])
]
for i, item in enumerate(add_users_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Spacer(1, 12))

# Управление запросами на доступ
content.append(Paragraph("5. Управление запросами на доступ", styles['Heading2']))
content.append(Paragraph(
    "Для управления запросами на доступ используйте команду <code>/requests</code> или кнопку \"📝 Запросы\" в меню администратора.",
    styles['Normal']))

content.append(Paragraph("Просмотр ожидающих запросов", styles['Heading3']))
view_requests_items = [
    Paragraph("Отправьте команду <code>/requests</code>", styles['Normal']),
    Paragraph("Нажмите на кнопку \"📋 Ожидающие запросы\"", styles['Normal']),
    Paragraph("Бот отобразит список всех ожидающих запросов на доступ", styles['Normal'])
]
for i, item in enumerate(view_requests_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Подтверждение запроса", styles['Heading3']))
content.append(Paragraph(
    "Когда новый пользователь отправляет команду <code>/start</code> боту, вы получаете уведомление с информацией о пользователе и двумя кнопками:",
    styles['Normal']))
approve_request_items = [
    Paragraph("Нажмите на кнопку \"Подтвердить\", чтобы одобрить запрос", styles['Normal']),
    Paragraph("Пользователь получит уведомление о подтверждении доступа", styles['Normal']),
    Paragraph("Пользователь будет добавлен в список разрешенных пользователей", styles['Normal'])
]
for i, item in enumerate(approve_request_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Отклонение запроса", styles['Heading3']))
reject_request_items = [
    Paragraph("Нажмите на кнопку \"Отклонить\", чтобы отклонить запрос", styles['Normal']),
    Paragraph("Пользователь получит уведомление об отклонении доступа", styles['Normal'])
]
for i, item in enumerate(reject_request_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(PageBreak())

# Рассылка сообщений
content.append(Paragraph("6. Рассылка сообщений", styles['Heading2']))
content.append(Paragraph(
    "Для отправки сообщений всем пользователям используйте команду <code>/broadcast</code> или кнопку \"📢 Рассылка\" в меню администратора.",
    styles['Normal']))

content.append(Paragraph("Отправка сообщения всем пользователям", styles['Heading3']))
broadcast_items = [
    Paragraph("Отправьте команду <code>/broadcast &lt;текст сообщения&gt;</code>", styles['Normal']),
    Paragraph("Например: <code>/broadcast Уважаемые пользователи! Сегодня с 14:00 до 15:00 будут проводиться технические работы.</code>", styles['Normal']),
    Paragraph("Бот отправит указанное сообщение всем пользователям и сообщит вам о результатах отправки", styles['Normal'])
]
for i, item in enumerate(broadcast_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Spacer(1, 12))

# Просмотр статистики
content.append(Paragraph("7. Просмотр статистики", styles['Heading2']))
content.append(Paragraph(
    "Для просмотра статистики использования бота используйте команду <code>/stats</code> или кнопку \"📊 Статистика\" в меню администратора.",
    styles['Normal']))

content.append(Paragraph("Статистика включает:", styles['Normal']))
stats_items = [
    Paragraph("Общее количество пользователей", styles['Normal']),
    Paragraph("Количество администраторов", styles['Normal']),
    Paragraph("Количество обычных пользователей", styles['Normal']),
    Paragraph("Количество запросов на доступ (ожидающие, одобренные, отклоненные)", styles['Normal']),
    Paragraph("Количество активных категорий", styles['Normal'])
]
for item in stats_items:
    content.append(ListItem(item, leftIndent=20, bulletType='bullet'))

content.append(Spacer(1, 12))

# Меню администратора
content.append(Paragraph("8. Меню администратора", styles['Heading2']))
content.append(Paragraph(
    "Для доступа к меню администратора отправьте команду <code>/admin</code>. В меню доступны следующие кнопки:",
    styles['Normal']))

# Таблица кнопок меню
menu_data = [
    ["Кнопка", "Описание"],
    ["👥 Пользователи", "Управление пользователями"],
    ["📝 Запросы", "Управление запросами на доступ"],
    ["📊 Статистика", "Просмотр статистики бота"],
    ["📢 Рассылка", "Отправка сообщений всем пользователям"],
    ["◀️ Назад в главное меню", "Возврат в главное меню бота"]
]

menu_table = Table(menu_data, colWidths=[150, 250])
menu_table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
    ('FONTSIZE', (0, 0), (-1, 0), 10),
    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
    ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ('LEFTPADDING', (0, 0), (-1, -1), 6),
    ('RIGHTPADDING', (0, 0), (-1, -1), 6),
]))

content.append(menu_table)
content.append(Spacer(1, 12))

# Часто задаваемые вопросы
content.append(Paragraph("9. Часто задаваемые вопросы", styles['Heading2']))

content.append(Paragraph("Как узнать ID пользователя Telegram?", styles['Heading3']))
content.append(Paragraph("Для получения ID пользователя можно:", styles['Normal']))
user_id_items = [
    Paragraph("Попросить пользователя отправить сообщение боту @userinfobot", styles['Normal']),
    Paragraph("Посмотреть ID в запросе на доступ, который приходит администраторам", styles['Normal'])
]
for i, item in enumerate(user_id_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Как добавить нового администратора?", styles['Heading3']))
add_admin_items = [
    Paragraph("Откройте файл <code>config/telegram/bot.yaml</code>", styles['Normal']),
    Paragraph("Добавьте ID пользователя в список <code>admin_users</code>", styles['Normal']),
    Paragraph("Сохраните файл и перезапустите бота", styles['Normal'])
]
for i, item in enumerate(add_admin_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Что делать, если пользователь не получает уведомления?", styles['Heading3']))
notifications_items = [
    Paragraph("Проверьте, что пользователь подписан на нужные категории (команда <code>/status</code>)", styles['Normal']),
    Paragraph("Убедитесь, что пользователь не заблокировал бота", styles['Normal']),
    Paragraph("Проверьте, что ID пользователя есть в списке разрешенных пользователей", styles['Normal'])
]
for i, item in enumerate(notifications_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Как перезапустить бота?", styles['Heading3']))
restart_items = [
    Paragraph("Остановите текущий процесс бота (Ctrl+C в терминале)", styles['Normal']),
    Paragraph("Запустите бота командой <code>python -m src.presentation.telegram</code>", styles['Normal'])
]
for i, item in enumerate(restart_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Paragraph("Как изменить настройки бота?", styles['Heading3']))
settings_items = [
    Paragraph("Откройте файл <code>config/telegram/bot.yaml</code>", styles['Normal']),
    Paragraph("Внесите необходимые изменения", styles['Normal']),
    Paragraph("Сохраните файл и перезапустите бота", styles['Normal'])
]
for i, item in enumerate(settings_items, 1):
    content.append(ListItem(item, leftIndent=20, value=f"{i}."))

content.append(Spacer(1, 20))
content.append(Paragraph("Если у вас возникли вопросы или проблемы при использовании административных функций бота, обратитесь к разработчику.", styles['Normal']))

# Создаем PDF
doc.build(content)

print(f"PDF-инструкция создана: {pdf_path}")
