import re
import sys

def replace_edit_message_text_calls(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Регулярное выражение для поиска вызовов PropertyBot.safe_edit_message_text с query.message.chat_id и query.message.message_id
    pattern = r'await\s+PropertyBot\.safe_edit_message_text\(\s*self\.application\.bot,\s*query\.message\.chat_id,\s*query\.message\.message_id,\s*([^,]+)(?:,\s*([^,\)]+))?\s*(?:,\s*([^,\)]+))?\s*\)'
    
    # Функция для замены найденных вызовов
    def replace_match(match):
        text = match.group(1).strip()
        reply_markup = match.group(2) if match.group(2) else 'None'
        parse_mode = match.group(3) if match.group(3) else 'None'
        
        if parse_mode == 'None':
            return f'await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, {text}, {reply_markup})'
        else:
            return f'await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, {text}, {reply_markup}, {parse_mode})'
    
    # Заменяем все найденные вызовы
    new_content = re.sub(pattern, replace_match, content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f'Файл {file_path} успешно обновлен (edit_message_text).')

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print('Использование: python replace_edit_message_text.py <путь_к_файлу>')
        sys.exit(1)
    
    file_path = sys.argv[1]
    replace_edit_message_text_calls(file_path)
