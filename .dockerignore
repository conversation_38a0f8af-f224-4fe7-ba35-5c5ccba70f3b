# Git
.git
.gitignore
.github

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Logs and data (будут монтироваться как тома)
logs/
data/

# Tests
tests/
.pytest_cache/
.coverage
htmlcov/

# Documentation
# Исключаем все документы, кроме необходимых для работы скриптов
docs/
!scripts/

# Misc
*.md
!README.md
LICENSE
.DS_Store
