# OLX Parser - Архитектура Упрощенного Парсера для OLX (Clean Architecture, Без AI)

## Содержание
1. [Ключевые компоненты (обязательно)](#1-ключевые-компоненты-обязательно)
    - [Диаграмма архитектуры (Упрощенная для OLX Парсера)](#диаграмма-архитектуры-упрощенная-для-olx-парсера)
2. [Слои архитектуры (обязательно)](#2-слои-архитектуры-обязательно)
    - [Структура проекта (Упрощенная для OLX Парсера)](#структура-проекта-упрощенная-для-olx-парсера)
3. [Обзор компонентов по слоям (обязательно)](#3-обзор-компонентов-по-слоям-обязательно)
    - [Domain Layer](#domain-layer)
    - [Application Layer](#application-layer)
    - [Infrastructure Layer](#infrastructure-layer)
    - [Presentation Layer](#presentation-layer)
4. [Архитектурные решения (обязательно)](#4-архитектурные-решения-обязательно)
5. [Масштабирование (Future Consideration)](#5-масштабирование-future-consideration)
6. [Системы мониторинга и метрик (Future Consideration)](#6-системы-мониторинга-и-метрик-future-consideration)
7. [Конфигурация и настройки (Упрощено)](#7-конфигурация-и-настройки-упрощено)
    - [7.1 Структура конфигурации](#71-структура-конфигурации)
    - [7.2 Управление конфигурацией](#72-управление-конфигурацией)
8. [Процессы развертывания (Future Consideration)](#8-процессы-развертывания-future-consideration)
9. [Безопасность (Упрощено)](#9-безопасность-упрощено)
    - [9.1 Аутентификация и авторизация](#91-аутентификация-и-авторизация)
    - [9.2 Защита данных](#92-защита-данных)
10. [Отказоустойчивость (Future Consideration)](#10-отказоустойчивость-future-consideration)
    - [10.1 Обработка ошибок](#101-обработка-ошибок)
    - [10.2 Резервное копирование](#102-резервное-копирование)
11. [Интеграционные точки (Упрощено)](#11-интеграционные-точки-упрощено)
    - [11.1 Внешние сервисы](#111-внешние-сервисы)
12. [Глоссарий](#12-глоссарий)
13. [Тестирование (Упрощено)](#13-тестирование-упрощено)
    - [13.1 Уровни тестирования](#131-уровни-тестирования)
    - [13.2 Тестовые данные](#132-тестовые-данные)
14. [Управление зависимостями](#14-управление-зависимостями)
    - [14.1 Python зависимости](#141-python-зависимости)
    - [14.2 Внешние зависимости](#142-внешние-зависимости)
15. [Обработка данных](#15-обработка-данных)
    - [15.1 Очистка данных](#151-очистка-данных)
16. [Производительность (Future Consideration)](#16-производительность-future-consideration)
17. [Disaster Recovery (Future Consideration)](#17-disaster-recovery-future-consideration)
18. [Навигация по проекту (Упрощено)](#18-навигация-по-проекту-упрощено)
    - [18.1 Основные точки входа](#181-основные-точки-входа)
    - [18.2 Чек-лист при разработке](#182-чек-лист-при-разработке)
    - [18.3 Быстрые ссылки](#183-быстрые-ссылки)
    - [18.4 Рекомендуемый порядок изучения](#184-рекомендуемый-порядок-изучения)
19. [Диаграммы зависимостей (Упрощено)](#19-диаграммы-зависимостей-упрощено)
    - [19.1 OLX Parser Dependencies](#191-olx-parser-dependencies)
    - [19.2 Основные потоки данных (Упрощено)](#192-основные-потоки-данных-упрощено)
    - [19.3 Ключевые зависимости (Упрощено)](#193-ключевые-зависимости-упрощено)
    - [19.4 Компоненты хранилища (Упрощено)](#194-компоненты-хранилища-упрощено)
20. [Ограничения системы (Future Consideration)](#20-ограничения-системы-future-consideration)
21. [Интеграционные контракты (Упрощено)](#21-интеграционные-контракты-упрощено)
    - [21.1 API Контракты (Внешние)](#211-api-контракты-внешние)
    - [21.2 Форматы данных (Внутренние)](#212-форматы-данных-внутренние)
22. [Метрики и KPI (Future Consideration)](#22-метрики-и-kpi-future-consideration)
23. [Управление состоянием (Упрощено)](#23-управление-состоянием-упрощено)
    - [23.1 Состояния парсинга](#231-состояния-парсинга)
    - [23.2 Кеширование (Future Consideration)](#232-кеширование-future-consideration)
24. [Версионирование (Future Consideration)](#24-версионирование-future-consideration)


## 1. Ключевые компоненты (обязательно)

### Диаграмма архитектуры (Упрощенная для OLX Парсера)
```mermaid
graph LR
    subgraph "Presentation Layer"
        direction LR
        CLI["CLI"]
        Scheduler["Scheduler"]
        TG["Telegram Bot (Уведомления)"]
    end

    subgraph "Application Layer"
        direction LR
        subgraph "Use Cases / Services"
            PropService["Property Service"]
            NotificationService["Notification Service"]
        end
        subgraph "Interfaces (Ports)"
            INotificationSender["INotificationSender"]
            IParser["IParser"]
            IProcessedAdStorage["IProcessedAdStorage"]
        end
    end

    subgraph "Infrastructure Layer"
        direction LR
        subgraph "External Services & DB"
            JsonStorage["JSON Storage"]
            TelegramAPI["Telegram API"]
            Zyte["Zyte API"]
            WebhookAPI["Webhook API"]
        end
        subgraph "Implementation (Adapters)"
            DbProcessedAdStorage["DbProcessedAdStorage"]
            TelegramSender["TelegramNotificationSender"]
            WebhookSender["WebhookNotificationSender"]
            OlxParser["OlxParser"]
            Formatters["Formatters"]
            Logging["Logging"]
        end
    end

    subgraph "Domain Layer"
        direction LR
        subgraph "Entities"
            %% Property["Property"] - Сущность используется в памяти, но не хранится целиком в БД
        end
        subgraph "Value Objects"
            Address["Address"]
            Price["Price"]
            Area["Area"]
            Contact["Contact"]
            AdId["AdId"]
            %% ... другие необходимые VO для обработки в памяти
        end
        subgraph "Domain Services"
            Validator["PropertyValidator"]
        end
     end

    %% Межслойные зависимости (Правило Clean Architecture)
    PresentationLayer --> ApplicationLayer
    ApplicationLayer --> DomainLayer
    InfrastructureLayer --> ApplicationLayer
    InfrastructureLayer --> DomainLayer

    %% Основные связи внутри слоев и между компонентами
    CLI --> PropService
    Scheduler --> PropService
    TG --> NotificationService

    PropService --> IProcessedAdStorage
    PropService --> INotificationSender
    PropService --> IParser
    PropService --> Validator
    PropService --> Logging

    NotificationService --> INotificationSender

    DbProcessedAdStorage --> IProcessedAdStorage
    DbProcessedAdStorage --> JsonStorage

    TelegramSender --> INotificationSender
    TelegramSender --> TelegramAPI

    WebhookSender --> INotificationSender
    WebhookSender --> WebhookAPI

    OlxParser --> IParser
    OlxParser --> Zyte
    OlxParser --> Formatters
    OlxParser --> Logging

    %% Связи для Value Objects (используются в памяти при обработке)
    %% Property --> Address
    %% Property --> Price
    %% ... другие связи VO

    %% Стили
    classDef presentation fill:#f9f,stroke:#333,stroke-width:2px;
    classDef application fill:#ffd,stroke:#333,stroke-width:2px;
    classDef domain fill:#dff,stroke:#333,stroke-width:2px;
    classDef infrastructure fill:#dfd,stroke:#333,stroke-width:2px;

    class CLI,Scheduler,TG presentation;
    class PropService,NotificationService application;
    class INotificationSender,IParser,IProcessedAdStorage application;
    class Address,Price,Area,Contact,AdId domain;
    class Validator domain;
    class JsonStorage,TelegramAPI,Zyte,WebhookAPI infrastructure;
    class DbProcessedAdStorage,TelegramSender,WebhookSender,OlxParser,Formatters,Logging infrastructure;
```

## 2. Слои архитектуры (обязательно)

### Структура проекта (Упрощенная для OLX Парсера)
```
olx_parser/
├── src/
│   ├── domain/                 # Бизнес-логика и модели (используемые в памяти)
│   │   ├── __init__.py        # Помечает директорию как пакет Python
│   │   ├── entities/          # Доменные модели (если нужны для обработки в памяти)
│   │   │   ├── __init__.py    # Помечает директорию как пакет Python
│   │   │   └── property.py    # Модель недвижимости (для обработки)
│   │   ├── interfaces/        # Интерфейсы доменных сервисов
│   │   │   ├── __init__.py    # Помечает директорию как пакет Python
│   │   │   └── localization_provider.py # Интерфейс провайдера локализации
│   │   ├── services/         # Доменные сервисы
│   │   │   ├── __init__.py    # Помечает директорию как пакет Python
│   │   │   └── property_validator.py  # Доменная валидация (бизнес-правила)
│   │   └── value_objects/    # Значимые объекты
│   │       ├── __init__.py    # Помечает директорию как пакет Python
│   │       ├── address.py    # Адрес объекта
│   │       ├── price.py      # Цена и валюта
│   │       ├── area.py       # Площадь помещения
│   │       ├── contact.py    # Контактная информация
│   │       ├── ad_id.py      # Идентификатор объявления
│   │       └── localization.py # Значимые объекты для локализации
│   │
│   ├── application/           # Прикладной слой
│   │   ├── __init__.py        # Помечает директорию как пакет Python
│   │   ├── services/         # Сервисы приложения
│   │   │   ├── __init__.py    # Помечает директорию как пакет Python
│   │   │   ├── property.py   # Работа с недвижимостью (оркестрация)
│   │   │   ├── notification.py# Уведомления
│   │   │   ├── localization.py # Сервис локализации
│   │   │   ├── localized_notification.py # Локализованные уведомления
│   │   │   └── user_language.py # Сервис управления языком пользователя
│   │   └── interfaces/       # Интерфейсы сервисов
│   │       ├── __init__.py    # Помечает директорию как пакет Python
│   │       ├── notification_service.py  # Интерфейс сервиса уведомлений
│   │       ├── notification_sender.py  # Интерфейс отправителя уведомлений
│   │       ├── parser.py             # Интерфейс парсера
│   │       └── processed_ad_storage.py # Интерфейс хранилища ID
│   │
│   ├── config/                # Конфигурация приложения (не путать с config/ вне src/)
│   │   ├── __init__.py        # Экспортирует объект settings для удобства импорта
│   │   ├── app_settings.py    # Централизованные настройки приложения (Singleton)
│   │   ├── constants.py       # Глобальные константы
│   │   ├── dotenv.py          # Загрузка переменных окружения из .env файла
│   │   ├── logging_config.py  # Конфигурация логирования и интеграция с Sentry
│   │   ├── paths.py           # Пути к директориям
│   │   ├── settings.py        # Низкоуровневые функции загрузки настроек из файлов
│   │   └── data/              # Конфигурационные данные
│   │       └── categories.json  # Конфигурация категорий для парсинга
│   ├── monitoring/            # Мониторинг и метрики
│   │   ├── __init__.py        # Помечает директорию как пакет Python
│   │   ├── metrics.py         # Определение метрик Prometheus
│   │   └── server.py          # Сервер метрик для экспорта в Prometheus
│   │
│   ├── infrastructure/        # Внешние сервисы и реализации
│   │   ├── parsers/          # Парсеры
│   │   │   └── olx/         # OLX парсер
│   │   │       ├── __init__.py
│   │   │       ├── olx_parser.py
│   │   │       ├── config/                # Конфигурация парсера OLX
│   │   │       │   ├── __init__.py
│   │   │       │   └── tag_mapping.py     # Маппинг тегов OLX на поля Property
│   │   │       └── utils/                 # Утилиты для OLX парсера
│   │   │           ├── __init__.py
│   │   │           ├── html_utils.py      # Функции для работы с HTML
│   │   │           ├── url_builder.py     # Построение URL для OLX
│   │   │           ├── phone_extractor.py # Извлечение телефонов
│   │   │           ├── json_ld_processor.py # Обработка JSON-LD OLX
│   │   │           ├── price_utils.py     # Обработка цен OLX
│   │   │           ├── area_utils.py      # Обработка площади OLX
│   │   │           ├── room_utils.py      # Обработка комнат OLX
│   │   │           ├── building_utils.py  # Информация о здании OLX
│   │   │           ├── location_utils.py  # Обработка локаций OLX
│   │   │           ├── id_extractor.py    # Извлечение ID OLX
│   │   │           ├── contact_utils.py   # Обработка контактов
│   │   │           ├── date_utils.py      # Обработка дат
│   │   │           ├── error_handling.py  # Специфичная обработка ошибок OLX
│   │   │           ├── tag_utils.py       # Обработка тегов
│   │   │           └── text_processors.py # Специфичная обработка текста OLX
│   │   ├── formatters/      # Форматирование данных
│   │   │   ├── base_formatter.py     # Базовый форматтер
│   │   │   ├── property_formatter.py  # Форматтер недвижимости
│   │   │   ├── value_formatter.py    # Форматтер значений
│   │   │   └── field_filter.py      # Фильтр полей со значением "не указан"
│   │   ├── localization/    # Реализация локализации
│   │   │   ├── __init__.py
│   │   │   ├── localization_provider.py # Реализация интерфейса локализации
│   │   │   └── yaml_localization_provider.py # Провайдер локализации на основе YAML
│   │   ├── storage/         # Работа с хранилищем (только для ID)
│   │   │   ├── __init__.py
│   │   │   ├── json_storage.py    # Единый файл для работы с JSON
│   │   │   └── models/
│   │   │       └── processed_ad.py # Простая модель для ID
│   │   ├── persistence/      # Реализация хранилищ
│   │   │   ├── __init__.py
│   │   │   ├── processed_ad_storage.py # Реализация IProcessedAdStorage
│   │   │   ├── user_language_storage.py # Хранилище языковых настроек пользователей
│   │   │   └── access_requests.py # Хранилище запросов на доступ
│   │   ├── logging/        # Логирование
│   │   │   ├── __init__.py
│   │   │   ├── logger.py         # Основной логгер
│   │   │   ├── formatters/       # Форматтеры логов (опционально)
│   │   │   │   └── custom.py
│   │   │   └── handlers/         # Обработчики логов
│   │   │       ├── file.py      # Файловый обработчик
│   │   │       └── console.py   # Консольный обработчик
│   │   └── external/       # Внешние API (опционально)
│   │       ├── telegram/    # Интеграция с Telegram API
│   │       │   └── notification_sender.py  # Реализация INotificationSender
│   │       ├── webhook/     # Интеграция с Webhook API
│   │       │   ├── __init__.py
│   │       │   └── notification_sender.py  # Реализация INotificationSender для вебхуков
│   │       └── zyte/       # Интеграция с Zyte API
│   │           ├── __init__.py
│   │           ├── api_client.py  # Клиент для работы с Zyte API
│   │           └── exceptions.py  # Исключения для работы с Zyte API
│   │
│   └── presentation/         # Интерфейсы пользователя
│       ├── cli/            # Командная строка
│       │   ├── __init__.py
│       │   ├── main.py            # Точка входа CLI
│       │   └── commands/          # Команды CLI
│       │       ├── __init__.py
│       │       ├── parse.py       # Команда парсинга
│       │       └── schedule.py    # Команда планирования
│       ├── telegram/       # Telegram бот (для уведомлений)
│       │   ├── __init__.py
│       │   ├── __main__.py      # Точка входа для запуска бота как модуля
│       │   ├── bot.py           # Инициализация бота (для отправки)
│       │   ├── handlers.py      # Обработчики команд
│       │   ├── main.py          # Точка входа для запуска бота
│       │   ├── messages/    # Шаблоны сообщений
│       │   │   ├── __init__.py
│       │   │   ├── templates.py     # Текстовые шаблоны на русском
│       │   │   ├── templates_ua.py  # Текстовые шаблоны на украинском
│       │   │   └── formatters.py    # Форматирование сообщений
│       │   ├── menu.py         # Функции для создания меню бота
│       │   ├── localization/   # Локализация бота
│       │   │   ├── __init__.py
│       │   │   ├── ru.py       # Русская локализация
│       │   │   └── ua.py       # Украинская локализация
│       │   └── services/    # Сервис отправки
│       │       ├── __init__.py
│       │       └── sender.py        # Сервис отправки сообщений
│       └── scheduler/         # Планировщик
│           ├── __init__.py
│           ├── main.py          # Точка входа для запуска планировщика
│           ├── core/               # Ядро планировщика
│           │   ├── __init__.py
│           │   ├── scheduler.py    # Основной класс планировщика
│           │   └── executor.py     # Исполнитель задач
│           ├── config/            # Конфигурация
│           │   ├── __init__.py
│           │   └── settings.py    # Настройки планировщика
│           └── tasks/            # Определения задач
│               ├── __init__.py
│               ├── base.py       # Базовый класс задачи
│               └── parser_task.py     # Задача парсинга
├── tests/                    # Тесты
│   ├── unit/                # Модульные тесты
│   ├── integration/         # Интеграционные тесты
│   └── performance/         # Тесты производительности
│       ├── test_parser_performance.py    # Тесты производительности парсера
│       ├── test_utils_performance.py     # Тесты производительности утилит
│       ├── profile_parser.py             # Скрипт профилирования парсера
│       └── profile_utils.py               # Скрипт профилирования утилит
├── scripts/                  # Скрипты
│   └── start_metrics_server.py  # Скрипт для запуска сервера метрик
├── config/                   # Конфигурации
│   ├── environments/        # Конфигурации для разных сред
│   │   ├── development.yaml # Настройки для разработки
│   │   ├── production.yaml  # Настройки для продакшена
│   │   └── testing.yaml     # Настройки для тестов
│   ├── localization/        # Файлы локализации
│   │   ├── ru.yaml          # Русская локализация
│   │   └── ua.yaml          # Украинская локализация
│   ├── parsers/            # Настройки парсеров
│   │   └── olx.yaml        # Конфигурация OLX парсера
│   ├── telegram/           # Настройки Telegram
│   │   └── bot.yaml        # Конфигурация бота
│   ├── webhook/            # Настройки вебхуков
│   │   └── notification.yaml # Конфигурация вебхука для уведомлений
│   ├── logging/            # Настройки логирования
│   │   └── default.yaml    # Базовая конфигурация
│   └── storage.yaml       # Настройки хранилища JSON
├── prometheus.yml           # Конфигурация Prometheus
└── docs/                     # Документация
```

## 3. Обзор компонентов по слоям (обязательно)

### Domain Layer

#### Entities (Property - в памяти)
- Доменная модель недвижимости (`Property`) используется для представления и обработки данных в памяти после парсинга. Она **не сохраняется целиком** в БД.
- Содержит бизнес-правила и валидацию, специфичную для домена.

#### Value Objects (Address, Price, Area, AdId, etc.)
- Неизменяемые объекты, представляющие конкретные значения или концепции (например, адрес, цена, ID объявления).
- Используются для создания объекта `Property` в памяти и обеспечивают валидность данных на этом этапе.
- `AdId` используется для идентификации объявлений.
- `Localization` содержит значимые объекты для локализации, такие как `Language` и `LocalizedText`.

#### Domain Services (PropertyValidator)
- Реализуют бизнес-логику, которая не принадлежит конкретной сущности.
- `PropertyValidator`: Проверяет соответствие объекта `Property` (в памяти) бизнес-правилам.

#### Interfaces (IParser, INotificationSender, IProcessedAdStorage, ILocalizationProvider)
- Определяют контракты для взаимодействия с внешними слоями.
- `IParser`: Контракт для парсинга данных с источника.
- `INotificationSender`: Контракт для отправки уведомлений.
- `IProcessedAdStorage`: Контракт для проверки и сохранения ID обработанных объявлений.
- `ILocalizationProvider`: Контракт для получения локализованных строк.

### Application Layer

#### PropertyService
- Оркестрирует основной use case:
    - Получает ID объявлений от парсера (`IParser`).
    - Проверяет, был ли ID уже обработан (`IProcessedAdStorage`).
    - Если ID новый:
        - Получает полные данные от парсера.
        - Создает объект `Property` в памяти (с использованием Value Objects).
        - Валидирует `Property` (`PropertyValidator`).
        - Инициирует уведомление (`INotificationSender`).
        - Сохраняет ID как обработанный (`IProcessedAdStorage`).
- Содержит логику приложения, не относящуюся напрямую к домену.

#### NotificationService
- Отвечает за подготовку и инициирование отправки уведомлений через `INotificationSender` на основе данных `Property` (в памяти).

#### LocalizationService
- Отвечает за получение локализованных строк через `ILocalizationProvider`.
- Предоставляет методы для получения локализованных строк на основе языка пользователя.

#### LocalizedNotificationService
- Расширяет `NotificationService` для отправки локализованных уведомлений.
- Использует `LocalizationService` для получения локализованных шаблонов.
- Поддерживает отправку уведомлений на разных языках в зависимости от предпочтений пользователя.

#### UserLanguageService
- Отвечает за управление языковыми настройками пользователей.
- Предоставляет методы для получения и установки языка пользователя.
- Использует `UserLanguageStorage` для хранения языковых настроек.

### Infrastructure Layer

#### OlxParser
- Реализация интерфейса `IParser` для сайта OLX.
- Взаимодействует с HTML OLX через Zyte API.
- Использует утилиты внутри `parsers/olx/utils` и `Formatters` для извлечения, обработки и, возможно, первичной проверки формата сырых данных. Основная валидация происходит в Domain Layer.
- Возвращает либо список ID, либо полные данные для конкретного ID.

#### Zyte API Client
- Реализация клиента для работы с Zyte API.
- Реализует паттерн Singleton для предотвращения создания множества экземпляров.
- Поддерживает как синхронные, так и асинхронные запросы.
- Реализует расширенную обработку ошибок с экспоненциальной задержкой между повторными попытками.
- Поддерживает различные методы для работы с Zyte API, включая получение HTML, скриншотов, извлечение данных, извлечение JSON-LD и выполнение JavaScript-действий.
- Собирает метрики по времени выполнения запросов и размеру полученных данных.
- Использует специализированные исключения из `exceptions.py` для детальной обработки различных типов ошибок.

#### Zyte API Exceptions
- Реализация иерархии исключений для работы с Zyte API.
- Базовый класс `ZyteAPIError` и специализированные исключения для различных типов ошибок.
- Включает исключения для ошибок аутентификации, превышения лимита запросов, серверных и клиентских ошибок, таймаутов и сетевых ошибок.
- Сохраняет детальную информацию об ошибке, включая HTTP-код, ответ API и URL запроса.

#### DbProcessedAdStorage
- Реализация `IProcessedAdStorage` с использованием JSON файла.
- Взаимодействует с JSON файлом для добавления и проверки существования `AdId`.
- Использует `models/processed_ad.py` для структуры данных.

#### TelegramNotificationSender
- Реализация `INotificationSender` для отправки сообщений через Telegram API.
- Находится в `src/infrastructure/external/telegram/notification_sender.py`.
- Отвечает за низкоуровневую отправку сообщений через Telegram API.
- Используется в различных компонентах системы, которым требуется отправка уведомлений.

#### WebhookNotificationSender
- Реализация `INotificationSender` для отправки сообщений через вебхуки.
- Находится в `src/infrastructure/external/webhook/notification_sender.py`.
- Отвечает за отправку HTTP-запросов на указанный URL вебхука.
- Поддерживает отправку текстовых сообщений и сообщений с изображениями.
- Используется для интеграции с внешними системами через HTTP-вебхуки.

#### Logging
- Настройка и управление логированием событий и ошибок во всей системе.
- Реализовано в `src/config/logging_config.py` с использованием стандартного модуля `logging` и дополнительных библиотек `python-json-logger` и `sentry-sdk`.
- Поддерживает структурированное логирование в JSON формате, ротацию логов и отправку ошибок в Sentry.

#### Formatters
- Преобразование данных, полученных от парсера, в стандартизированный формат перед передачей в Application Layer или созданием доменных объектов в памяти.
- Состоит из четырех основных компонентов:
  - `BaseFormatter`: Абстрактный базовый класс для всех форматтеров.
  - `PropertyFormatter`: Форматтер для объектов недвижимости, поддерживающий различные форматы вывода (полный, краткий, список) и использующий ValueFormatter для форматирования отдельных значений.
  - `ValueFormatter`: Форматтер для отдельных значений (цена, площадь, дата, этаж и т.д.).
  - `FieldFilter`: Фильтр полей со значением "не указан" и его склонениями, позволяющий скрывать такие поля в уведомлениях с возможностью управления через команды Telegram-бота.
- Используется как в инфраструктурном слое для подготовки данных, так и в презентационном слое для форматирования сообщений.

#### Localization
- Реализация системы локализации для поддержки многоязычности в приложении.
- Состоит из следующих компонентов:
  - `LocalizationProvider`: Реализация интерфейса `ILocalizationProvider` для получения локализованных строк.
  - `YamlLocalizationProvider`: Провайдер локализации, использующий YAML-файлы для хранения локализованных строк.
- Поддерживает несколько языков (русский, украинский) с возможностью легкого добавления новых.
- Используется в различных компонентах системы для локализации интерфейса и уведомлений.

#### Monitoring
- Система мониторинга и сбора метрик для отслеживания работы парсера.
- Реализована с использованием библиотеки `prometheus_client` для сбора и экспорта метрик.
- Состоит из следующих компонентов:
  - `metrics.py`: Определение метрик Prometheus (счетчики, гистограммы, измерители, сводки).
  - `server.py`: Сервер метрик для экспорта в Prometheus.
- Используется в различных компонентах системы для отслеживания производительности и ошибок.

#### Database (`database/`)
- Содержит простую реализацию хранения ID объявлений:
    - `storage.py`: Единый файл для работы с БД, включающий:
        - ORM-модель `ProcessedAd` для таблицы `processed_ads`
        - Класс `Storage` с методами `add` и `exists`
        - Автоматическое создание таблицы при старте

### Presentation Layer

#### CLI
- Интерфейс командной строки для запуска парсинга и других административных задач.

#### Scheduler
- Запускает задачи парсинга по расписанию.
- Поддерживает оптимизацию парсинга через настройку `parse_only_subscribed_categories`, которая позволяет запускать парсинг только для категорий, на которые подписаны пользователи в Telegram боте.
- Реализует логику проверки подписок пользователей на категории и фильтрации категорий для парсинга.
- Использует `UserLanguageStorage` для получения информации о подписках пользователей.
- Параметр `max_concurrent_tasks` в конфигурации шедулера определяет максимальное количество одновременно выполняемых задач парсинга. При использовании `parse_only_subscribed_categories: true` рекомендуется устанавливать значение `max_concurrent_tasks` не менее 3-5, чтобы обеспечить параллельное выполнение задач для разных категорий. Если установлено значение `max_concurrent_tasks: 1`, то задачи будут выполняться последовательно, что может привести к пропуску или задержке обработки некоторых категорий.

#### Telegram Bot (Уведомления и управление)
- Компоненты презентационного слоя для работы с Telegram.
- Состоит из следующих компонентов:
  - `bot.py`: Класс `PropertyBot` для инициализации бота с токеном из конфигурации и обработки команд. Реализует управление пользователями, запросами на доступ и категориями.
  - `messages/templates.py`: Шаблоны сообщений на русском языке для разных типов недвижимости, команд и уведомлений.
  - `messages/templates_ua.py`: Шаблоны сообщений на украинском языке для разных типов недвижимости, команд и уведомлений.
  - `messages/formatters.py`: Функции форматирования сообщений, использующие классы PropertyFormatter и ValueFormatter из инфраструктурного слоя.
  - `menu.py`: Функции для создания различных меню бота (администратора, категорий, пользователей и т.д.).
  - `handlers.py`: Функции для обработки различных команд бота (/admin, /users, /category и т.д.).
  - `services/sender.py`: Класс `TelegramSender` для высокоуровневой отправки сообщений в Telegram.
  - `localization/ru.py` и `localization/ua.py`: Файлы локализации интерфейса бота на русском и украинском языках.
- Основные функции бота:
  - Управление пользователями (добавление, удаление, просмотр списка)
  - Управление администраторами (добавление, удаление через суперадминистратора)
  - Управление категориями (подписка, отписка, просмотр активных категорий)
  - Обработка запросов на доступ (одобрение, отклонение)
  - Отправка уведомлений о новых объявлениях
  - Фильтрация полей со значением "не указан"
  - Выбор языка интерфейса (русский, украинский)
- Важно отметить разницу между `TelegramSender` (презентационный слой) и `TelegramNotificationSender` (инфраструктурный слой):
  - `TelegramSender` - высокоуровневый компонент, который использует `PropertyBot` для взаимодействия с пользователем.
  - `TelegramNotificationSender` - низкоуровневый компонент, который реализует интерфейс `INotificationSender` и отвечает только за отправку сообщений через Telegram API.

## 4. Архитектурные решения (обязательно)

1. SOLID
2. Clean Architecture
3. DDD (Domain-Driven Design) - Применяется к объектам, обрабатываемым в памяти.
4. Repository Pattern - Применяется к `IProcessedAdStorage`.
5. Factory Pattern для создания объектов
6. Observer Pattern для уведомлений
7. Decorator Pattern для кеширования (Future Consideration)

(Подробнее об обзоре компонентов см. раздел 3)

## 5. Масштабирование (Future Consideration)

*На данном этапе основное внимание уделяется базовой функциональности. Вопросы масштабирования будут рассмотрены позже.*

1. Горизонтальное масштабирование парсеров (потенциально через очереди).
2. Оптимизация запросов к БД (для проверки ID).

## 6. Системы мониторинга и метрик

В проекте реализована система мониторинга и логирования, которая позволяет отслеживать работу парсера и собирать метрики производительности.

### 6.1 Логирование

Система логирования построена на основе стандартного модуля `logging` с дополнительными возможностями:

- **Структурированное логирование в JSON** - используется библиотека `python-json-logger` для форматирования логов в JSON формате.
- **Ротация логов** - используется `RotatingFileHandler` для ограничения размера лог-файлов.
- **Интеграция с Sentry** - для отслеживания и анализа ошибок в продакшене.

Основные компоненты системы логирования:

- `src/config/logging_config.py` - конфигурация логирования и интеграция с Sentry.

### 6.2 Метрики

Система метрик построена на основе Prometheus и позволяет отслеживать различные аспекты работы парсера:

- **Счетчики (Counters)** - отслеживают количество спарсенных URL, ошибок, обработанных объектов и отправленных уведомлений.
- **Гистограммы (Histograms)** - измеряют время парсинга и отправки уведомлений.
- **Измерители (Gauges)** - отслеживают количество активных парсеров и размер очереди парсинга.
- **Сводки (Summaries)** - собирают статистику по ценам недвижимости.

Основные компоненты системы метрик:

- `src/monitoring/metrics.py` - определение метрик Prometheus.
- `src/monitoring/server.py` - сервер метрик для экспорта в Prometheus.
- `scripts/start_metrics_server.py` - скрипт для запуска сервера метрик.
- `prometheus.yml` - конфигурация Prometheus.

### 6.3 Визуализация метрик

Для визуализации метрик используется Grafana, которая позволяет создавать дашборды с различными метриками и графиками.

## 7. Конфигурация и настройки (Упрощено)

### 7.1 Структура конфигурации
- Environments (development.yaml, production.yaml)
- Parsers (olx.yaml)
- Telegram (bot.yaml) - настройки бота, включая super_admin_id, admin_users, allowed_users
- Webhook (notification.yaml) - настройки вебхука для отправки уведомлений
- Logging (default.yaml)
- Storage (storage.yaml)
- Scheduler (scheduler.yaml) - настройки шедулера, включая расписание, категории для парсинга и оптимизацию парсинга

### 7.2 Управление конфигурацией

#### 7.2.1 Структура системы настроек
Система настроек состоит из следующих компонентов:

- **settings.py** - низкоуровневый модуль, который отвечает за загрузку YAML-файлов конфигурации, преобразование данных из файлов в словари Python и объединение настроек из разных источников.

- **app_settings.py** - высокоуровневый модуль, который использует функции из `settings.py` для загрузки конфигурации, предоставляет удобный объектно-ориентированный интерфейс для доступа к настройкам и реализует паттерн Singleton для обеспечения единой точки доступа к настройкам.

- **__init__.py** - модуль, который экспортирует объект `settings` для удобства импорта.

#### 7.2.2 Использование системы настроек
Для использования системы настроек в коде проекта достаточно импортировать объект `settings` из пакета `src.config`:

```python
from src.config import settings

# Получение настроек хранилища
storage_file_path = settings.get_storage_file_path()

# Получение настроек Telegram
telegram_token = settings.get_telegram_token()
telegram_chat_ids = settings.get_telegram_chat_ids()
```

#### 7.2.3 Разделение настроек

В проекте используется следующий подход к разделению настроек:

1. **Чувствительные данные** (токены, пароли, API ключи) хранятся в файле `.env`, который не должен попадать в систему контроля версий:
   - `TELEGRAM_BOT_TOKEN` - токен Telegram бота
   - `TELEGRAM_CHAT_IDS` - ID чатов Telegram
   - `TELEGRAM_SUPER_ADMIN_ID` - ID суперадминистратора Telegram бота
   - `TELEGRAM_ADMIN_USERS` - ID администраторов Telegram бота
   - `TELEGRAM_ALLOWED_USERS` - ID разрешенных пользователей Telegram бота
   - `TELEGRAM_ERROR_CHAT_ID` - ID чата для отправки уведомлений об ошибках
   - `WEBHOOK_NOTIFICATION_URL` - URL вебхука для отправки уведомлений
   - `ZYTE_API_KEY` - ключ API Zyte
   - `OLX_LOGIN` - логин для OLX
   - `OLX_PASSWORD` - пароль для OLX

2. **Обычные настройки** хранятся в YAML-файлах в директории `config/`:
   - Настройки хранилища (`config/storage.yaml`)
   - Настройки логирования (`config/logging/default.yaml`)
   - Настройки парсера OLX (`config/parsers/olx.yaml`)
   - Настройки Telegram бота (`config/telegram/bot.yaml`)
   - Настройки вебхука для уведомлений (`config/webhook/notification.yaml`)
   - Настройки окружения (`config/environments/*.yaml`)
   - Файлы локализации (`config/localization/*.yaml`)

Такое разделение обеспечивает безопасность чувствительных данных и удобство управления обычными настройками.

#### 7.2.4 Локализация

Система локализации использует YAML-файлы в директории `config/localization/`:
- `ru.yaml` - локализация на русском языке
- `ua.yaml` - локализация на украинском языке

Каждый файл локализации содержит следующие секции:
- `texts` - общие текстовые строки
- `templates` - шаблоны сообщений
- `buttons` - тексты кнопок
- `menu` - тексты меню
- `errors` - сообщения об ошибках

Для добавления новой локализации достаточно создать новый YAML-файл с соответствующим именем и структурой.

## 8. Процессы развертывания (Future Consideration)

*Начальная стадия предполагает ручной запуск или запуск через системные средства (cron). CI/CD будет настроен позже.*

- **Окружения:** Development, Production.
- **Развертывание:** Ручное или с использованием Docker.

## 9. Безопасность (Упрощено)

### 9.1 Аутентификация и авторизация
- **Внешние сервисы:** Использование API ключей (Telegram, Zyte), хранимых в переменных окружения или защищенной конфигурации.
- **Внутренняя:** На данном этапе не требуется сложная аутентификация/авторизация пользователей.

### 9.2 Защита данных
- **Секреты:** Хранение API ключей вне репозитория (переменные окружения, `.env` файл).
- **Валидация:** Валидация данных, получаемых от парсера (в утилитах и домене) и перед отправкой во внешние сервисы.
- **Rate Limiting:** Учет ограничений внешних API (Telegram, Zyte).

## 10. Отказоустойчивость (Future Consideration)

*Базовая обработка ошибок будет реализована. Сложные механизмы (retry, circuit breaker) будут добавлены по мере необходимости.*

- **Обработка ошибок:** Логирование ошибок парсинга, отправки уведомлений. Базовые `try-except` блоки.
- **Повторные попытки:** Возможно, простые повторные попытки для сетевых запросов.
- **Резервное копирование:** Настройка резервного копирования JSON файла.

## 11. Интеграционные точки (Упрощено)

### 11.1 Внешние сервисы
- Zyte API (для доступа к OLX)
- Telegram API
- Webhook API (для отправки уведомлений на внешние системы)
- JSON файл (для хранения ID)

## 12. Глоссарий

- **Property:** Доменная сущность (в памяти), представляющая объект недвижимости.
- **Ad/Listing:** Объявление на OLX.
- **Parser:** Компонент (`OlxParser`), извлекающий данные с OLX.
- **Validator:** Компонент (`PropertyValidator`), проверяющий бизнес-правила `Property`.
- **Notification:** Уведомление, отправляемое в Telegram.
- **ProcessedAdStorage:** Компонент, отвечающий за хранение и проверку ID обработанных объявлений.

## 13. Тестирование (Упрощено)

### 13.1 Уровни тестирования

#### Модульные тесты
- Тестируют отдельные компоненты системы в изоляции.
- Расположены в `tests/unit/`.
- Используют pytest и unittest.mock для изоляции зависимостей.
- Включают тесты для доменной логики, сервисов, утилит парсера, хранилища ID и форматтеров.

#### Интеграционные тесты
- Тестируют взаимодействие между различными компонентами системы.
- Расположены в `tests/integration/`.
- Используют реальные зависимости или их реалистичные замены.
- Включают тесты для проверки взаимодействия парсера с сохраненными HTML страницами, взаимодействия хранилища ID с БД (in-memory или тестовая БД).

#### Тесты производительности
- Тестируют производительность и скорость работы ключевых компонентов системы.
- Расположены в `tests/performance/`.
- Используют pytest-benchmark для измерения времени выполнения функций.
- Включают тесты для парсера, хранилища и сервиса уведомлений.
- Дополнительно включают сценарий нагрузочного тестирования с использованием Locust.

### 13.2 Тестовые данные
- Фикстуры с примерами HTML страниц OLX.
- Моки для внешних API (Telegram, Zyte).
- Тестовые данные для профилирования и тестов производительности.
- Тесты для `ZyteApiClient` с использованием моков для проверки синхронных и асинхронных методов.

### 13.3 Инструменты тестирования
- **pytest** - основной фреймворк для модульных и интеграционных тестов.
- **pytest-cov** - анализ покрытия кода тестами.
- **pytest-benchmark** - тестирование производительности.
- **locust** - нагрузочное тестирование.

## 14. Управление зависимостями

### 14.1 Python зависимости
- Использование `requirements.txt` или `poetry` для управления зависимостями.
- Фиксация версий для воспроизводимости.

### 14.2 Внешние зависимости
- Telegram API
- Webhook API
  - Используется для отправки уведомлений на внешние системы через HTTP-запросы
  - Реализован через класс `WebhookNotificationSender` с поддержкой отправки текстовых сообщений и сообщений с изображениями
- Zyte API
  - Используется для получения HTML страниц, скриншотов, извлечения данных и выполнения JavaScript-действий
  - Реализован через класс `ZyteApiClient` с поддержкой синхронных и асинхронных запросов

## 15. Обработка данных

### 15.1 Очистка данных
- Выполняется в `OlxParser` и `Formatters`.
- Нормализация текста, извлечение структурированных данных (цена, площадь и т.д.).

## 16. Производительность

*Начальная оптимизация заключается в эффективной работе парсера и запросов к БД (для проверки ID). Более сложные техники будут применяться при необходимости.*

- **Оптимизации:**
  - Эффективные запросы к БД для проверки ID, оптимизация логики парсера.
  - Оптимизация парсинга категорий через настройку `parse_only_subscribed_categories`, которая позволяет запускать парсинг только для категорий, на которые подписаны пользователи в Telegram боте, что значительно снижает количество запросов к OLX и нагрузку на сервер.
  - **ВАЖНО:** При использовании `parse_only_subscribed_categories: true` необходимо установить параметр `max_concurrent_tasks` в значение, соответствующее количеству категорий, на которые могут быть подписаны пользователи (рекомендуется 3-5 или больше в зависимости от количества категорий для парсинга). Это позволит шедулеру запускать задачи парсинга для разных категорий параллельно, без блокировок и задержек. Если `max_concurrent_tasks: 1`, то задачи будут выполняться последовательно, и некоторые категории могут не обрабатываться вовремя или пропускаться, если предыдущие задачи занимают много времени.
- **Лимиты:** Учет rate limits внешних API (Telegram, Zyte).
- **Асинхронность:** Реализация асинхронных запросов в `ZyteApiClient` для параллельной обработки запросов.
- **Метрики:** Сбор метрик по времени выполнения запросов и размеру полученных данных в `ZyteApiClient`.
- **Повторные попытки:** Реализация экспоненциальной задержки между повторными попытками в `ZyteApiClient`.

## 17. Disaster Recovery (Future Consideration)

*Основной фокус - на резервном копировании БД (с ID).*

- **Планы восстановления:** Восстановление БД из бэкапа.

## 18. Навигация по проекту

### 18.1 Основные точки входа
1. **Парсинг:**
   - Запуск: `src/presentation/cli/commands/parse.py` или `src/presentation/scheduler/tasks/parser_task.py`.
   - Логика: `src/application/services/property.py` -> `src/infrastructure/parsers/olx/olx_parser.py` и `src/infrastructure/persistence/processed_ad_storage.py`.
2. **Уведомления:**
   - Логика: `src/application/services/notification.py` -> `src/infrastructure/telegram/services/sender.py`.

### 18.2 Чек-лист при разработке
- Следовать принципам Clean Architecture.
- Писать Unit/Интеграционные тесты.
- Обновлять документацию при необходимости.
- Проверять логику парсера на реальных/сохраненных страницах OLX.

### 18.3 Быстрые ссылки
- **Код:** `src/`
- **Тесты:** `tests/`
- **Конфиги:** `config/`
- **Документация:** `docs/`

### 18.4 Рекомендуемый порядок изучения
1. `docs/architecture/clean_architecture.md` (этот файл).
2. `src/domain/value_objects/ad_id.py` и `entities/property.py` (модели данных).
3. `src/application/services/property.py` (основной use case).
4. `src/infrastructure/parsers/olx/olx_parser.py` (логика парсинга).
5. `src/infrastructure/persistence/processed_ad_storage.py` (хранение ID).
6. `src/infrastructure/telegram/services/sender.py` (отправка уведомлений).

## 19. Диаграммы зависимостей (Упрощено)

### 19.1 OLX Parser Dependencies
*(Эта диаграмма остается актуальной, так как описывает внутреннюю структуру OLX парсера)*
```mermaid
graph LR
    subgraph "Infrastructure Layer - OLX Parser"
        subgraph "Core"
            OlxParser["olx_parser.py"]
            ListingParser["listing_parser.py"]
            DetailParser["detail_parser.py"]
        end

        subgraph "Utils"
            HTMLProc["html_processor.py"]
            JSONLDParser["json_ld_processor.py"]
            UrlBuilder["url_builder.py"]
            PhoneExtractor["phone_extractor.py"]
            IdExtractor["id_extractor.py"]
            TextProc["text_processors.py"]
            ErrorHandling["error_handling.py"]
            %% ... другие утилиты (price, area, date, etc.)
        end
    end

    %% Зависимости
    OlxParser --> ListingParser
    OlxParser --> DetailParser

    ListingParser --> HTMLProc
    DetailParser --> HTMLProc
    ListingParser --> JSONLDParser
    DetailParser --> JSONLDParser
    ListingParser --> IdExtractor
    DetailParser --> PhoneExtractor
    ListingParser --> TextProc
    DetailParser --> TextProc
    %% ... другие связи утилит

    OlxParser --> UrlBuilder
    OlxParser --> ErrorHandling

    %% Стили
    classDef core fill:#fbb,stroke:#333,stroke-width:2px;
    classDef utils fill:#bfb,stroke:#333,stroke-width:2px;

    class OlxParser,ListingParser,DetailParser core;
    class HTMLProc,JSONLDParser,UrlBuilder,PhoneExtractor,IdExtractor,TextProc,ErrorHandling utils;
```

### 19.2 Основные потоки данных (Упрощено)
1.  **Планирование задач парсинга:** `Scheduler` -> `UserLanguageStorage` -> проверка подписок -> фильтрация категорий -> планирование задач
2.  **Парсинг (получение ID):** `Scheduler/CLI` -> `PropertyService` -> `OlxParser` (режим списка) -> `AdId`
3.  **Проверка ID:** `PropertyService` -> `IProcessedAdStorage` -> `DbProcessedAdStorage` -> `JSON файл`
4.  **Парсинг (полные данные):** `PropertyService` -> `OlxParser` (режим деталей) -> `Formatters` -> `Property` (в памяти)
5.  **Форматирование сообщений:** `Property` (в памяти) -> `formatters.py` -> `PropertyFormatter/ValueFormatter` -> форматированный текст
6.  **Уведомление:** `PropertyService/NotificationService` -> `TelegramNotificationSender` -> `Telegram API`
7.  **Сохранение ID:** `PropertyService` -> `IProcessedAdStorage` -> `DbProcessedAdStorage` -> `JSON файл`

### 19.3 Ключевые зависимости (Упрощено)
- `OlxParser` зависит от утилит парсинга (`utils/`) и внешнего вида OLX (через Zyte).
- `api_client.py` (в `src/infrastructure/external/zyte/`) отвечает за взаимодействие с Zyte API для получения данных с сайта OLX.
- `PropertyService` зависит от интерфейсов `IParser`, `IProcessedAdStorage`, `INotificationSender`.
- `DbProcessedAdStorage` зависит от JSON файла (Infrastructure).
- `TelegramNotificationSender` (в `src/infrastructure/external/telegram/`) зависит от `Telegram API` и реализует интерфейс `INotificationSender`.
- `WebhookNotificationSender` (в `src/infrastructure/external/webhook/`) зависит от HTTP-клиента для отправки запросов на вебхук и реализует интерфейс `INotificationSender`.
- `NotificationService` может использовать как `TelegramNotificationSender`, так и `WebhookNotificationSender` для отправки уведомлений через разные каналы.
- `LocalizedNotificationService` зависит от `NotificationService` и `LocalizationService` для отправки локализованных уведомлений.
- `LocalizationService` зависит от `ILocalizationProvider` для получения локализованных строк.
- `YamlLocalizationProvider` зависит от YAML-файлов локализации в директории `config/localization/`.
- `UserLanguageService` зависит от `UserLanguageStorage` для хранения языковых настроек пользователей.
- `TelegramSender` (в `src/presentation/telegram/services/`) зависит от `PropertyBot` и отвечает за высокоуровневую логику уведомлений.
- `formatters.py` в презентационном слое зависит от `PropertyFormatter` и `ValueFormatter` из инфраструктурного слоя.
- `PropertyFormatter` зависит от `ValueFormatter` для форматирования отдельных значений.
- `Scheduler` зависит от `UserLanguageStorage` для получения информации о подписках пользователей на категории при использовании оптимизации парсинга.

### 19.4 Компоненты хранилища (Упрощено)
- **Основные компоненты:** `DbProcessedAdStorage`, `models/processed_ad.py`, `json_storage.py`.
- **Ключевые операции:** Добавление и проверка существования `AdId`.
- **Конфигурация хранилища:** JSON файл с путем, указанным в конфигурации.
- **Хранение данных:** Хранятся только ID обработанных объявлений в JSON файле.

## 20. Ограничения системы (Future Consideration)

*Необходимо учитывать ограничения внешних API и потенциальные ограничения парсинга.*

- **Технические лимиты:** Rate limits Telegram, Zyte. Тайм-ауты запросов.
- **Бизнес-ограничения:** Частота парсинга (чтобы не забанили).

## 21. Интеграционные контракты (Упрощено)

### 21.1 API Контракты (Внешние)
- **Telegram API:** Следовать документации Telegram Bot API для отправки сообщений.
- **Webhook API:** Отправлять HTTP POST-запросы с JSON-данными на указанный URL вебхука.
- **Zyte API:** Следовать документации Zyte API.

### 21.2 Форматы данных (Внутренние)
- **Parser -> Application:** Список `AdId` или данные для создания `Property` в памяти.
- **Application -> Notification:** Текст и метаданные для сообщения Telegram или вебхука.
- **Application -> ProcessedAdStorage:** `AdId`.
- **Notification -> Webhook:** JSON-данные с полями type, message, chat_id, parse_mode, image_url и другими метаданными.
- **LocalizationProvider -> LocalizationService:** Локализованные строки в виде словаря с ключами и значениями.
- **UserLanguageService -> UserLanguageStorage:** Данные о языковых настройках пользователей в виде словаря с ID пользователя и кодом языка.

## 22. Метрики и KPI (Future Consideration)

*На данном этапе отслеживаются через логирование.*

- **Бизнес-метрики:** Количество спарсенных объявлений, количество отправленных уведомлений.
- **Технические метрики:** Время выполнения парсинга, количество ошибок.

## 23. Управление состоянием (Упрощено)

### 23.1 Состояния парсинга
- Основное состояние - наличие ID в `processed_ads`.
- Идемпотентность обеспечивается проверкой ID перед полной обработкой.





