# Рекомендации по написанию тестов

В этом документе приведены рекомендации по написанию тестов для проекта Parser OLX.

## Общие принципы

1. **Следуйте принципу AAA (Arrange-Act-Assert)**:
   - **Arrange**: подготовка данных и объектов для теста
   - **Act**: выполнение тестируемого действия
   - **Assert**: проверка результатов

   ```python
   def test_example():
       # Arrange
       calculator = Calculator()
       a = 2
       b = 3
       
       # Act
       result = calculator.add(a, b)
       
       # Assert
       assert result == 5
   ```

2. **Изолируйте тесты** - каждый тест должен быть независимым от других тестов и от внешних зависимостей.

3. **Один тест - одна проверка** - каждый тест должен проверять только одну функциональность или один аспект функциональности.

4. **Используйте говорящие имена тестов** - имя теста должно описывать, что тестируется и какой результат ожидается.

   ```python
   def test_add_two_positive_numbers_returns_correct_sum():
       # ...
   ```

5. **Тестируйте как позитивные, так и негативные сценарии** - проверяйте не только корректное поведение, но и обработку ошибок.

## Структура тестов

1. **Группируйте тесты по классам** - тесты для одного класса или модуля должны быть сгруппированы в одном тестовом классе.

   ```python
   class TestCalculator:
       def test_add(self):
           # ...
       
       def test_subtract(self):
           # ...
   ```

2. **Используйте фикстуры pytest** для повторного использования кода настройки тестов.

   ```python
   class TestCalculator:
       @pytest.fixture
       def calculator(self):
           return Calculator()
       
       def test_add(self, calculator):
           # ...
       
       def test_subtract(self, calculator):
           # ...
   ```

3. **Используйте параметризованные тесты** для тестирования различных входных данных.

   ```python
   @pytest.mark.parametrize("a,b,expected", [
       (1, 2, 3),
       (0, 0, 0),
       (-1, 1, 0),
   ])
   def test_add(self, calculator, a, b, expected):
       result = calculator.add(a, b)
       assert result == expected
   ```

## Моки и стабы

1. **Используйте моки для имитации зависимостей** - это позволяет изолировать тестируемый код от внешних зависимостей.

   ```python
   def test_process_data(self):
       # Arrange
       mock_repository = MagicMock()
       mock_repository.get_data.return_value = [1, 2, 3]
       service = DataService(repository=mock_repository)
       
       # Act
       result = service.process_data()
       
       # Assert
       assert result == 6
       mock_repository.get_data.assert_called_once()
   ```

2. **Используйте патчи для замены функций и методов** - это позволяет изолировать тестируемый код от внешних функций и методов.

   ```python
   @patch('module.function')
   def test_function_that_calls_another_function(self, mock_function):
       # Arrange
       mock_function.return_value = 42
       
       # Act
       result = function_that_calls_another_function()
       
       # Assert
       assert result == 42
       mock_function.assert_called_once()
   ```

3. **Используйте контекстные менеджеры для временного патчинга** - это позволяет патчить функции и методы только в рамках определенного блока кода.

   ```python
   def test_function_that_calls_another_function(self):
       # Arrange
       with patch('module.function') as mock_function:
           mock_function.return_value = 42
           
           # Act
           result = function_that_calls_another_function()
           
           # Assert
           assert result == 42
           mock_function.assert_called_once()
   ```

## Тестирование различных компонентов

### Тестирование Value Objects

1. **Тестируйте создание объекта с валидными данными**.
2. **Тестируйте создание объекта с невалидными данными**.
3. **Тестируйте сравнение объектов**.
4. **Тестируйте строковое представление объекта**.

### Тестирование Domain Services

1. **Тестируйте бизнес-логику сервиса**.
2. **Тестируйте валидацию данных**.
3. **Тестируйте обработку ошибок**.

### Тестирование Application Services

1. **Тестируйте взаимодействие с другими сервисами и репозиториями**.
2. **Тестируйте обработку ошибок**.
3. **Тестируйте транзакционность операций**.

### Тестирование Infrastructure

1. **Тестируйте взаимодействие с внешними системами**.
2. **Тестируйте сохранение и загрузку данных**.
3. **Тестируйте обработку ошибок при взаимодействии с внешними системами**.

### Тестирование Presentation

1. **Тестируйте обработку команд и запросов**.
2. **Тестируйте форматирование данных для отображения**.
3. **Тестируйте обработку ошибок и отображение сообщений об ошибках**.

## Интеграционные тесты

1. **Тестируйте взаимодействие между компонентами системы**.
2. **Используйте реальные зависимости или их близкие имитации**.
3. **Тестируйте сценарии использования системы**.

## Рекомендации по улучшению тестов

1. **Стремитесь к высокому покрытию кода тестами** - цель 80% и выше.
2. **Тестируйте граничные случаи** - пустые значения, отрицательные числа, очень большие числа и т.д.
3. **Пишите тесты до или одновременно с кодом** - это поможет улучшить дизайн кода.
4. **Регулярно запускайте тесты** - это поможет обнаружить проблемы на ранней стадии.
5. **Используйте непрерывную интеграцию** - это позволит автоматически запускать тесты при каждом коммите.

## Примеры тестов

Примеры тестов для различных компонентов системы можно найти в файле [test_examples.md](test_examples.md).
