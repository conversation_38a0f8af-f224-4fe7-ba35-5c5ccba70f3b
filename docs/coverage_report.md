# Отчет о покрытии кода тестами

## Общая информация

- **Общее покрытие кода**: 62%
- **Количество тестов**: 477
- **Дата отчета**: 02.04.2024

## Покрытие по модулям

### Модули с высоким покрытием (90-100%)

| Модуль | Покрытие |
|--------|----------|
| src/domain/value_objects/ad_id.py | 100% |
| src/domain/value_objects/address.py | 100% |
| src/domain/value_objects/price.py | 100% |
| src/infrastructure/formatters/value_formatter.py | 100% |
| src/infrastructure/parsers/olx/utils/url_builder.py | 100% |
| src/infrastructure/persistence/processed_ad_storage.py | 100% |
| src/infrastructure/storage/models/processed_ad.py | 100% |
| src/presentation/telegram/messages/formatters.py | 100% |
| src/presentation/telegram/messages/templates.py | 100% |
| src/domain/value_objects/contact.py | 95% |
| src/infrastructure/parsers/olx/utils/contact_utils.py | 95% |
| src/infrastructure/parsers/olx/utils/html_utils.py | 95% |
| src/presentation/telegram/handlers.py | 96% |

### Модули со средним покрытием (70-89%)

| Модуль | Покрытие |
|--------|----------|
| src/application/services/notification.py | 88% |
| src/domain/value_objects/area.py | 89% |
| src/domain/services/property_validator.py | 83% |
| src/infrastructure/formatters/base_formatter.py | 83% |
| src/infrastructure/formatters/property_formatter.py | 86% |
| src/infrastructure/parsers/olx/utils/area_utils.py | 93% |
| src/infrastructure/parsers/olx/utils/building_utils.py | 85% |
| src/infrastructure/parsers/olx/utils/date_utils.py | 87% |
| src/infrastructure/parsers/olx/utils/id_extractor.py | 91% |
| src/infrastructure/parsers/olx/utils/price_utils.py | 92% |
| src/infrastructure/parsers/olx/utils/room_utils.py | 91% |
| src/infrastructure/storage/json_storage.py | 91% |
| src/presentation/telegram/bot.py | 92% |
| src/presentation/telegram/services/sender.py | 91% |

### Модули с низким покрытием (50-69%)

| Модуль | Покрытие |
|--------|----------|
| src/application/interfaces/notification_sender.py | 75% |
| src/application/interfaces/notification_service.py | 80% |
| src/application/interfaces/parser.py | 80% |
| src/application/interfaces/processed_ad_storage.py | 77% |
| src/application/services/property.py | 76% |
| src/config/app_settings.py | 74% |
| src/config/paths.py | 81% |
| src/config/settings.py | 82% |
| src/domain/entities/property.py | 93% |
| src/infrastructure/parsers/olx/olx_parser.py | 69% |

### Модули с критически низким покрытием (0-49%)

| Модуль | Покрытие |
|--------|----------|
| src/application/services/dummy_notification.py | 0% |
| src/config/dotenv.py | 56% |
| src/infrastructure/external/telegram/notification_sender.py | 0% |
| src/infrastructure/external/zyte/api_client.py | 0% |
| src/infrastructure/logging/formatters/custom.py | 0% |
| src/infrastructure/logging/handlers/console.py | 0% |
| src/infrastructure/logging/handlers/file.py | 0% |
| src/infrastructure/logging/logger.py | 0% |
| src/infrastructure/parsers/olx/utils/error_handling.py | 40% |
| src/infrastructure/parsers/olx/utils/json_ld_processor.py | 15% |
| src/infrastructure/parsers/olx/utils/location_utils.py | 97% |
| src/infrastructure/parsers/olx/utils/phone_extractor.py | 17% |
| src/infrastructure/parsers/olx/utils/tag_utils.py | 18% |
| src/infrastructure/parsers/olx/utils/text_processors.py | 99% |
| src/presentation/cli/commands/parse.py | 0% |
| src/presentation/cli/commands/schedule.py | 0% |
| src/presentation/cli/main.py | 0% |
| src/presentation/scheduler/* | 0% |
| src/presentation/telegram/main.py | 0% |

## Улучшения покрытия

В рамках работы по улучшению покрытия кода тестами были достигнуты следующие результаты:

1. Увеличено общее покрытие кода с 55% до 62%.
2. Значительно улучшено покрытие для следующих модулей:
   - src/domain/entities/property.py: с 54% до 93%
   - src/infrastructure/parsers/olx/utils/contact_utils.py: с 28% до 95%
   - src/infrastructure/parsers/olx/utils/html_utils.py: с 39% до 95%
   - src/infrastructure/parsers/olx/utils/location_utils.py: с 32% до 97%
   - src/infrastructure/parsers/olx/utils/text_processors.py: с 25% до 99%

## Рекомендации по дальнейшему улучшению покрытия

1. **Приоритет 1**: Улучшить покрытие для модулей с низким покрытием (50-69%), особенно для:
   - src/config/dotenv.py (56%)
   - src/infrastructure/parsers/olx/olx_parser.py (69%)

2. **Приоритет 2**: Улучшить покрытие для модулей с критически низким покрытием (0-49%), особенно для:
   - src/infrastructure/parsers/olx/utils/error_handling.py (40%)
   - src/infrastructure/parsers/olx/utils/json_ld_processor.py (15%)
   - src/infrastructure/parsers/olx/utils/phone_extractor.py (17%)
   - src/infrastructure/parsers/olx/utils/tag_utils.py (18%)

3. **Приоритет 3**: Добавить тесты для модулей с нулевым покрытием, особенно для:
   - src/infrastructure/external/telegram/notification_sender.py (0%)
   - src/infrastructure/external/zyte/api_client.py (0%)

## Заключение

Текущее покрытие кода тестами составляет 62%, что является хорошим показателем, но требует дальнейшего улучшения. Рекомендуется стремиться к покрытию не менее 80% для критических модулей и не менее 70% для всего проекта.

Для достижения этой цели необходимо продолжить работу по написанию тестов для модулей с низким и критически низким покрытием, а также добавить тесты для модулей с нулевым покрытием.
