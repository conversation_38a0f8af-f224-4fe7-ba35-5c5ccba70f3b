# Техническое задание на переход на python-telegram-bot 20.3

## Общая информация

**Проект**: Parser_olx  
**Текущая версия библиотеки**: python-telegram-bot 13.15  
**Целевая версия библиотеки**: python-telegram-bot 20.3  
**Репозиторий**: https://github.com/Vnedrenec/Parser_olx.git

## Цель проекта

Полностью обновить код проекта для работы с python-telegram-bot версии 20.3, отказавшись от устаревшего API и перейдя на современный асинхронный API.

## Обоснование необходимости

1. Версия 13.x больше не поддерживается и не получает обновлений безопасности
2. Версия 20.x предоставляет более современный и эффективный асинхронный API
3. Новая версия имеет улучшенную документацию и поддержку сообщества
4. Избавление от легаси-кода повысит поддерживаемость проекта в долгосрочной перспективе

## Объем работ

### 1. Анализ и подготовка

- Изучение документации python-telegram-bot 20.3
- Анализ текущей кодовой базы и выявление всех мест, требующих изменений
- Создание отдельной ветки для разработки
- Обновление зависимостей в requirements.txt

### 2. Обновление основных компонентов

#### 2.1. Обновление файлов, связанных с Telegram API

Следующие файлы требуют обновления для работы с python-telegram-bot 20.3:

##### 2.1.1. Основные файлы Telegram бота

1. **src/presentation/telegram/bot.py**
   - Полная переработка класса `PropertyBot`
   - Замена `Updater` и `Dispatcher` на `Application`
   - Переработка методов регистрации обработчиков
   - Переработка методов запуска и остановки бота на асинхронные
   - Адаптация обработки ошибок

2. **src/presentation/telegram/handlers.py**
   - Переработка всех обработчиков на асинхронные функции
   - Изменение типа контекста с `CallbackContext` на `ContextTypes.DEFAULT_TYPE`
   - Адаптация к новым сигнатурам методов

3. **src/presentation/telegram/__init__.py**
   - Обновление импортов
   - Удаление кода совместимости

4. **src/presentation/telegram/main.py**
   - Адаптация точки входа для работы с асинхронным API
   - Обновление создания и запуска бота

##### 2.1.2. Сервисы и утилиты Telegram

5. **src/presentation/telegram/services/sender.py**
   - Адаптация к асинхронному API
   - Обновление методов отправки сообщений
   - Обновление импортов и типов

6. **src/presentation/telegram/services/__init__.py**
   - Обновление импортов

7. **src/infrastructure/external/telegram/notification_sender.py**
   - Переработка всех методов отправки сообщений на асинхронные
   - Обновление импорта `ParseMode` из `telegram.constants`
   - Адаптация обработки ошибок

8. **src/infrastructure/external/telegram/__init__.py**
   - Обновление импортов

##### 2.1.3. Интерфейсы и сервисы, взаимодействующие с Telegram

9. **src/application/interfaces/notification_sender.py**
   - Обновление интерфейса для поддержки асинхронных методов

10. **src/application/services/notification.py**
    - Адаптация к асинхронным методам отправки уведомлений
    - Изменения в логике работы с асинхронными функциями

##### 2.1.4. Точки входа, использующие Telegram

11. **src/presentation/scheduler/main.py**
    - Адаптация для работы с асинхронным API Telegram
    - Обновление создания и использования сервисов уведомлений

12. **src/presentation/cli/commands/parse.py**
    - Адаптация для работы с асинхронным API Telegram
    - Обновление создания и использования сервисов уведомлений

13. **src/presentation/cli/commands/schedule.py**
    - Адаптация для работы с асинхронным API Telegram
    - Обновление создания и использования сервисов уведомлений

##### 2.1.5. Тесты

14. **tests/unit/presentation/telegram/test_bot.py**
    - Обновление тестов для работы с новым API
    - Адаптация моков и фикстур

15. **tests/unit/presentation/telegram/test_handlers.py**
    - Обновление тестов для работы с асинхронными обработчиками
    - Адаптация моков и фикстур

16. **tests/unit/presentation/telegram/services/test_sender.py**
    - Обновление тестов для работы с асинхронными методами
    - Адаптация моков и фикстур

17. **tests/unit/application/services/test_notification_service.py**
    - Обновление тестов для работы с асинхронными методами
    - Адаптация моков и фикстур

18. **test_telegram.py** (если используется)
    - Обновление для работы с асинхронным API

### 3. Обновление запуска приложения

- Адаптация всех точек входа для работы с асинхронными функциями
- Обновление механизма запуска бота во всех компонентах
- Обеспечение корректной обработки сигналов и завершения работы

### 4. Тестирование и отладка

- Написание тестов для проверки работы обновленного кода
- Тестирование отправки уведомлений
- Тестирование обработки команд бота
- Тестирование работы шедулера

### 5. Документация

- Обновление документации по работе с Telegram-ботом
- Документирование новых подходов и паттернов

## Технические требования

### Общие требования

1. Весь код должен быть асинхронным с использованием `async/await`
2. Использовать типизацию для всех функций и методов
3. Следовать стилю кода, принятому в проекте
4. Обеспечить обратную совместимость на уровне API (внешние вызовы должны работать так же)
5. Добавить подробное логирование для отладки

### Требования к реализации

#### Класс PropertyBot

```python
class PropertyBot:
    """
    Telegram бот для уведомлений о новых объявлениях.
    """

    def __init__(self, token: str, property_service: PropertyService,
                allowed_users: Optional[List[int]] = None,
                logger: Optional[logging.Logger] = None):
        """
        Инициализирует Telegram бота.
        """
        # Инициализация бота через Application.builder()
        self.application = Application.builder().token(token).build()
        # Регистрация обработчиков
        # ...

    async def start(self) -> None:
        """
        Запускает бота.
        """
        # Асинхронный запуск бота
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()

    async def stop(self) -> None:
        """
        Останавливает бота.
        """
        # Асинхронная остановка бота
        await self.application.stop()
        await self.application.shutdown()
```

#### Обработчики команд

```python
async def handle_start(update: Update, context: ContextTypes.DEFAULT_TYPE, bot: PropertyBot) -> None:
    """
    Обрабатывает команду /start.
    """
    # Асинхронная обработка команды
    # ...
```

#### Отправка уведомлений

```python
class TelegramNotificationSender(INotificationSender):
    """
    Отправка уведомлений через Telegram.
    """

    async def send(self, message: str, chat_id: Union[str, int], parse_mode: Optional[str] = None,
                  disable_web_page_preview: bool = False) -> bool:
        """
        Отправляет сообщение в чат.
        """
        try:
            await self._bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode=parse_mode or ParseMode.HTML,
                disable_web_page_preview=disable_web_page_preview
            )
            return True
        except TelegramError as e:
            self._logger.error(f"Ошибка при отправке сообщения в чат {chat_id}: {e}")
            return False
```

## План работ

### Этап 1: Анализ и подготовка (2-3 дня)

1. Изучение документации python-telegram-bot 20.3
2. Анализ кодовой базы
3. Создание ветки `feature/telegram-bot-update`
4. Обновление requirements.txt

### Этап 2: Обновление интерфейсов и базовых классов (3-4 дня)

1. Обновление `INotificationSender`
2. Создание базовых асинхронных классов и утилит

### Этап 3: Обновление основных компонентов (5-7 дней)

1. Обновление `PropertyBot`
2. Обновление обработчиков команд
3. Обновление сервисов отправки уведомлений

### Этап 4: Обновление запуска приложения (2-3 дня)

1. Адаптация шедулера
2. Адаптация CLI-интерфейса

### Этап 5: Тестирование и отладка (3-5 дней)

1. Написание тестов
2. Тестирование всех компонентов
3. Исправление выявленных ошибок

### Этап 6: Документация и завершение (1-2 дня)

1. Обновление документации
2. Создание pull request
3. Код-ревью и внесение правок

## Общая оценка трудозатрат

| Этап | Трудозатраты (дни) |
|------|-------------------|
| Анализ и подготовка | 2-3 |
| Обновление интерфейсов и базовых классов | 3-4 |
| Обновление основных компонентов | 5-7 |
| Обновление запуска приложения | 2-3 |
| Тестирование и отладка | 3-5 |
| Документация и завершение | 1-2 |
| **Итого** | **16-24** |

## Риски и их минимизация

1. **Риск**: Сложность асинхронного программирования
   **Минимизация**: Тщательное изучение документации, использование примеров из официальной документации

2. **Риск**: Каскадные изменения в зависимых модулях
   **Минимизация**: Поэтапное обновление с тестированием каждого компонента

3. **Риск**: Проблемы с обратной совместимостью
   **Минимизация**: Сохранение публичного API, добавление адаптеров при необходимости

4. **Риск**: Сложность отладки асинхронного кода
   **Минимизация**: Подробное логирование, использование инструментов для отладки асинхронного кода

## Критерии приемки

1. Все тесты проходят успешно
2. Бот корректно запускается и обрабатывает команды
3. Уведомления успешно отправляются
4. Шедулер корректно работает с обновленным кодом
5. Код соответствует стилю проекта и имеет типизацию
6. Документация обновлена и соответствует новой реализации

## Дополнительные материалы

1. [Официальная документация python-telegram-bot 20.3](https://docs.python-telegram-bot.org/en/v20.3/)
2. [Руководство по миграции с v13.x на v20.x](https://github.com/python-telegram-bot/python-telegram-bot/wiki/Transition-guide-to-Version-20.0)
3. [Примеры использования асинхронного API](https://github.com/python-telegram-bot/python-telegram-bot/tree/master/examples)

## Заключение

Переход на python-telegram-bot 20.3 представляет собой значительный объем работы, но даст чистую и современную кодовую базу без легаси-кода. Основная сложность заключается в переходе на асинхронное программирование и адаптации всех компонентов системы к этому подходу.

После завершения обновления проект будет использовать современный, поддерживаемый API, что обеспечит долгосрочную поддерживаемость и возможность использования новых функций Telegram Bot API.
