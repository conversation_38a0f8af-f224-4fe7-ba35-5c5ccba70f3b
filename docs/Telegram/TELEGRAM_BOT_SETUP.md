# Установка и запуск Telegram бота

## Подготовка

1. Клонируйте репозиторий:
   ```bash
   git clone https://github.com/Vnedrenec/Parser_olx.git
   cd Parser_olx
   ```

2. Создайте виртуальное окружение и установите зависимости:
   ```bash
   python -m venv venv
   source venv/bin/activate  # На Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. Создайте файл .env с переменными окружения:
   ```bash
   cp .env.example .env
   ```

4. Отредактируйте файл .env, указав токен бота и список разрешенных пользователей:
   ```
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   TELEGRAM_ALLOWED_USERS=123456789,987654321
   ```

## Запуск бота

### Локальный запуск

```bash
python -m src.presentation.telegram
```

### Запуск с помощью Docker

```bash
docker-compose -f docker-compose.telegram.yml up -d
```

### Запуск с помощью systemd

1. Скопируйте файл службы в системную директорию:
   ```bash
   sudo cp olx-telegram-bot.service /etc/systemd/system/
   ```

2. Перезагрузите конфигурацию systemd:
   ```bash
   sudo systemctl daemon-reload
   ```

3. Включите и запустите службу:
   ```bash
   sudo systemctl enable olx-telegram-bot
   sudo systemctl start olx-telegram-bot
   ```

4. Проверьте статус службы:
   ```bash
   sudo systemctl status olx-telegram-bot
   ```

### Развертывание на Fly.io

1. Установите Fly CLI:
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. Войдите в аккаунт Fly.io:
   ```bash
   fly auth login
   ```

3. Создайте приложение:
   ```bash
   fly apps create olx-telegram-bot
   ```

4. Создайте тома для данных и логов:
   ```bash
   fly volumes create olx_data --size 1
   fly volumes create olx_logs --size 1
   ```

5. Установите секреты:
   ```bash
   fly secrets set TELEGRAM_BOT_TOKEN=your_bot_token_here
   fly secrets set TELEGRAM_ALLOWED_USERS=123456789,987654321
   ```

6. Разверните приложение:
   ```bash
   fly deploy
   ```

## Проверка работы бота

1. Откройте Telegram и найдите своего бота по имени.
2. Отправьте команду `/start`.
3. Бот должен ответить приветственным сообщением.
4. Отправьте команду `/help` для получения списка доступных команд.

## Управление ботом

- `/start` - Начать работу с ботом
- `/help` - Показать справку
- `/status` - Показать статус бота
- `/stop` - Остановить уведомления
- `/category add <категория>` - Добавить категорию для уведомлений
- `/category remove <категория>` - Удалить категорию для уведомлений
- `/category list` - Показать список категорий для уведомлений

## Логирование

Логи бота сохраняются в файл `logs/telegram_bot.log`. Для просмотра логов можно использовать команду:

```bash
tail -f logs/telegram_bot.log
```

## Устранение неполадок

1. Если бот не отвечает, проверьте:
   - Правильность токена бота в файле .env
   - Наличие интернет-соединения
   - Логи на наличие ошибок

2. Если бот отвечает только некоторым пользователям, проверьте:
   - Список разрешенных пользователей в файле .env
   - Логи на наличие сообщений о попытках доступа от неразрешенных пользователей
