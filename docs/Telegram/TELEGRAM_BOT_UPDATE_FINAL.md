# Обновление Telegram бота на python-telegram-bot 20.3

## Выполненные изменения

1. Обновлены обработчики в файле handlers.py:
   - Все методы стали асинхронными (добавлен async)
   - Д<PERSON><PERSON><PERSON>влен await перед вызовами методов Telegram API (reply_text и др.)
   - Удалены неиспользуемые импорты

2. Обновлен файл bot.py:
   - Метод _is_user_allowed стал асинхронным
   - Добавлен await перед вызовами _is_user_allowed в обработчиках
   - Добавлен await перед вызовами методов Telegram API
   - Методы start и stop обновлены для работы с асинхронными функциями
   - Добавлен метод remove_all_categories для удаления всех категорий пользователя

3. Создан новый скрипт async_bot_runner.py для запуска бота в асинхронном режиме

4. Создана заглушка DummyOlxParser для тестирования бота без реального парсера

5. Созданы файлы для развертывания:
   - Dockerfile.telegram - для сборки Docker-образа
   - docker-compose.telegram.yml - для запуска в Docker Compose
   - olx-telegram-bot.service - для запуска в systemd
   - fly.telegram.toml - для развертывания на Fly.io

## Как запустить бота

### Локальный запуск

```bash
python -m src.presentation.telegram
```

### Запуск с помощью Docker

```bash
docker-compose -f docker-compose.telegram.yml up -d
```

### Запуск с помощью systemd

1. Скопируйте файл службы в системную директорию:
   ```bash
   sudo cp olx-telegram-bot.service /etc/systemd/system/
   ```

2. Перезагрузите конфигурацию systemd:
   ```bash
   sudo systemctl daemon-reload
   ```

3. Включите и запустите службу:
   ```bash
   sudo systemctl enable olx-telegram-bot
   sudo systemctl start olx-telegram-bot
   ```

### Развертывание на Fly.io

1. Установите Fly CLI:
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. Войдите в аккаунт Fly.io:
   ```bash
   fly auth login
   ```

3. Создайте приложение:
   ```bash
   fly apps create olx-telegram-bot
   ```

4. Создайте тома для данных и логов:
   ```bash
   fly volumes create olx_data --size 1
   fly volumes create olx_logs --size 1
   ```

5. Установите секреты:
   ```bash
   fly secrets set TELEGRAM_BOT_TOKEN=your_bot_token_here
   fly secrets set TELEGRAM_ALLOWED_USERS=123456789,987654321
   ```

6. Разверните приложение:
   ```bash
   fly deploy -c fly.telegram.toml
   ```

## Известные проблемы

1. IDE может показывать предупреждения о неизвестных символах импорта и о том, что Message не является awaitable. Эти предупреждения могут быть связаны с тем, что IDE не распознает правильно типы из новой версии библиотеки. При запуске кода эти предупреждения могут не вызывать ошибок, если установлена правильная версия библиотеки.

2. Фильтры в новой версии библиотеки могут работать иначе. Если возникают проблемы с фильтрами, обратитесь к документации python-telegram-bot 20.3.

## Дополнительная информация

Для получения дополнительной информации о миграции на python-telegram-bot 20.3 обратитесь к следующим ресурсам:

- [Официальная документация python-telegram-bot 20.3](https://docs.python-telegram-bot.org/en/v20.3/)
- [Руководство по миграции с v13.x на v20.x](https://github.com/python-telegram-bot/python-telegram-bot/wiki/Transition-guide-to-Version-20.0)
