# Миграция на python-telegram-bot 20.3

## Выполненные изменения

1. Обновлены обработчики в файле handlers.py:
   - Все методы стали асинхронными (добавлен async)
   - Добавлен await перед вызовами методов Telegram API (reply_text и др.)
   - Удалены неиспользуемые импорты

2. Обновлен файл bot.py:
   - Метод _is_user_allowed стал асинхронным
   - Добавлен await перед вызовами _is_user_allowed в обработчиках
   - Добавлен await перед вызовами методов Telegram API
   - Методы start и stop обновлены для работы с асинхронными функциями

3. Создан новый скрипт async_bot_runner.py для запуска бота в асинхронном режиме

## Как запустить бота

1. Убедитесь, что установлена правильная версия библиотеки:
   ```bash
   pip install python-telegram-bot==20.3
   ```

2. Для запуска бота используйте команду:
   ```bash
   python -m src.presentation.telegram
   ```

2. Создайте файл .env с переменными окружения:
   ```
   TELEGRAM_BOT_TOKEN=ваш_токен_бота
   TELEGRAM_ALLOWED_USERS=123456789,987654321
   ```

3. Запустите бота с помощью нового скрипта:
   ```bash
   python async_bot_runner.py
   ```

## Известные проблемы

1. IDE может показывать предупреждения о неизвестных символах импорта и о том, что Message не является awaitable. Эти предупреждения могут быть связаны с тем, что IDE не распознает правильно типы из новой версии библиотеки. При запуске кода эти предупреждения могут не вызывать ошибок, если установлена правильная версия библиотеки.

2. Фильтры в новой версии библиотеки могут работать иначе. Если возникают проблемы с фильтрами, обратитесь к документации python-telegram-bot 20.3.

## Дополнительная информация

Для получения дополнительной информации о миграции на python-telegram-bot 20.3 обратитесь к следующим ресурсам:

- [Официальная документация python-telegram-bot 20.3](https://docs.python-telegram-bot.org/en/v20.3/)
- [Руководство по миграции с v13.x на v20.x](https://github.com/python-telegram-bot/python-telegram-bot/wiki/Transition-guide-to-Version-20.0)
