# Руководство пользователя Telegram бота

Данное руководство содержит информацию о том, как использовать Telegram бота для получения уведомлений о новых объявлениях.

## Начало работы

1. Найдите бота в Telegram по имени пользователя, которое предоставил вам администратор.
2. Отправьте команду `/start` для начала работы с ботом.
3. Бот отправит вам приветственное сообщение и предложит выбрать категории для подписки.

## Основные команды

- `/start` - Начать работу с ботом
- `/help` - Показать справку
- `/status` - Показать статус бота
- `/stop` - Остановить уведомления
- `/category` - Управление категориями
- `/language` - Выбор языка интерфейса (русский или украинский)

## Управление категориями

Для управления категориями используйте команду `/category` с следующими параметрами:

- `/category add <категория>` - Добавить категорию для уведомлений
- `/category remove <категория>` - Удалить категорию из уведомлений
- `/category list` - Показать список категорий для уведомлений
- `/category all` - Подписаться на все категории

Пример:
```
/category add apartments_sale
```

## Выбор языка

Для выбора языка интерфейса используйте команду `/language`. Бот предложит вам выбрать один из доступных языков:

- 🇷🇺 Русский
- 🇺🇦 Українська

Выберите нужный язык, нажав на соответствующую кнопку.

## Доступные категории

В боте доступны следующие категории недвижимости:

- `apartments_sale` - Квартиры (продажа)
- `apartments_rent` - Квартиры (аренда)
- `houses_sale` - Дома (продажа)
- `houses_rent` - Дома (аренда)
- `rooms_sale` - Комнаты (продажа)
- `rooms_rent` - Комнаты (аренда)
- `garages_sale` - Гаражи (продажа)
- `garages_rent` - Гаражи (аренда)
- `dacha_sale` - Дачи (продажа)
- `dacha_rent` - Дачи (аренда)
- `land_sale` - Земельные участки (продажа)
- `commercial_sale` - Коммерческая недвижимость (продажа)
- `commercial_rent` - Коммерческая недвижимость (аренда)

## Получение уведомлений

После подписки на категории вы будете получать уведомления о новых объявлениях в выбранных категориях. Уведомления содержат:

- Заголовок объявления
- Цену
- Расположение
- Детали объявления
- Ссылку на объявление

## Остановка уведомлений

Для остановки уведомлений используйте команду `/stop`. Бот отключит все уведомления и предложит вам выбрать категории для подписки, если вы захотите возобновить уведомления.

## Проверка статуса

Для проверки статуса бота используйте команду `/status`. Бот отправит вам информацию о своем текущем статусе и список активных категорий, на которые вы подписаны.

## Получение справки

Для получения справки используйте команду `/help`. Бот отправит вам информацию о доступных командах и категориях.
