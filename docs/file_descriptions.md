# Подробное Описание Файлов Проекта (Упрощенный OLX Парсер, Без AI)

Этот документ содержит подробное описание назначения и ответственности каждого файла проекта на основе структуры, представленной в `docs/architecture/clean_architecture.md`.

## `src/` - Исходный код приложения

### `src/domain/` - Domain Layer: Бизнес-логика и модели (используемые в памяти)

*   **`src/domain/entities/__init__.py`**: Стандартный файл Python, который помечает директорию `entities` как пакет. Позволяет импортировать классы из этого пакета.
*   **`src/domain/entities/property.py`**: Определяет класс `Property`. Это центральная доменная сущность, представляющая объект недвижимости *в памяти* во время обработки. Она агрегирует различные Value Objects (цена, адрес, площадь и т.д.) и может содержать методы, реализующие бизнес-логику, специфичную для одного объекта недвижимости (например, расчет цены за метр). **Важно:** В текущей архитектуре этот объект не предназначен для прямого сохранения в БД целиком, а используется как структура для передачи данных между парсером, валидатором и сервисом уведомлений.
*   **`src/domain/services/__init__.py`**: Стандартный файл Python, помечающий директорию `services` как пакет.
*   **`src/domain/services/property_validator.py`**: Содержит класс `PropertyValidator`. Его задача — проверять объект `Property` (уже созданный в памяти) на соответствие **бизнес-правилам**, которые могут затрагивать несколько атрибутов объекта. Например, проверка, что цена находится в допустимом диапазоне для данного типа недвижимости и площади, или что количество этажей в здании больше или равно этажу квартиры. Этот валидатор инкапсулирует именно бизнес-логику проверки.
*   **`src/domain/value_objects/__init__.py`**: Стандартный файл Python, помечающий директорию `value_objects` как пакет.
*   **`src/domain/value_objects/address.py`**: Определяет Value Object `Address`. Представляет адрес объекта недвижимости как единое целое, возможно, с разделением на город, улицу, номер дома. Обеспечивает инварианты адреса (например, что город не пустой).
*   **`src/domain/value_objects/price.py`**: Определяет Value Object `Price`. Инкапсулирует сумму и валюту. При создании проверяет корректность данных (например, сумма не отрицательная, валюта из допустимого списка).
*   **`src/domain/value_objects/area.py`**: Определяет Value Object `Area`. Представляет площадь (общую, жилую, кухни) и обеспечивает ее корректность (например, не отрицательная).
*   **`src/domain/value_objects/contact.py`**: Определяет Value Object `Contact`. Инкапсулирует контактную информацию (имя, телефон), возможно, с базовой валидацией формата телефона.
*   **`src/domain/value_objects/ad_id.py`**: Определяет Value Object `AdId`. Представляет уникальный идентификатор объявления OLX, обеспечивая его корректность (например, что это не пустая строка или соответствует определенному формату). Используется для проверки дубликатов.

### `src/application/` - Application Layer: Прикладной слой

*   **`src/application/services/__init__.py`**: Стандартный файл Python, помечающий директорию `services` как пакет.
*   **`src/application/services/property.py`**: Содержит `PropertyService`. Это ключевой сервис, управляющий основным сценарием использования (use case). Он координирует взаимодействие между различными компонентами: запрашивает у парсера (`IParser`) список ID, проверяет их наличие в хранилище (`IProcessedAdStorage`), запрашивает детали для новых ID, инициирует создание объекта `Property` в памяти, вызывает доменный валидатор (`PropertyValidator`), запускает отправку уведомления (`INotificationSender` через `NotificationService`) и сохраняет обработанный ID (`IProcessedAdStorage`). Он содержит логику приложения, но не бизнес-правила и не детали инфраструктуры.
*   **`src/application/services/notification.py`**: Содержит `NotificationService`. Отвечает за логику, связанную с уведомлениями. Он принимает данные об объекте `Property` (из памяти), решает, нужно ли отправлять уведомление, возможно, форматирует базовые данные (используя форматтеры из Presentation Layer) и вызывает `INotificationSender` для фактической отправки.
*   **`src/application/interfaces/__init__.py`**: Стандартный файл Python, помечающий директорию `interfaces` как пакет.
*   **`src/application/interfaces/notification_service.py`**: Определяет абстрактный интерфейс (контракт) `INotificationService`. Это позволяет `PropertyService` зависеть от абстракции, а не от конкретной реализации сервиса уведомлений.
*   **`src/application/interfaces/notification_sender.py`**: Определяет абстрактный интерфейс `INotificationSender`. Описывает методы для отправки уведомлений, которые должны быть реализованы в конкретных реализациях (например, в Telegram).
*   **`src/application/interfaces/parser.py`**: Определяет абстрактный интерфейс `IParser`. Описывает методы, которые должен предоставлять любой парсер (например, `get_ad_ids()` и `get_ad_details()`). Application Layer зависит от этого интерфейса, а не от конкретной реализации парсера.
*   **`src/application/interfaces/processed_ad_storage.py`**: Определяет абстрактный интерфейс `IProcessedAdStorage`. Описывает методы, которые должно предоставлять любое хранилище обработанных ID (например, `add(ad_id: AdId)` и `exists(ad_id: AdId) -> bool`). Application Layer зависит от этого интерфейса, а не от конкретной реализации в БД.

### `src/config/` - Конфигурация приложения (внутри `src`)

*   **`src/config/__init__.py`**: Стандартный файл Python, помечающий директорию `config` как пакет.
*   **`src/config/constants.py`**: Содержит константы времени компиляции или редко изменяемые значения, используемые в коде приложения (например, стандартные таймауты, названия очередей, если бы они были).
*   **`src/config/dotenv.py`**: Загружает переменные окружения из файла `.env` и предоставляет функции для работы с ними, такие как `get_env()`, `get_env_bool()`, `get_env_int()`, `get_env_list()` и т.д.
*   **`src/config/paths.py`**: Определяет и предоставляет удобный доступ к основным путям файловой системы проекта (например, корень проекта, директория логов, директория конфигураций вне `src`).
*   **`src/config/settings.py`**: Ключевой файл для управления конфигурацией. Загружает настройки из файлов в директории `config/` (вне `src`) и переменных окружения с помощью модуля `dotenv.py`, валидирует их и предоставляет доступ к настройкам в виде типизированного объекта для всего приложения.
*   **`src/config/data/__init__.py`**: Стандартный файл Python, помечающий директорию `data` как пакет. Позволяет импортировать данные из этой директории как модуль Python.
*   **`src/config/data/categories.json`**: JSON-файл, содержащий список категорий OLX для парсинга. Каждая запись включает URL категории, ее имя, тип, подтип, флаг активности и ожидаемую структуру полей (`fields`) для извлечения парсером.

### `src/infrastructure/` - Infrastructure Layer: Внешние сервисы и реализации

*   **`src/infrastructure/parsers/olx/__init__.py`**: Стандартный файл Python, помечающий директорию `olx` как пакет.
*   **`src/infrastructure/parsers/olx/olx_parser.py`**: Конкретная реализация интерфейса `IParser` для сайта OLX. Содержит логику взаимодействия с Zyte API (или напрямую с сайтом), использует утилиты из `utils/` для разбора HTML/JSON-LD, извлечения данных и их первичной обработки. Реализует методы `get_ad_ids` и `get_ad_details`.
*   **`src/infrastructure/parsers/olx/utils/__init__.py`**: Стандартный файл Python, помечающий директорию `utils` как пакет.
*   **`src/infrastructure/parsers/olx/utils/html_utils.py`**: Содержит функции для работы с HTML, включая очистку текста, извлечение чисел, цен, параметров URL, а также специфичные функции для разбора HTML-структуры страниц OLX, такие как извлечение заголовка, описания, параметров, изображений, контактной информации и т.д.
*   **`src/infrastructure/parsers/olx/utils/url_builder.py`**: Содержит набор функций для работы с URL для OLX. Предоставляет функции для добавления параметров запроса к URL, формирования URL объявления, категории, страницы с пагинацией и URL для API телефонов.
*   **`src/infrastructure/parsers/olx/utils/phone_extractor.py`**: Специализированная логика для поиска и извлечения телефонных номеров со страниц объявлений OLX, которые могут быть скрыты или представлены в разных форматах.
*   **`src/infrastructure/parsers/olx/utils/json_ld_processor.py`**: Функции для поиска, извлечения и разбора данных в формате JSON-LD (часто используется сайтами для предоставления структурированных данных поисковикам), которые могут содержать полезную информацию об объявлении.
*   **`src/infrastructure/parsers/olx/utils/price_utils.py`**: Утилиты для надежного извлечения цены и валюты из различных мест на странице OLX и их приведения к числовому формату. Может включать простейшие проверки формата.
*   **`src/infrastructure/parsers/olx/utils/area_utils.py`**: Утилиты для извлечения данных о площади (общей, жилой, кухни) и их приведения к числовому формату. Может включать простейшие проверки формата.
*   **`src/infrastructure/parsers/olx/utils/room_utils.py`**: Утилиты для извлечения информации о количестве комнат.
*   **`src/infrastructure/parsers/olx/utils/building_utils.py`**: Утилиты для извлечения данных о здании (тип, этажность и т.д.).
*   **`src/infrastructure/parsers/olx/utils/location_utils.py`**: Утилиты для извлечения и обработки адреса или местоположения объекта.
*   **`src/infrastructure/parsers/olx/utils/id_extractor.py`**: Логика для надежного извлечения уникального идентификатора объявления со страницы OLX.
*   **`src/infrastructure/parsers/olx/utils/contact_utils.py`**: Утилиты для извлечения имени и других контактных данных продавца.
*   **`src/infrastructure/parsers/olx/utils/date_utils.py`**: Утилиты для извлечения и преобразования даты публикации или обновления объявления в стандартный формат (например, datetime).
*   **`src/infrastructure/parsers/olx/utils/error_handling.py`**: Определяет пользовательские исключения, специфичные для ошибок парсинга OLX (например, `AdNotFound`, `ParsingError`), и, возможно, общие обработчики для них.
*   **`src/infrastructure/parsers/olx/utils/tag_utils.py`**: Утилиты для извлечения различных характеристик или тегов объявления (например, "мебель", "ремонт").
*   **`src/infrastructure/parsers/olx/utils/text_processors.py`**: Функции для базовой очистки и нормализации текстовых полей (например, описания), специфичные для формата данных на OLX.
*   **`src/infrastructure/formatters/__init__.py`**: Стандартный файл Python, помечающий директорию `formatters` как пакет.
*   **`src/infrastructure/formatters/base_formatter.py`**: Может содержать абстрактный базовый класс для форматтеров или общие вспомогательные функции форматирования.
*   **`src/infrastructure/formatters/property_formatter.py`**: Отвечает за преобразование словаря с сырыми данными, полученными от парсера, в структуру (например, другой словарь или DTO), удобную для последующего создания доменного объекта `Property` и его Value Objects.
*   **`src/infrastructure/formatters/value_formatter.py`**: Содержит мелкие вспомогательные функции для форматирования конкретных типов значений (например, форматирование цены для вывода, преобразование булевых значений).
*   **`src/infrastructure/storage/__init__.py`**: Стандартный файл Python, помечающий директорию `storage` как пакет.
*   **`src/infrastructure/storage/json_storage.py`**: Основной файл для работы с JSON файлом, содержащий класс `JsonStorage` для чтения и записи данных в JSON файл.


*   **`src/infrastructure/persistence/__init__.py`**: Стандартный файл Python, помечающий директорию `persistence` как пакет.
*   **`src/infrastructure/persistence/processed_ad_storage.py`**: Конкретная реализация (`DbProcessedAdStorage`) интерфейса `IProcessedAdStorage`. Использует `JsonStorage` для взаимодействия с JSON файлом, реализуя методы `add` и `exists`.
*   **`src/infrastructure/logging/__init__.py`**: Стандартный файл Python, помечающий директорию `logging` как пакет.
*   **`src/infrastructure/logging/logger.py`**: Настраивает корневой логгер Python или логгеры для конкретных модулей, устанавливая уровни логирования и добавляя обработчики.
*   **`src/infrastructure/logging/formatters/__init__.py`**: Стандартный файл Python (опционально).
*   **`src/infrastructure/logging/formatters/custom.py`**: Определяет пользовательские классы `logging.Formatter` для нестандартного форматирования сообщений лога (опционально).
*   **`src/infrastructure/logging/handlers/__init__.py`**: Стандартный файл Python.
*   **`src/infrastructure/logging/handlers/file.py`**: Настраивает `logging.FileHandler` или `logging.RotatingFileHandler` для записи логов в файл.
*   **`src/infrastructure/logging/handlers/console.py`**: Настраивает `logging.StreamHandler` для вывода логов в консоль (stderr/stdout).
*   **`src/infrastructure/external/zyte/__init__.py`**: Стандартный файл Python (опционально).
*   **`src/infrastructure/external/zyte/client.py`**: (Пример) Содержит класс или функции для инкапсуляции логики взаимодействия с Zyte API (отправка запросов, обработка ответов, обработка ошибок API). Используется парсером `OlxParser`.

### `src/presentation/` - Presentation Layer: Интерфейсы пользователя

*   **`src/presentation/cli/__init__.py`**: Стандартный файл Python, помечающий директорию `cli` как пакет.
*   **`src/presentation/cli/main.py`**: Основная точка входа для интерфейса командной строки. Инициализирует CLI-фреймворк (например, Typer или Click), регистрирует доступные команды из `commands/` и запускает обработку аргументов командной строки.
*   **`src/presentation/cli/commands/__init__.py`**: Стандартный файл Python, помечающий директорию `commands` как пакет.
*   **`src/presentation/cli/commands/parse.py`**: Определяет команду `parse`, которая доступна пользователю через CLI. Эта команда обычно получает необходимые зависимости (например, `PropertyService`) через DI-контейнер или фабрику и вызывает соответствующий метод сервиса для запуска процесса парсинга. Может обрабатывать аргументы командной строки (например, `--limit`).
*   **`src/presentation/cli/commands/schedule.py`**: Определяет команду `schedule`, которая доступна пользователю через CLI. Эта команда позволяет настроить и запустить планировщик задач для автоматического парсинга по расписанию. Может обрабатывать аргументы командной строки (например, `--interval`, `--categories`).
*   **`src/presentation/telegram/__init__.py`**: Стандартный файл Python, помечающий директорию `telegram` как пакет.
*   **`src/presentation/telegram/bot.py`**: Отвечает за создание и базовую настройку экземпляра клиента Telegram бота (например, `telegram.Bot` из `python-telegram-bot` или `aiogram.Bot`) с использованием API токена, полученного из конфигурации. Этот экземпляр затем используется `TelegramSender`.
*   **`src/presentation/telegram/handlers.py`**: Содержит обработчики команд для Telegram бота, такие как /start, /help, /status и другие.
*   **`src/presentation/telegram/main.py`**: Точка входа для запуска Telegram бота. Инициализирует все необходимые сервисы и запускает бота.
*   **`src/presentation/telegram/messages/__init__.py`**: Стандартный файл Python, помечающий директорию `messages` как пакет.
*   **`src/presentation/telegram/messages/templates.py`**: Содержит строки-шаблоны для сообщений, отправляемых в Telegram. Содержит шаблоны для разных типов недвижимости, команд и уведомлений.
*   **`src/presentation/telegram/messages/formatters.py`**: Содержит функции, которые принимают данные (например, объект `Property`) и преобразуют их в строки, подходящие для вставки в шаблоны из `templates.py`. Важно, что здесь обрабатываются случаи отсутствия данных (пустые поля), чтобы сообщение выглядело аккуратно.
*   **`src/presentation/telegram/services/__init__.py`**: Стандартный файл Python, помечающий директорию `services` как пакет.
*   **`src/presentation/telegram/services/sender.py`**: Конкретная реализация (`TelegramSender`) интерфейса `INotificationSender`. Использует экземпляр бота из `bot.py` для вызова методов Telegram Bot API (например, `sendMessage`) для отправки сформированного сообщения в указанный чат.
*   **`src/presentation/scheduler/__init__.py`**: Стандартный файл Python, помечающий директорию `scheduler` как пакет.
*   **`src/presentation/scheduler/main.py`**: Точка входа для запуска планировщика задач. Инициализирует все необходимые сервисы и запускает планировщик. Принимает аргументы командной строки, такие как `--categories`, `--interval`, `--limit` и `--delay`, что позволяет настраивать планировщик без изменения кода.
*   **`src/presentation/scheduler/core/__init__.py`**: Стандартный файл Python, помечающий директорию `core` как пакет.
*   **`src/presentation/scheduler/core/scheduler.py`**: Инициализирует, настраивает и запускает планировщик задач. Добавляет задачи из `tasks/` в расписание на основе конфигурации.
*   **`src/presentation/scheduler/core/executor.py`**: Содержит логику для выполнения задач в отдельных потоках или процессах, если это требуется планировщику.
*   **`src/presentation/scheduler/config/__init__.py`**: Стандартный файл Python, помечающий директорию `config` как пакет.
*   **`src/presentation/scheduler/config/settings.py`**: Загружает и предоставляет специфичные настройки для планировщика, такие как интервалы запуска задач, параметры хранилища задач (если используется).
*   **`src/presentation/scheduler/tasks/__init__.py`**: Стандартный файл Python, помечающий директорию `tasks` как пакет.
*   **`src/presentation/scheduler/tasks/base.py`**: Определяет базовый класс и общие функции для всех задач планировщика.
*   **`src/presentation/scheduler/tasks/parser_task.py`**: Определяет конкретную задачу парсинга, которая будет выполняться планировщиком по расписанию. Эта задача инициирует процесс парсинга, обычно путем вызова соответствующего метода `PropertyService`.

## `config/` - Конфигурационные файлы (вне `src`)

*   **`config/environments/development.yaml`**: Содержит настройки, специфичные для среды разработки: путь к JSON файлу, низкие уровни логирования, возможно, отключение некоторых внешних интеграций.
*   **`config/environments/production.yaml`**: Содержит настройки для производственной среды: путь к JSON файлу, более высокие уровни логирования, реальные ключи API (хотя лучше через env).
*   **`config/environments/testing.yaml`**: Настройки для автоматизированного тестирования: путь к тестовому JSON файлу, отключение внешних вызовов.
*   **`config/parsers/olx.yaml`**: Конфигурация, специфичная для парсера OLX: базовый URL сайта, возможно, User-Agent, таймауты, параметры Zyte API, если они специфичны для парсера.
*   **`config/telegram/bot.yaml`**: Настройки для Telegram: API токен бота (лучше через env), ID чатов для отправки уведомлений.
*   **`config/logging/default.yaml`**: Определяет конфигурацию системы логирования: форматтеры, обработчики (файл, консоль), уровни логирования для разных модулей.
*   **`config/storage.yaml`**: Параметры хранилища JSON: путь к файлу, настройки форматирования.

## `tests/` - Тесты

*   Содержит все автоматизированные тесты для проекта. Структура директорий внутри `tests/` (например, `unit/`, `integration/`, `functional/`) обычно отражает структуру `src/`, чтобы тесты было легко найти.

## Корневая директория

*   **`.gitignore`**: Стандартный файл Git для исключения из репозитория ненужных файлов (например, `__pycache__`, `.env`, файлы логов, виртуальные окружения).
*   **`activate_env.sh` / `setup_env.sh`**: Вспомогательные shell-скрипты для упрощения создания, настройки и активации виртуального окружения Python для проекта.
*   **`CONTRIBUTING.md`**: Документ с рекомендациями для разработчиков, желающих внести свой вклад в проект (правила оформления кода, процесс отправки pull request и т.д.).
*   **`MANIFEST.in`**: Используется инструментом `setuptools` при сборке Python-пакета (`.whl` или `.tar.gz`). Указывает, какие не-кодовые файлы (например, конфигурации, шаблоны) должны быть включены в дистрибутив.
*   **`README.md`**: Главный файл документации проекта. Содержит описание проекта, инструкции по установке зависимостей, настройке окружения, запуску приложения и тестов.
*   **`requirements-dev.txt` / `requirements-test.txt`**: Файлы, перечисляющие Python-зависимости, необходимые только для разработки (например, линтеры, средства форматирования) и для запуска тестов (например, `pytest`, `pytest-cov`, `mock`).
*   **`requirements.txt`**: Основной файл зависимостей, перечисляющий все Python-библиотеки, необходимые для работы приложения в production.
*   **`setup.py`**: Скрипт, используемый `setuptools` для сборки, упаковки и установки проекта как Python-пакета. Определяет метаданные пакета (имя, версия, автор), зависимости и точки входа.
