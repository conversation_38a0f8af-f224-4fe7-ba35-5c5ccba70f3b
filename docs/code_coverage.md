# Анализ покрытия кода тестами

В этом документе описаны инструкции по анализу покрытия кода тестами для проекта Parser OLX.

## Что такое покрытие кода тестами?

Покрытие кода тестами (code coverage) - это метрика, которая показывает, какая часть исходного кода программы выполняется при запуске тестов. Покрытие кода помогает определить, насколько хорошо тесты проверяют функциональность программы.

## Типы покрытия кода

1. **Покрытие строк (Line Coverage)** - процент строк кода, которые были выполнены при запуске тестов.
2. **Покрытие ветвей (Branch Coverage)** - процент ветвей кода (например, if/else), которые были выполнены при запуске тестов.
3. **Покрытие функций (Function Coverage)** - процент функций, которые были вызваны при запуске тестов.
4. **Покрытие классов (Class Coverage)** - процент классов, которые были использованы при запуске тестов.

## Инструменты для анализа покрытия кода

В проекте Parser OLX используются следующие инструменты для анализа покрытия кода:

1. **pytest-cov** - расширение для pytest, которое позволяет анализировать покрытие кода тестами.
2. **Codecov** - сервис для визуализации и анализа покрытия кода тестами.

## Запуск анализа покрытия кода

### Локальный анализ покрытия кода

```bash
# Запуск тестов с отчетом о покрытии
pytest --cov=src

# Генерация HTML-отчета о покрытии
pytest --cov=src --cov-report=html

# Генерация XML-отчета о покрытии для Codecov
pytest --cov=src --cov-report=xml
```

### Анализ покрытия кода в CI/CD

Анализ покрытия кода автоматически выполняется при каждом пуше в репозиторий или создании Pull Request. Результаты анализа отправляются в Codecov.

## Интерпретация результатов анализа покрытия кода

### Локальный HTML-отчет

После выполнения команды `pytest --cov=src --cov-report=html` будет создана директория `htmlcov`, в которой будет находиться HTML-отчет о покрытии кода тестами. Откройте файл `htmlcov/index.html` в браузере для просмотра отчета.

В отчете будет представлена информация о покрытии кода тестами для каждого файла в проекте. Для каждого файла будет показано:

- Процент покрытия строк
- Процент покрытия ветвей
- Количество пропущенных строк
- Количество пропущенных ветвей

Строки, которые не были выполнены при запуске тестов, будут выделены красным цветом.

### Отчет Codecov

Отчет Codecov доступен на сайте Codecov после настройки интеграции с GitHub. В отчете будет представлена информация о покрытии кода тестами для каждого коммита и Pull Request.

## Целевые показатели покрытия кода

Для проекта Parser OLX установлены следующие целевые показатели покрытия кода:

- **Общее покрытие строк**: 80%
- **Покрытие строк для критических модулей**: 90%

Критическими модулями считаются:

- Доменный слой (src/domain/)
- Слой приложения (src/application/)
- Инфраструктурный слой (src/infrastructure/)

## Рекомендации по улучшению покрытия кода

1. **Начинайте с критических модулей** - сосредоточьтесь на тестировании критических модулей, которые содержат основную бизнес-логику.
2. **Используйте TDD (Test-Driven Development)** - пишите тесты до или одновременно с кодом.
3. **Тестируйте граничные случаи** - пустые значения, отрицательные числа, очень большие числа и т.д.
4. **Используйте параметризованные тесты** для тестирования различных входных данных.
5. **Изолируйте тесты** - каждый тест должен быть независимым от других тестов.
6. **Используйте моки и стабы** для имитации зависимостей.
7. **Регулярно запускайте анализ покрытия кода** - это поможет обнаружить непокрытые участки кода.
8. **Добавляйте тесты для новой функциональности** - при добавлении новой функциональности всегда добавляйте соответствующие тесты.

## Исключение кода из анализа покрытия

Иногда некоторые участки кода не нужно или невозможно тестировать. В таких случаях можно исключить их из анализа покрытия кода.

Для исключения строки кода из анализа покрытия добавьте комментарий `# pragma: no cover`:

```python
def some_function():
    try:
        # Код, который нужно тестировать
        return True
    except Exception:  # pragma: no cover
        # Код, который не нужно тестировать
        return False
```

Для исключения блока кода из анализа покрытия используйте комментарии `# pragma: no cover` и `# pragma: no cover end`:

```python
def some_function():
    # pragma: no cover
    if some_condition:
        # Код, который не нужно тестировать
        return True
    # pragma: no cover end
    
    # Код, который нужно тестировать
    return False
```

## Заключение

Анализ покрытия кода тестами - это важный инструмент для обеспечения качества кода. Он помогает определить, насколько хорошо тесты проверяют функциональность программы, и выявить непокрытые участки кода, которые могут содержать ошибки.

Регулярный анализ покрытия кода и добавление тестов для непокрытых участков кода помогут улучшить качество кода и снизить количество ошибок в программе.
