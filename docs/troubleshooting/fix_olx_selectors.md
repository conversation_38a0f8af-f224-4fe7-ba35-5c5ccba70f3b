# Инструкция по исправлению проблем с селекторами OLX

Если парсер перестал корректно извлекать данные с сайта OLX из-за изменения селекторов, следуйте этой пошаговой инструкции для анализа и решения проблемы.

## Шаг 1: Подтверждение проблемы

1. **Проверьте логи парсера**:
   ```bash
   # Поиск ошибок
   cat logs/olx_parser.log | grep -i error

   # Поиск предупреждений
   cat logs/olx_parser.log | grep -i warning

   # Поиск всех проблемных записей
   cat logs/olx_parser.log | grep -i -E 'error|warn|exception|critical|fail'
   ```

   **Ручной просмотр логов**:
   ```bash
   # Просмотр последних 50 строк лога
   tail -n 50 logs/olx_parser.log

   # Просмотр лога в реальном времени
   tail -f logs/olx_parser.log

   # Просмотр лога с помощью текстового редактора
   nano logs/olx_parser.log
   ```

   Обратите внимание на ошибки и предупреждения, связанные с извлечением данных, например: `Не удалось извлечь` или `Не удалось получить`.

   **Примечание**: Если команда `grep -i error` не возвращает результатов, это хороший знак - в логах нет ошибок. В этом случае проверьте предупреждения.

2. **Запустите парсер в режиме отладки**:
   ```bash
   python -m src.presentation.cli.main parse --category apartments_sale --limit 1 --verbose
   ```
   Проанализируйте вывод на наличие ошибок при извлечении данных.

## Шаг 2: Анализ изменений на сайте OLX

1. **Откройте сайт OLX** и перейдите к объявлению, которое не удалось корректно обработать.

2. **Используйте инструменты разработчика браузера**:
   - Chrome: нажмите F12 или Ctrl+Shift+I (Cmd+Option+I на Mac)
   - Firefox: нажмите F12 или Ctrl+Shift+I (Cmd+Option+I на Mac)

3. **Исследуйте HTML-структуру**:
   - Используйте инструмент выбора элемента (значок курсора в инструментах разработчика)
   - Нажмите на элемент страницы, данные которого не удалось извлечь (например, цена, описание, телефон)
   - Изучите HTML-код и определите новые селекторы

4. **Запишите новые селекторы**:
   - CSS-селекторы (например, `div.price-section span.price`)
   - XPath-селекторы (например, `//div[@class='price-section']/span[@class='price']`)
   - data-атрибуты (например, `[data-testid='ad-price']`)

## Шаг 3: Локализация кода, требующего изменений

В проекте Parser_olx селекторы находятся в следующих файлах:

1. **Основные файлы с селекторами**:
   - `src/infrastructure/parsers/olx/utils/html_utils.py` - селекторы для заголовка, описания и параметров объявления
   - `src/infrastructure/parsers/olx/utils/price_utils.py` - селекторы для цены
   - `src/infrastructure/parsers/olx/utils/contact_utils.py` - селекторы для телефонов
   - `src/infrastructure/parsers/olx/utils/id_extractor.py` - селекторы для ID объявлений
   - `src/infrastructure/parsers/olx/utils/location_utils.py` - селекторы для местоположения
   - `src/infrastructure/parsers/olx/utils/tag_utils.py` - селекторы для тегов

2. **Найдите конкретные методы извлечения данных**:

   **Для описания** (`html_utils.py`):
   ```python
   def get_description(soup: BeautifulSoup) -> Optional[str]:
       # Пробуем разные селекторы для описания
       selectors = [
           'div.css-19duwlz',  # Новый селектор из примера
           'div[data-cy="ad_description"] div.css-19duwlz',  # Новый селектор с data-cy
           'div[data-testid="ad_description"] div.css-19duwlz',  # Новый селектор с data-testid
           'div[data-cy="ad_description"] div',  # Старый селектор
           'div[data-testid="ad_description"] div',  # Селектор с data-testid
       ]
   ```

   **Для параметров объявления** (`html_utils.py`):
   ```python
   def get_parameters(soup: BeautifulSoup) -> Dict[str, str]:
       # Новый основной селектор для блока параметров
       params_container = soup.select_one('div[data-testid="ad-parameters-container"]')
       # Альтернативные селекторы
       selectors = [
           'div[data-cy="ad-parameters-container"]',  # Альтернативный селектор с data-cy
           'div.css-41yf00 p',  # Селектор по классу
           'div[data-cy="ad_parameters"] p',  # Селектор по data-атрибуту
       ]
   ```

   **Для цены** (`price_utils.py`):
   ```python
   def extract_price_from_soup(soup: BeautifulSoup) -> Tuple[Optional[float], Optional[str]]:
       # Пробуем разные селекторы для цены
       selectors = [
           'h3.css-fqcbii',  # Новый селектор из примера
           'div[data-testid="ad-price-container"] h3',  # Новый селектор из примера
           'div[data-cy="ad-price-container"] h3',  # Еще один вариант селектора
       ]
   ```

   **Для телефонов** (`contact_utils.py`):
   ```python
   def extract_phones_from_html(html: str) -> List[str]:
       # Ищем телефоны в тексте ссылок и других элементах
       phone_selectors = [
           'a[data-testid="contact-phone"]',  # Основной селектор для телефона
           'span.css-1p15v7u a',              # Телефон в спане с иконкой (новый)
           'a.css-v1ndtc',                    # Телефон в ссылке (новый)
           'div[data-cy="ad_contact"] a',     # Телефон в блоке контактов
       ]
   ```

   **Для тегов** (`tag_utils.py`):
   ```python
   def extract_tags(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
       # Ищем блок по новому селектору data-testid="ad-parameters-container"
       details_block = soup.find('div', {'data-testid': 'ad-parameters-container'})

       if not details_block:
           # Альтернативные селекторы
           details_block = soup.find('div', {'data-cy': 'ad-parameters-container'})
           # Или
           details_block = soup.find('div', class_='css-41yf00')
           # Или
           details_block = soup.find('div', {'data-cy': 'ad_parameters'})

       # Ищем все параметры внутри блока - сначала ищем параграфы
       parameter_items = details_block.find_all('p', recursive=True)
   ```

   **Для ID объявлений в листинге** (`id_extractor.py`):
   ```python
   def extract_ids_from_listing_html(cls, html: str) -> List[str]:
       # Ищем все объявления на странице по разным селекторам
       ad_selectors = [
           'div[data-cy="l-card"]',           # Основной селектор для карточек объявлений
           'div.css-1sw7q4x',                 # Альтернативный селектор по классу
           'a[href*="/obyavlenie/"]',        # Поиск по ссылкам на объявления
           'div.offer-wrapper',               # Старый формат карточек
       ]
   ```

## Шаг 4: Обновление селекторов

1. **Откройте соответствующий файл утилиты** в редакторе, в зависимости от того, какие селекторы нужно обновить:
   ```bash
   # Для описания и параметров
   nano src/infrastructure/parsers/olx/utils/html_utils.py

   # Для цены
   nano src/infrastructure/parsers/olx/utils/price_utils.py

   # Для телефонов
   nano src/infrastructure/parsers/olx/utils/contact_utils.py

   # Для ID объявлений
   nano src/infrastructure/parsers/olx/utils/id_extractor.py

   # Для тегов
   nano src/infrastructure/parsers/olx/utils/tag_utils.py

   # Для местоположения
   nano src/infrastructure/parsers/olx/utils/location_utils.py
   ```

2. **Добавьте новые селекторы** в начало списка селекторов:

   **Пример для описания** (`html_utils.py`):
   ```python
   # Было:
   selectors = [
       'div.css-19duwlz',  # Новый селектор из примера
       'div[data-cy="ad_description"] div.css-19duwlz',  # Новый селектор с data-cy
       # ...
   ]

   # Стало (пример):
   selectors = [
       'div[data-testid="ad_description"] div.css-abcdef',  # Новый селектор с новым классом
       'div.css-19duwlz',  # Старый селектор
       'div[data-cy="ad_description"] div.css-19duwlz',  # Старый селектор с data-cy
       # ...
   ]
   ```

   **Пример для параметров** (`html_utils.py`):
   ```python
   # Было:
   params_container = soup.select_one('div[data-testid="ad-parameters-container"]')

   # Стало (пример):
   params_container = soup.select_one('div[data-testid="ad-parameters-container"]')
   if not params_container:
       params_container = soup.select_one('div[data-testid="ad-parameters"]')  # Новый селектор
   ```

   **Пример для телефонов** (`contact_utils.py`):
   ```python
   # Было:
   phone_selectors = [
       'a[data-testid="contact-phone"]',  # Основной селектор для телефона
       # ...
   ]

   # Стало (пример):
   phone_selectors = [
       'a[data-testid="phone-number"]',  # Новый селектор для телефона
       'a[data-testid="contact-phone"]',  # Старый селектор для телефона
       # ...
   ]
   ```

   **Пример для тегов** (`tag_utils.py`):
   ```python
   # Было:
   details_block = soup.find('div', {'data-testid': 'ad-parameters-container'})
   if not details_block:
       details_block = soup.find('div', {'data-cy': 'ad-parameters-container'})
       # ...

   # Стало (пример):
   details_block = soup.find('div', {'data-testid': 'ad-parameters-container'})
   if not details_block:
       details_block = soup.find('div', {'data-testid': 'parameters-container'})  # Новый селектор
       if not details_block:
           details_block = soup.find('div', {'data-cy': 'ad-parameters-container'})
           # ...
   ```

3. **Обновите все затронутые селекторы**:

   При обновлении селекторов следуйте этим правилам:

   - **Добавляйте новые селекторы в начало списка**, а не заменяйте старые. Это позволит парсеру работать с разными версиями сайта.

   - **Предпочитайте селекторы на основе data-атрибутов** (`data-testid`, `data-cy`), так как они меняются реже, чем классы CSS.

   - **Добавляйте комментарии с датой обновления** к новым селекторам, чтобы отслеживать изменения.

## Шаг 5: Тестирование изменений

1. **Запустите парсер для одного объявления**:
   ```bash
   python -m src.presentation.cli.main parse --category apartments_sale --limit 1
   ```

2. **Проверьте результаты**:
   - Убедитесь, что все данные извлекаются корректно
   - Проверьте, что телефонные номера извлекаются правильно

3. **Если проблемы остаются**, повторите шаги 2-4, уточняя селекторы.

## Шаг 6: Комплексное тестирование

1. **Запустите парсер для нескольких категорий**:
   ```bash
   python -m src.presentation.cli.main parse --category apartments_sale --limit 5
   python -m src.presentation.cli.main parse --category houses_sale --limit 5
   ```

2. **Проверьте результаты для разных типов объявлений**:
   - Объявления с разным количеством фотографий
   - Объявления с разным количеством параметров

## Шаг 7: Обновление документации

1. **Добавьте комментарии к коду** с описанием новых селекторов:
   ```python
   # Селектор для цены объявления (обновлен 2023-10-15)
   price_element = soup.select_one('div[data-testid="ad-price"] h3')
   ```

2. **Обновите документацию проекта**, если в ней описаны селекторы.

## Дополнительные рекомендации

1. **Используйте более устойчивые селекторы**:
   - Предпочитайте селекторы на основе `data-testid` или других data-атрибутов
   - Избегайте селекторов, основанных только на классах, так как они часто меняются

2. **Добавьте логирование** для отладки селекторов:
   ```python
   logger.debug(f"HTML элемента цены: {soup.select_one('div[data-testid=\"ad-price\"]')}")
   ```

3. **Рассмотрите возможность использования Zyte API** для более надежного парсинга, если проблемы с селекторами возникают часто.

4. **Настройте мониторинг** для раннего обнаружения проблем с парсингом:
   - Регулярно проверяйте количество успешно обработанных объявлений
   - Настройте оповещения при резком снижении количества извлеченных данных

## Текущие селекторы OLX (на момент создания документа)

Для справки, вот текущие селекторы, используемые в проекте для извлечения данных с OLX:

### Основные селекторы для страницы объявления

| Данные | Основной селектор | Файл |
|--------|----------|----------|
| Заголовок | `h4.css-10ofhqw` | `html_utils.py` |
| Описание | `div.css-19duwlz` | `html_utils.py` |
| Параметры | `div[data-testid="ad-parameters-container"]` | `html_utils.py` |
| Цена | `h3.css-fqcbii` | `price_utils.py` |
| Телефон | `a[data-testid="contact-phone"]` | `contact_utils.py` |
| Местоположение | `a[href*="/nedvizhimost"] p` | `location_utils.py` |
| ID объявления | `span.css-w85dhy` | `id_extractor.py` |
| Теги | `div[data-testid="ad-parameters-container"] p` | `tag_utils.py` |

### Селекторы для списка объявлений

| Данные | Селектор | Файл |
|--------|----------|----------|
| Карточка объявления | `div[data-cy="l-card"]` | `id_extractor.py` |
| Ссылка на объявление | `a[href*="/obyavlenie/"]` | `id_extractor.py` |

### Альтернативные селекторы

В проекте используется механизм резервных селекторов, которые применяются, если основной селектор не сработал. Например, для описания:

```python
# Альтернативные селекторы для описания
'div[data-cy="ad_description"] div.css-19duwlz'
'div[data-testid="ad_description"] div.css-19duwlz'
'div[data-cy="ad_description"] div'
'div[data-testid="ad_description"] div'
```

Следуя этой инструкции, вы сможете быстро выявить и исправить проблемы с селекторами на сайте OLX, обеспечив стабильную работу парсера даже при изменениях на сайте.
