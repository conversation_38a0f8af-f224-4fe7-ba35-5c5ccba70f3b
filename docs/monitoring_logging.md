# Мониторинг и логирование

В этом документе описаны инструкции по настройке и использованию мониторинга и логирования для проекта Parser OLX.

## Цели мониторинга и логирования

1. **Отслеживание работы парсера** - мониторинг работы парсера для выявления проблем и ошибок.
2. **Сбор статистики** - сбор статистики о работе парсера для анализа производительности.
3. **Отладка** - логирование информации для отладки проблем.
4. **Уведомления** - отправка уведомлений о критических ошибках и проблемах.

## Инструменты для мониторинга и логирования

Для мониторинга и логирования проекта Parser OLX используются следующие инструменты:

1. **Python logging** - встроенная библиотека для логирования в Python.
2. **Sentry** - сервис для мониторинга ошибок и исключений.
3. **Prometheus** - система мониторинга и сбора метрик.
4. **Grafana** - платформа для визуализации метрик и создания дашбордов.

## Настройка логирования

### Установка зависимостей

```bash
pip install python-json-logger sentry-sdk prometheus-client
```

### Конфигурация логирования

Логирование настраивается в файле `src/config/logging_config.py`:

```python
import logging
import os
from logging.handlers import RotatingFileHandler

import sentry_sdk
from pythonjsonlogger import jsonlogger

# Настройка Sentry
sentry_sdk.init(
    dsn=os.getenv("SENTRY_DSN", ""),
    traces_sample_rate=1.0,
    environment=os.getenv("ENVIRONMENT", "development"),
)

# Настройка логирования
def setup_logging(log_level=logging.INFO, log_file=None):
    """Настройка логирования."""
    logger = logging.getLogger("parser_olx")
    logger.setLevel(log_level)
    
    # Очистка обработчиков
    if logger.handlers:
        logger.handlers.clear()
    
    # Форматтер для консоли
    console_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Обработчик для консоли
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Форматтер для JSON
    json_formatter = jsonlogger.JsonFormatter(
        "%(asctime)s %(name)s %(levelname)s %(message)s %(filename)s %(funcName)s %(lineno)d"
    )
    
    # Обработчик для файла
    if log_file:
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(json_formatter)
        logger.addHandler(file_handler)
    
    return logger
```

### Использование логирования

Пример использования логирования в коде:

```python
from src.config.logging_config import setup_logging

logger = setup_logging(log_file="logs/parser.log")

def parse_url(url):
    """Парсинг URL."""
    logger.info(f"Начало парсинга URL: {url}")
    try:
        # Код парсинга
        logger.info(f"Успешно спарсен URL: {url}")
    except Exception as e:
        logger.error(f"Ошибка при парсинге URL: {url}", exc_info=True)
        raise
```

## Настройка мониторинга с Prometheus

### Конфигурация Prometheus

Создайте файл `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'parser_olx'
    static_configs:
      - targets: ['localhost:8000']
```

### Настройка метрик Prometheus

Создайте файл `src/monitoring/metrics.py`:

```python
from prometheus_client import Counter, Gauge, Histogram, Summary

# Счетчики
PARSED_URLS = Counter('parsed_urls_total', 'Total number of parsed URLs')
FAILED_URLS = Counter('failed_urls_total', 'Total number of failed URLs')
PROCESSED_PROPERTIES = Counter('processed_properties_total', 'Total number of processed properties')
SENT_NOTIFICATIONS = Counter('sent_notifications_total', 'Total number of sent notifications')

# Гистограммы
PARSING_TIME = Histogram('parsing_time_seconds', 'Time spent parsing URLs')
NOTIFICATION_TIME = Histogram('notification_time_seconds', 'Time spent sending notifications')

# Измерители
ACTIVE_PARSERS = Gauge('active_parsers', 'Number of active parsers')
QUEUE_SIZE = Gauge('queue_size', 'Size of the parsing queue')

# Сводки
PROPERTY_PRICE = Summary('property_price_dollars', 'Property price in dollars')
```

### Использование метрик Prometheus

Пример использования метрик в коде:

```python
from src.monitoring.metrics import PARSED_URLS, FAILED_URLS, PARSING_TIME, ACTIVE_PARSERS

def parse_url(url):
    """Парсинг URL."""
    ACTIVE_PARSERS.inc()
    try:
        with PARSING_TIME.time():
            # Код парсинга
            pass
        PARSED_URLS.inc()
    except Exception as e:
        FAILED_URLS.inc()
        raise
    finally:
        ACTIVE_PARSERS.dec()
```

### Запуск Prometheus

```bash
docker run -d --name prometheus -p 9090:9090 -v /path/to/prometheus.yml:/etc/prometheus/prometheus.yml prom/prometheus
```

## Настройка Grafana

### Запуск Grafana

```bash
docker run -d --name grafana -p 3000:3000 grafana/grafana
```

### Настройка дашборда Grafana

1. Откройте Grafana по адресу http://localhost:3000 (логин: admin, пароль: admin).
2. Добавьте источник данных Prometheus (http://localhost:9090).
3. Создайте дашборд с панелями для метрик:
   - Количество спарсенных URL
   - Количество ошибок парсинга
   - Время парсинга
   - Количество отправленных уведомлений
   - Средняя цена недвижимости

## Примеры использования мониторинга и логирования

### Пример логирования в парсере

```python
def parse(self, url):
    """Парсинг объявления."""
    self._logger.info(f"Начало парсинга URL: {url}")
    try:
        html = self._get_html_with_zyte(url)
        self._logger.debug(f"Получен HTML для URL: {url}")
        
        property_obj = self._parse_html(html, url)
        self._logger.info(f"Успешно спарсен URL: {url}")
        
        return property_obj
    except Exception as e:
        self._logger.error(f"Ошибка при парсинге URL: {url}", exc_info=True)
        raise
```

### Пример использования метрик в сервисе

```python
def parse_and_notify(self, url):
    """Парсинг объявления и отправка уведомления."""
    ACTIVE_PARSERS.inc()
    try:
        with PARSING_TIME.time():
            property_obj = self._parser.parse(url)
        PARSED_URLS.inc()
        
        if property_obj:
            ad_id = property_obj.ad_id.value
            
            if not self._storage.is_processed(ad_id):
                with NOTIFICATION_TIME.time():
                    self._notification_service.send_property_notification(property_obj)
                SENT_NOTIFICATIONS.inc()
                
                if property_obj.price:
                    PROPERTY_PRICE.observe(property_obj.price.amount)
                
                self._storage.save_processed(ad_id)
                PROCESSED_PROPERTIES.inc()
                
                return True
    except Exception as e:
        FAILED_URLS.inc()
        raise
    finally:
        ACTIVE_PARSERS.dec()
    
    return False
```

## Рекомендации по мониторингу и логированию

1. **Используйте структурированное логирование** - логируйте информацию в структурированном формате (JSON) для удобства анализа.
2. **Логируйте контекст** - включайте в логи контекст (URL, ID объявления, и т.д.) для облегчения отладки.
3. **Используйте уровни логирования** - используйте разные уровни логирования (DEBUG, INFO, WARNING, ERROR) для разных типов сообщений.
4. **Настройте ротацию логов** - настройте ротацию логов для предотвращения переполнения диска.
5. **Мониторьте ключевые метрики** - мониторьте ключевые метрики (количество спарсенных URL, количество ошибок, время парсинга) для выявления проблем.
6. **Настройте алерты** - настройте алерты для уведомления о критических проблемах.
7. **Регулярно анализируйте логи и метрики** - регулярно анализируйте логи и метрики для выявления проблем и оптимизации работы парсера.
