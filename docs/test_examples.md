# Примеры тестов для различных компонентов системы

В этом документе приведены примеры тестов для различных компонентов системы Parser OLX.

## Тесты для Value Objects

### Пример теста для AdId

```python
import pytest
from src.domain.value_objects.ad_id import AdId

class TestAdId:
    def test_create_valid_ad_id(self):
        """Тест создания валидного AdId."""
        # Arrange & Act
        ad_id = AdId("12345678")
        
        # Assert
        assert ad_id.value == "12345678"
    
    def test_create_invalid_ad_id(self):
        """Тест создания невалидного AdId."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError):
            AdId("invalid")
    
    def test_equality(self):
        """Тест сравнения AdId."""
        # Arrange
        ad_id1 = AdId("12345678")
        ad_id2 = AdId("12345678")
        ad_id3 = AdId("87654321")
        
        # Assert
        assert ad_id1 == ad_id2
        assert ad_id1 != ad_id3
    
    def test_string_representation(self):
        """Тест строкового представления AdId."""
        # Arrange
        ad_id = AdId("12345678")
        
        # Act
        result = str(ad_id)
        
        # Assert
        assert result == "12345678"
```

### Пример теста для Price

```python
import pytest
from src.domain.value_objects.price import Price

class TestPrice:
    def test_create_valid_price(self):
        """Тест создания валидной цены."""
        # Arrange & Act
        price = Price(100, "USD")
        
        # Assert
        assert price.amount == 100
        assert price.currency == "USD"
    
    def test_create_invalid_price_negative_amount(self):
        """Тест создания невалидной цены с отрицательной суммой."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError):
            Price(-100, "USD")
    
    def test_create_invalid_price_invalid_currency(self):
        """Тест создания невалидной цены с некорректной валютой."""
        # Arrange & Act & Assert
        with pytest.raises(ValueError):
            Price(100, "INVALID")
    
    def test_equality(self):
        """Тест сравнения цен."""
        # Arrange
        price1 = Price(100, "USD")
        price2 = Price(100, "USD")
        price3 = Price(200, "USD")
        price4 = Price(100, "EUR")
        
        # Assert
        assert price1 == price2
        assert price1 != price3
        assert price1 != price4
    
    def test_string_representation(self):
        """Тест строкового представления цены."""
        # Arrange
        price = Price(100, "USD")
        
        # Act
        result = str(price)
        
        # Assert
        assert result == "100 USD"
```

## Тесты для Domain Services

### Пример теста для PropertyValidator

```python
import pytest
from src.domain.services.property_validator import PropertyValidator
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.price import Price
from src.domain.value_objects.address import Address

class TestPropertyValidator:
    def test_validate_property_valid(self):
        """Тест валидации корректного объекта недвижимости."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html",
        )
        
        # Act
        result = PropertyValidator.validate_property(property_obj)
        
        # Assert
        assert result is True
    
    def test_validate_property_invalid_price(self):
        """Тест валидации объекта недвижимости с некорректной ценой."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(0, "USD"),  # Некорректная цена
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html",
        )
        
        # Act
        result = PropertyValidator.validate_property(property_obj)
        
        # Assert
        assert result is False
```

## Тесты для Application Services

### Пример теста для PropertyService

```python
from unittest.mock import MagicMock, patch
import pytest
from src.application.services.property import PropertyService
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId

class TestPropertyService:
    @pytest.fixture
    def mock_parser(self):
        """Фикстура для создания мока парсера."""
        mock = MagicMock()
        return mock
    
    @pytest.fixture
    def mock_storage(self):
        """Фикстура для создания мока хранилища."""
        mock = MagicMock()
        return mock
    
    @pytest.fixture
    def mock_notification_service(self):
        """Фикстура для создания мока сервиса уведомлений."""
        mock = MagicMock()
        return mock
    
    @pytest.fixture
    def property_service(self, mock_parser, mock_storage, mock_notification_service):
        """Фикстура для создания сервиса недвижимости."""
        return PropertyService(
            parser=mock_parser,
            storage=mock_storage,
            notification_service=mock_notification_service
        )
    
    def test_parse_and_notify_new_property(self, property_service, mock_parser, mock_storage, mock_notification_service):
        """Тест парсинга и отправки уведомления о новом объекте недвижимости."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=MagicMock(),
            address=MagicMock(),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html",
        )
        mock_parser.parse.return_value = property_obj
        mock_storage.is_processed.return_value = False
        
        # Act
        result = property_service.parse_and_notify("https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html")
        
        # Assert
        assert result is True
        mock_parser.parse.assert_called_once_with("https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html")
        mock_storage.is_processed.assert_called_once_with("12345678")
        mock_storage.save_processed.assert_called_once()
        mock_notification_service.send_property_notification.assert_called_once_with(property_obj)
    
    def test_parse_and_notify_already_processed(self, property_service, mock_parser, mock_storage, mock_notification_service):
        """Тест парсинга и отправки уведомления о уже обработанном объекте недвижимости."""
        # Arrange
        mock_storage.is_processed.return_value = True
        
        # Act
        result = property_service.parse_and_notify("https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html")
        
        # Assert
        assert result is False
        mock_parser.parse.assert_not_called()
        mock_storage.save_processed.assert_not_called()
        mock_notification_service.send_property_notification.assert_not_called()
```

## Тесты для Infrastructure

### Пример теста для JsonStorage

```python
import os
import json
import pytest
from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd

class TestJsonStorage:
    @pytest.fixture
    def storage_path(self):
        """Фикстура для создания пути к тестовому хранилищу."""
        return "tests/data/test_storage.json"
    
    @pytest.fixture
    def storage(self, storage_path):
        """Фикстура для создания тестового хранилища."""
        storage = JsonStorage(storage_path)
        yield storage
        # Cleanup
        if os.path.exists(storage_path):
            os.remove(storage_path)
    
    def test_save_and_load(self, storage):
        """Тест сохранения и загрузки объекта из хранилища."""
        # Arrange
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at="2023-01-01")
        
        # Act
        storage.save(processed_ad)
        loaded_ad = storage.load(ad_id)
        
        # Assert
        assert loaded_ad is not None
        assert loaded_ad.ad_id == ad_id
        assert loaded_ad.processed_at == "2023-01-01"
    
    def test_load_nonexistent(self, storage):
        """Тест загрузки несуществующего объекта из хранилища."""
        # Act
        loaded_ad = storage.load("nonexistent")
        
        # Assert
        assert loaded_ad is None
    
    def test_is_processed(self, storage):
        """Тест проверки, был ли объект обработан."""
        # Arrange
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at="2023-01-01")
        storage.save(processed_ad)
        
        # Act
        result = storage.is_processed(ad_id)
        
        # Assert
        assert result is True
    
    def test_is_processed_nonexistent(self, storage):
        """Тест проверки, был ли несуществующий объект обработан."""
        # Act
        result = storage.is_processed("nonexistent")
        
        # Assert
        assert result is False
```

### Пример теста для OlxParser

```python
from unittest.mock import MagicMock, patch
import pytest
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.olx_parser import OlxParser

class TestOlxParser:
    @pytest.fixture
    def parser(self):
        """Фикстура для создания парсера."""
        return OlxParser()
    
    @pytest.fixture
    def sample_html(self):
        """Фикстура для создания тестового HTML."""
        return """
        <html>
            <head>
                <meta property="og:title" content="Тестовая квартира">
                <meta property="og:description" content="Описание тестовой квартиры">
            </head>
            <body>
                <h1 class="css-1soizd2">Тестовая квартира</h1>
                <div data-cy="ad_description">
                    <div>Описание тестовой квартиры</div>
                </div>
                <div class="css-1epmoz1">
                    <p>Цена: 50 000 USD</p>
                    <p>Город: Киев</p>
                    <p>Район: Печерский</p>
                </div>
                <a href="/nedvizhimost/kvartiry/prodazha/">
                    <p>Киев, Печерский</p>
                </a>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "id": "12345678",
                            "map": {
                                "lat": 50.4501,
                                "lon": 30.5234
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """
    
    def test_parse(self, parser, sample_html):
        """Тест парсинга объявления."""
        # Arrange
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html"
        
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.text = sample_html
            mock_get.return_value = mock_response
            
            # Act
            property_obj = parser.parse(url)
            
            # Assert
            assert property_obj is not None
            assert property_obj.ad_id.value == "12345678"
            assert property_obj.title == "Тестовая квартира"
            assert property_obj.description == "Описание тестовой квартиры"
            assert property_obj.url == url
```

## Тесты для Presentation

### Пример теста для PropertyBot

```python
from unittest.mock import MagicMock, patch
import pytest
from telegram import Update
from telegram.ext import CallbackContext
from src.presentation.telegram.bot import PropertyBot

class TestPropertyBot:
    @pytest.fixture
    def mock_property_service(self):
        """Фикстура для создания мока сервиса недвижимости."""
        mock = MagicMock()
        return mock
    
    @pytest.fixture
    def mock_telegram_sender(self):
        """Фикстура для создания мока отправителя Telegram."""
        mock = MagicMock()
        return mock
    
    @pytest.fixture
    def property_bot(self, mock_property_service, mock_telegram_sender):
        """Фикстура для создания бота недвижимости."""
        return PropertyBot(
            token="test_token",
            allowed_user_ids=[123456789],
            property_service=mock_property_service,
            telegram_sender=mock_telegram_sender
        )
    
    @pytest.fixture
    def mock_update(self):
        """Фикстура для создания мока объекта Update."""
        mock = MagicMock()
        mock.effective_chat.id = 123456789
        mock.message.text = "/start"
        return mock
    
    @pytest.fixture
    def mock_context(self):
        """Фикстура для создания мока объекта CallbackContext."""
        mock = MagicMock()
        return mock
    
    def test_handle_start(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /start."""
        # Arrange
        with patch('src.presentation.telegram.bot.handle_start') as mock_handle_start:
            # Act
            property_bot._handle_start(mock_update, mock_context)
            
            # Assert
            mock_handle_start.assert_called_once_with(mock_update, mock_context, property_bot)
    
    def test_handle_help(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /help."""
        # Arrange
        with patch('src.presentation.telegram.bot.handle_help') as mock_handle_help:
            # Act
            property_bot._handle_help(mock_update, mock_context)
            
            # Assert
            mock_handle_help.assert_called_once_with(mock_update, mock_context, property_bot)
```

## Интеграционные тесты

### Пример интеграционного теста для OlxParser

```python
import os
import pytest
from src.infrastructure.parsers.olx.olx_parser import OlxParser

class TestOlxParserIntegration:
    @pytest.fixture
    def parser(self):
        """Фикстура для создания парсера."""
        return OlxParser()
    
    @pytest.fixture
    def sample_html_path(self):
        """Фикстура для создания пути к тестовому HTML-файлу."""
        return "tests/data/sample_ad.html"
    
    def test_parse_from_file(self, parser, sample_html_path):
        """Тест парсинга объявления из файла."""
        # Arrange
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-**********.html"
        
        # Проверяем, что файл существует
        assert os.path.exists(sample_html_path)
        
        with open(sample_html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Act
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.text = html_content
            mock_get.return_value = mock_response
            
            property_obj = parser.parse(url)
        
        # Assert
        assert property_obj is not None
        assert property_obj.ad_id is not None
        assert property_obj.title is not None
        assert property_obj.url == url
```

## Рекомендации по написанию тестов

1. **Следуйте принципу AAA (Arrange-Act-Assert)**:
   - Arrange: подготовка данных и объектов для теста
   - Act: выполнение тестируемого действия
   - Assert: проверка результатов

2. **Используйте фикстуры pytest** для повторного использования кода настройки тестов.

3. **Изолируйте тесты** - каждый тест должен быть независимым от других тестов.

4. **Используйте моки и стабы** для имитации зависимостей.

5. **Тестируйте граничные случаи** - пустые значения, отрицательные числа, очень большие числа и т.д.

6. **Используйте параметризованные тесты** для тестирования различных входных данных.

7. **Пишите тесты до или одновременно с кодом** - это поможет улучшить дизайн кода.

8. **Регулярно запускайте тесты** - это поможет обнаружить проблемы на ранней стадии.
