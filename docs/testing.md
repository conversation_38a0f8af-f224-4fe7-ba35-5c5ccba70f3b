# Тестирование проекта Parser OLX

В этом документе описаны инструкции по запуску тестов и анализу покрытия кода тестами для проекта Parser OLX.

## Дополнительная документация по тестированию

- [Рекомендации по написанию тестов](test_guidelines.md) - общие принципы и рекомендации по написанию тестов
- [Примеры тестов](test_examples.md) - примеры тестов для различных компонентов системы
- [Анализ покрытия кода тестами](code_coverage.md) - инструкции по анализу покрытия кода тестами
- [Отчет о покрытии кода тестами](coverage_report.md) - текущий отчет о покрытии кода тестами

## Структура тестов

Тесты в проекте разделены на две категории:

1. **Модульные тесты (Unit Tests)** - тестируют отдельные компоненты системы в изоляции.
2. **Интеграционные тесты (Integration Tests)** - тестируют взаимодействие между компонентами системы.

Структура директорий тестов:

```
tests/
├── unit/                  # Модульные тесты
│   ├── domain/            # Тесты для доменного слоя
│   ├── application/       # Тесты для слоя приложения
│   ├── infrastructure/    # Тесты для инфраструктурного слоя
│   └── presentation/      # Тесты для слоя представления
└── integration/           # Интеграционные тесты
    ├── parsers/           # Тесты для парсеров
    └── storage/           # Тесты для хранилища
```

## Запуск тестов

### Установка зависимостей для тестирования

```bash
pip install pytest pytest-cov pytest-mock
```

### Запуск всех тестов

```bash
pytest
```

### Запуск модульных тестов

```bash
pytest tests/unit/
```

### Запуск интеграционных тестов

```bash
pytest tests/integration/
```

### Запуск тестов для конкретного модуля

```bash
# Тесты для доменного слоя
pytest tests/unit/domain/

# Тесты для парсера OLX
pytest tests/unit/infrastructure/parsers/olx/

# Тесты для Telegram бота
pytest tests/unit/presentation/telegram/
```

### Запуск конкретного теста

```bash
pytest tests/unit/domain/value_objects/test_price.py::TestPrice::test_create_price
```

## Анализ покрытия кода тестами

### Запуск тестов с отчетом о покрытии

```bash
pytest --cov=src
```

### Генерация HTML-отчета о покрытии

```bash
pytest --cov=src --cov-report=html
```

После выполнения этой команды будет создана директория `htmlcov`, в которой будет находиться HTML-отчет о покрытии кода тестами. Откройте файл `htmlcov/index.html` в браузере для просмотра отчета.

### Генерация XML-отчета о покрытии для Codecov

```bash
pytest --cov=src --cov-report=xml
```

## Написание тестов

### Модульные тесты

Модульные тесты должны тестировать отдельные компоненты системы в изоляции. Для этого используйте моки и стабы для имитации зависимостей.

Пример модульного теста:

```python
import pytest
from unittest.mock import MagicMock
from src.domain.value_objects.price import Price

class TestPrice:
    def test_create_price(self):
        # Arrange
        amount = 100
        currency = "USD"

        # Act
        price = Price(amount, currency)

        # Assert
        assert price.amount == amount
        assert price.currency == currency
```

### Интеграционные тесты

Интеграционные тесты должны тестировать взаимодействие между компонентами системы. Для этого используйте реальные зависимости или их близкие имитации.

Пример интеграционного теста:

```python
import pytest
import os
from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd

class TestJsonStorage:
    @pytest.fixture
    def storage_path(self):
        return "tests/data/test_storage.json"

    @pytest.fixture
    def storage(self, storage_path):
        storage = JsonStorage(storage_path)
        yield storage
        # Cleanup
        if os.path.exists(storage_path):
            os.remove(storage_path)

    def test_save_and_load(self, storage):
        # Arrange
        ad_id = "12345"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at="2023-01-01")

        # Act
        storage.save(processed_ad)
        loaded_ad = storage.load(ad_id)

        # Assert
        assert loaded_ad is not None
        assert loaded_ad.ad_id == ad_id
```

## Рекомендации по тестированию

1. **Стремитесь к высокому покрытию кода тестами** - цель 80% и выше.
2. **Следуйте принципу AAA (Arrange-Act-Assert)** - подготовка данных, выполнение действия, проверка результата.
3. **Используйте фикстуры pytest** для повторного использования кода настройки тестов.
4. **Изолируйте тесты** - каждый тест должен быть независимым от других тестов.
5. **Тестируйте граничные случаи** - пустые значения, отрицательные числа, очень большие числа и т.д.
6. **Используйте параметризованные тесты** для тестирования различных входных данных.
7. **Пишите тесты до или одновременно с кодом** - это поможет улучшить дизайн кода.
8. **Регулярно запускайте тесты** - это поможет обнаружить проблемы на ранней стадии.
