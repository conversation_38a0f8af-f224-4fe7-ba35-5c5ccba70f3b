# Руководство по развертыванию на Cloud66

Это руководство описывает процесс развертывания Parser OLX на платформе Cloud66.

## Предварительные требования

1. Аккаунт на [Cloud66](https://app.cloud66.com/users/sign_up)
2. Доступ к Git-репозиторию проекта
3. Аккаунт у облачного провайдера (AWS, DigitalOcean, GCP и т.д.)
4. Переменные окружения (см. файл `.env.example`)

## Шаги по развертыванию

### 1. Подготовка репозитория

Убедитесь, что в вашем репозитории есть следующие файлы:
- `Dockerfile` - инструкции для сборки Docker-образа
- `service.yml` - конфигурация сервисов для Cloud66
- `manifest.yml` - дополнительная конфигурация для Cloud66
- `requirements.txt` - зависимости Python

### 2. Создание нового приложения на Cloud66

1. Войдите в свой аккаунт Cloud66
2. Нажмите "New Application" (Новое приложение)
3. Выберите "Deploy your own code" (Развернуть свой код)
4. Укажите URL вашего Git-репозитория: `https://github.com/Vnedrenec/Parser_olx.git`
5. Выберите ветку (обычно `main`)
6. Нажмите "Analyze" (Анализировать)

### 3. Настройка приложения

После анализа кода Cloud66 предложит настройки для вашего приложения:

1. **Выбор сервисов**:
   - Убедитесь, что выбраны сервисы `scheduler`, `telegram-bot` и `metrics` (если нужен)
   - При необходимости добавьте дополнительные сервисы

2. **Настройка переменных окружения**:
   - `TELEGRAM_BOT_TOKEN` - токен вашего Telegram бота
   - `TELEGRAM_SUPER_ADMIN_ID` - ID суперадминистратора в Telegram
   - `ZYTE_API_KEY` - ключ API для Zyte (если используется)
   - Другие необходимые переменные из файла `.env.example`

3. **Настройка томов**:
   - `/app/data` - для хранения данных
   - `/app/logs` - для хранения логов

### 4. Выбор облачного провайдера и сервера

1. Выберите вашего облачного провайдера (AWS, DigitalOcean, GCP и т.д.)
2. Выберите регион, ближайший к вашим пользователям
3. Выберите размер сервера (рекомендуется минимум 1 ГБ RAM)

### 5. Запуск развертывания

1. Проверьте все настройки
2. Нажмите "Deploy" (Развернуть)
3. Дождитесь завершения процесса развертывания

## Настройка после развертывания

### 1. Проверка работоспособности

1. Перейдите в раздел "Services" (Сервисы)
2. Проверьте статус каждого сервиса
3. Проверьте логи на наличие ошибок

### 2. Настройка мониторинга

1. Перейдите в раздел "Monitoring" (Мониторинг)
2. Настройте оповещения о проблемах с сервером или приложением

### 3. Настройка резервного копирования

1. Перейдите в раздел "Backups" (Резервные копии)
2. Настройте регулярное резервное копирование томов с данными

## Обновление приложения

Для обновления приложения после внесения изменений в код:

1. Внесите изменения в ваш Git-репозиторий
2. Отправьте изменения (push)
3. В Cloud66 перейдите к вашему приложению
4. Нажмите "Deploy" (Развернуть) для обновления

## Устранение неполадок

### Проблемы с доступом к OLX

Если OLX блокирует запросы с IP-адресов облачных провайдеров:

1. Настройте использование Zyte API в конфигурации парсера
2. Или настройте прокси-сервер для запросов

### Проблемы с Telegram ботом

Если Telegram бот не отвечает:

1. Проверьте правильность токена бота в переменных окружения
2. Проверьте логи на наличие ошибок подключения к API Telegram

### Проблемы с томами данных

Если данные не сохраняются между перезапусками:

1. Проверьте настройки томов в Cloud66
2. Проверьте права доступа к директориям данных и логов
