# Развертывание на Cloud66

Этот документ содержит инструкции по развертыванию Parser OLX на платформе Cloud66.

## Предварительные требования

1. Аккаунт на [Cloud66](https://cloud66.com)
2. Доступ к Git-репозиторию проекта
3. Аккаунт у облачного провайдера (AWS, DigitalOcean, GCP и т.д.)

## Шаги по развертыванию

### 1. Подготовка репозитория

Убедитесь, что в вашем репозитории есть следующие файлы:
- `Dockerfile` (в корне проекта)
- `requirements.txt` (список зависимостей)
- `config/` (директория с конфигурационными файлами)

### 2. Создание нового приложения на Cloud66

1. Войдите в свой аккаунт Cloud66
2. Нажмите "New Application" (Новое приложение)
3. Выберите "Deploy your own code" (Развернуть свой код)
4. Укажите URL вашего Git-репозитория
5. Выберите ветку (обычно `main` или `master`)
6. Нажмите "Analyze" (Анализировать)

### 3. Настройка приложения

После анализа кода Cloud66 предложит настройки для вашего приложения:

1. **Выбор сервисов**:
   - Убедитесь, что выбран сервис на основе вашего Dockerfile
   - При необходимости добавьте дополнительные сервисы (например, базу данных)

2. **Настройка переменных окружения**:
   - `TELEGRAM_BOT_TOKEN` - токен вашего Telegram бота
   - `ZYTE_API_KEY` - ключ API для Zyte (если используется)
   - `TELEGRAM_SUPER_ADMIN_ID` - ID суперадминистратора в Telegram
   - Другие необходимые переменные из вашего .env файла

3. **Настройка постоянных томов**:
   - `/app/data` - для хранения данных
   - `/app/logs` - для хранения логов

### 4. Выбор облачного провайдера и сервера

1. Выберите вашего облачного провайдера (AWS, DigitalOcean, GCP и т.д.)
2. Выберите регион, ближайший к вашим пользователям
3. Выберите размер сервера (рекомендуется минимум 1 ГБ RAM)

### 5. Запуск развертывания

1. Проверьте все настройки
2. Нажмите "Deploy" (Развернуть)
3. Дождитесь завершения процесса развертывания

## Настройка после развертывания

### 1. Настройка планировщика

Если вы используете планировщик для регулярного запуска парсера:

1. Перейдите в раздел "Toolbelt" (Инструменты)
2. Выберите "Scheduled Tasks" (Запланированные задачи)
3. Добавьте новую задачу для запуска планировщика

### 2. Настройка мониторинга

Cloud66 предоставляет базовый мониторинг, но вы также можете:

1. Настроить интеграцию с внешними сервисами мониторинга
2. Настроить оповещения о проблемах с сервером или приложением

### 3. Масштабирование (при необходимости)

Если вам нужно масштабировать приложение:

1. Перейдите в раздел "Servers" (Серверы)
2. Нажмите "Scale Up" (Масштабировать) для увеличения ресурсов сервера
3. Или нажмите "Scale Out" (Расширить) для добавления дополнительных серверов

## Обновление приложения

Для обновления приложения после внесения изменений в код:

1. Внесите изменения в ваш Git-репозиторий
2. Отправьте изменения (push)
3. В Cloud66 перейдите к вашему приложению
4. Нажмите "Deploy" (Развернуть) для обновления

## Устранение неполадок

### Проблемы с доступом к OLX

Если OLX блокирует запросы с IP-адресов облачных провайдеров:

1. Настройте использование Zyte API в конфигурации парсера
2. Или настройте прокси-сервер для запросов

### Проблемы с логами

Если логи не сохраняются или не ротируются:

1. Проверьте настройки томов для директории `/app/logs`
2. Проверьте права доступа к директории логов

### Проблемы с Telegram ботом

Если Telegram бот не отвечает:

1. Проверьте правильность токена бота в переменных окружения
2. Проверьте логи на наличие ошибок подключения к API Telegram
