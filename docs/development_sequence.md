# Последовательность Разработки (Упрощенный OLX Парсер)

Этот документ описывает рекомендуемый порядок разработки компонентов системы на основе упрощенной архитектуры, описанной в `docs/architecture/clean_architecture.md`.

## 1. Domain Layer (`src/domain/`)

Начните с ядра приложения – бизнес-логики и моделей.

1.  **Value Objects (`src/domain/value_objects/`)**:
    *   `ad_id.py`: Идентификатор объявления.
    *   `price.py`: Цена и валюта (с валидацией).
    *   `address.py`: Адрес объекта.
    *   `area.py`: Площадь (с валидацией).
    *   `contact.py`: Контактная информация.
    *   *(Другие необходимые VO)*: Создайте остальные Value Objects, которые будут использоваться для представления данных объявления в памяти.

2.  **Entities (`src/domain/entities/`)**:
    *   `property.py`: Класс `Property`, использующий созданные Value Objects. Определите его атрибуты. Так как он не хранится целиком в БД, ORM-маппинг здесь не нужен.

3.  **Domain Services (`src/domain/services/`)**:
    *   `property_validator.py`: Реализуйте `PropertyValidator` с бизнес-правилами для проверки объекта `Property` (в памяти).

4.  **Application Layer Interfaces (`src/application/interfaces/`)**:
    *   Определите интерфейсы (абстрактные базовые классы или протоколы), которые будут использоваться Application Layer для взаимодействия с Infrastructure Layer. На данном этапе они нужны Доменному слою для определения зависимостей (хотя сами файлы лежат в `application`).
    *   `processed_ad_storage.py`: Интерфейс `IProcessedAdStorage` (методы `add`, `exists`).
    *   `parser.py`: Интерфейс `IParser` (методы для получения списка ID и деталей объявления).
    *   `notification_sender.py`: Интерфейс `INotificationSender` (метод `send`).

## 2. Application Layer (`src/application/`)

Реализуйте логику приложения, оркестрирующую взаимодействие.

1.  **Interfaces (`src/application/interfaces/`)**:
    *   `notification_service.py`: Интерфейс `INotificationService` (если требуется абстракция).

2.  **Services (`src/application/services/`)**:
    *   `notification.py`: Реализуйте `NotificationService`, который использует `INotificationSender` для подготовки и отправки уведомлений.
    *   `property.py`: Реализуйте `PropertyService`. Это основной оркестратор:
        *   Использует `IParser` для получения ID.
        *   Использует `IProcessedAdStorage` для проверки ID.
        *   Использует `IParser` для получения деталей нового объявления.
        *   Создает `Property` (из `domain.entities`) в памяти.
        *   Использует `PropertyValidator` (из `domain.services`).
        *   Вызывает `NotificationService`.
        *   Использует `IProcessedAdStorage` для сохранения ID.

## 3. Infrastructure Layer (`src/infrastructure/`)

Реализуйте взаимодействие с внешним миром.

1.  **Storage (`src/infrastructure/storage/`)**:
    *   `json_storage.py`: Создайте простой класс для работы с JSON файлом, включающий:
        - Модель данных для хранения записей (`ad_id` и `processed_at`)
        - Методы для добавления и проверки ID объявлений
        - Инициализацию файла JSON при первом запуске

    Пример структуры `json_storage.py`:
    ```python
    class ProcessedAd:
        def __init__(self, ad_id: str, processed_at: datetime = None):
            self.ad_id = ad_id
            self.processed_at = processed_at or datetime.utcnow()

        def to_dict(self):
            return {
                "ad_id": self.ad_id,
                "processed_at": self.processed_at.isoformat()
            }

    class JsonStorage:
        def __init__(self, file_path: str):
            self.file_path = file_path
            self._ensure_file_exists()

        def _ensure_file_exists(self):
            # Создание файла, если он не существует
            if not os.path.exists(self.file_path):
                with open(self.file_path, 'w') as f:
                    json.dump([], f)

        def add(self, ad_id: str) -> None:
            # Добавление ID в JSON файл

        def exists(self, ad_id: str) -> bool:
            # Проверка существования ID в JSON файле
    ```

2.  **Persistence (`src/infrastructure/persistence/`)**:
    *   `processed_ad_storage.py`: Реализуйте `DbProcessedAdStorage`, имплементирующий `IProcessedAdStorage`, используя `JsonStorage` для работы с JSON файлом.

3.  **Logging (`src/infrastructure/logging/`)**:
    *   `logger.py`, `handlers/`: Настройте базовое логирование в файл и/или консоль.

4.  **Parsers (`src/infrastructure/parsers/olx/`)**:
    *   `utils/`: Начните реализацию утилит для извлечения данных:
        *   `id_extractor.py`
        *   `html_processor.py` (объединенный)
        *   `text_processors.py` (объединенный)
        *   `error_handling.py` (объединенный)
        *   `url_builder.py`
        *   `price_utils.py`, `area_utils.py`, `date_utils.py`, и т.д. (постепенно добавляя логику извлечения и первичной обработки/проверки формата).
    *   `olx_parser.py`: Реализуйте `OlxParser`, имплементирующий `IParser`. Используйте Zyte API и созданные утилиты для получения списка ID и деталей объявления.

5.  **Formatters (`src/infrastructure/formatters/`)**:
    *   `property_formatter.py`, `value_formatter.py`, `base_formatter.py`: Реализуйте форматтеры для преобразования данных, полученных парсером, в формат, удобный для создания доменных объектов `Property`.

6.  **Telegram (`src/infrastructure/telegram/`)**:
    *   `services/sender.py`: Реализуйте `TelegramNotificationSender`, имплементирующий `INotificationSender`, используя выбранную библиотеку и экземпляр бота из `presentation`.

## 4. Presentation Layer (`src/presentation/`)

Создайте точки входа и пользовательские интерфейсы.

1.  **Telegram (`src/presentation/telegram/`)**:
    *   `messages/templates.py`: Определите шаблоны сообщений.
    *   `messages/formatters.py`: Реализуйте форматирование данных `Property` для шаблонов, включая обработку отсутствующих полей.
    *   `bot.py`: Инициализируйте экземпляр бота с токеном из конфигурации.

2.  **Configuration (`src/config/` и `config/`)**:
    *   `src/config/settings.py`: Настройте загрузку конфигурации (например, с Pydantic).
    *   `config/`: Создайте и заполните конфигурационные файлы (`storage.yaml`, `logging.yaml`, `olx.yaml`, `telegram.yaml`, `environments/*.yaml`).
    *   `src/config/data/categories.json`: Заполните конфигурацию категорий для парсинга.

3.  **CLI (`src/presentation/cli/`)**:
    *   `commands/parse.py`: Создайте команду для запуска `PropertyService`.
    *   `main.py`: Настройте точку входа CLI (например, с использованием Typer или Click).

4.  **Scheduler (`src/presentation/scheduler/`)**:
    *   `tasks/parser_task.py`: Создайте задачу для периодического запуска `PropertyService`.
    *   `core/scheduler.py`: Настройте планировщик (например, APScheduler) для запуска задачи.

## 5. Тестирование (`tests/`)

Параллельно с разработкой каждого компонента пишите тесты:

*   **Unit-тесты:** Для Value Objects, Domain Services, Application Services, утилит парсера, форматтеров, хранилища ID (с моком БД).
*   **Интеграционные тесты:** Для `OlxParser` (с сохраненными HTML), `DbProcessedAdStorage` (с тестовой БД или in-memory SQLite), полного потока `PropertyService`.

Эта последовательность не является абсолютно строгой, но она помогает двигаться от стабильного ядра к изменяемым внешним деталям, следуя зависимостям Clean Architecture.

Правила разработки приложения:
1. Соблюдение принципов Clean Architecture
2. Разделение ответственности
3. Использование паттернов проектирования
4. Избегай зависимостей от конкретных реализаций (например, конкретных библиотек или фреймворков).
5. Избегай дублирования кода.
6. Используй соглашения об именах (например, snake_case для переменных и CamelCase для классов).
7. Проверяй директорию на наличие файлов перед созданием новых файлов.
8. Не удаляй код, если не уверен, что он больше не нужен.
9. Не изменяй код, если не уверен, что это не повредит приложение.
10. Думай стратегически, не бегом.
11. Правило организации структуры проекта
- `src` - исходный код
- `tests` - тесты
- `scripts` - скрипты
- `config` - конфигурационные файлы
- `docs` - документация
- `logs` - логи
- `data` - данные
- `build` - сборки и артефакты
- `temp` - временные файлы
Запрещено создание любых файлов в корневой директории, кроме:
- README.md
- .gitignore
- requirements.txt
- LICENSE
- Makefile
- Dockerfile
- CONTRIBUTING.md
- MANIFEST.in
