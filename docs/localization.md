# Локализация

В проекте реализована система локализации, которая позволяет переводить интерфейс на разные языки. В настоящее время поддерживаются русский и украинский языки.

## Архитектура

Система локализации реализована в соответствии с принципами чистой архитектуры и разделена на следующие слои:

### Доменный слой (Domain Layer)
- `src/domain/interfaces/localization_service.py` - Интерфейс сервиса локализации
- `src/domain/interfaces/user_language_storage.py` - Интерфейс хранилища языковых настроек пользователей
- `src/domain/value_objects/localization.py` - Модели для языков и локализованных строк

### Слой приложения (Application Layer)
- `src/application/services/localization.py` - Сервис локализации
- `src/application/services/user_language.py` - Сервис для управления языком пользователя
- `src/application/services/localized_notification.py` - Сервис для отправки локализованных уведомлений

### Слой инфраструктуры (Infrastructure Layer)
- `src/infrastructure/localization/yaml_localization_service.py` - Реализация сервиса локализации на основе YAML-файлов
- `src/infrastructure/persistence/user_language_storage.py` - Хранилище для языковых настроек пользователей
- `config/localization/ru.yaml` - Файл с локализованными строками на русском языке
- `config/localization/ua.yaml` - Файл с локализованными строками на украинском языке

### Слой представления (Presentation Layer)
- `src/presentation/telegram/localization/factory.py` - Фабрика для создания сервисов локализации
- `src/presentation/telegram/localization/language_handler.py` - Обработчик команды для выбора языка
- `src/presentation/telegram/localization/register.py` - Регистрация обработчиков команд для локализации
- `src/presentation/telegram/localization/notification_templates.py` - Класс для работы с локализованными шаблонами уведомлений
- `src/presentation/telegram/localization/notification_factory.py` - Фабрика для создания локализованного сервиса уведомлений

## Использование

### Инициализация сервисов локализации

```python
from src.presentation.telegram.localization.factory import create_localization_services

# Создание сервисов локализации
localization_service, user_language_service = create_localization_services()
```

### Регистрация обработчиков команд для локализации

```python
from src.presentation.telegram.localization.register import register_language_handlers

# Регистрация обработчиков команд для локализации
register_language_handlers(application, localization_service, user_language_service)
```

### Получение локализованных текстов

```python
# Получение языка пользователя
language = user_language_service.get_language(user_id)

# Получение локализованного текста
text = localization_service.get_text('main_menu_status', language)

# Получение локализованного шаблона
template = localization_service.get_template('command_start', language)

# Форматирование шаблона
text = localization_service.format_template(template, {'name': user_name})
```

### Управление языком пользователя

```python
# Получение языка пользователя
language = user_language_service.get_language(user_id)

# Установка языка пользователя
user_language_service.set_language(user_id, 'ua')

# Получение списка доступных языков
languages = user_language_service.get_available_languages()

# Проверка, установил ли пользователь язык явно
has_set_language = user_id in user_language_service._user_language_storage._languages
```

### Выбор языка при первом запуске бота

При первом запуске бота пользователю предлагается выбрать язык. Для этого используется следующий код:

```python
# Проверяем, первый ли это запуск бота для пользователя
is_first_launch = language == user_language_service._default_language and not user_has_set_language(user_id)

if is_first_launch:
    # Получаем локализованное приветственное сообщение на русском и украинском языках
    ru_welcome = localization_service.get_text('welcome_message', 'ru')
    ua_welcome = localization_service.get_text('welcome_message', 'ua')

    # Объединяем приветствия на обоих языках
    welcome_text = f"{ua_welcome}\n\n{ru_welcome}"

    # Отправляем меню выбора языка
    await send_message(welcome_text, get_localized_language_menu(localization_service, user_language_service, user_id))
```

### Локализованные уведомления

Для отправки локализованных уведомлений используется класс `LocalizedNotificationService`, который автоматически выбирает язык уведомления в зависимости от настроек пользователя.

#### Инициализация локализованного сервиса уведомлений

```python
from src.presentation.telegram.localization.notification_factory import create_localized_notification_service

# Создание локализованного сервиса уведомлений
notification_service = create_localized_notification_service(
    notification_sender=notification_sender,
    localization_service=localization_service,
    user_language_service=user_language_service,
    logger=logger
)
```

#### Отправка локализованных уведомлений

```python
# Отправка уведомления о новом объявлении
await notification_service.notify_new_property(property_obj, [user_id], "apartments_sale")

# Отправка уведомления об изменении цены
await notification_service.notify_price_changed(property_obj, old_price, new_price, [user_id])

# Отправка уведомления о статусе парсинга
await notification_service.notify_parsing_status(
    category="apartments_sale",
    new_count=10,
    processed_count=100,
    error_count=2,
    execution_time="00:05:30",
    chat_ids=[user_id]
)
```

## Файлы локализации

Файлы локализации находятся в директории `config/localization` и имеют формат YAML. Каждый файл содержит два раздела:

- `texts` - Простые тексты
- `templates` - Шаблоны сообщений, которые могут содержать параметры для форматирования

Пример файла локализации:

```yaml
texts:
  main_menu_status: "📊 Статус"
  main_menu_notifications: "🔔 Уведомления"
  main_menu_categories: "🏠 Категории"
  main_menu_help: "❓ Помощь"

templates:
  command_start: |
    Привет, {name}! Я бот для уведомлений о новых объявлениях OLX.

    Используйте команду /help для получения справки.
```

## Добавление новых языков

Для добавления нового языка необходимо:

1. Создать файл локализации в директории `config/localization` с именем, соответствующим коду языка (например, `en.yaml` для английского языка).
2. Добавить язык в список доступных языков в файле `src/presentation/telegram/localization/factory.py`:

```python
# Создаем список доступных языков
available_languages = [
    Language(code='ru', name='Русский', flag='🇷🇺'),
    Language(code='ua', name='Українська', flag='🇺🇦'),
    Language(code='en', name='English', flag='🇬🇧')  # Добавляем новый язык
]
```

## Добавление новых текстов и шаблонов

Для добавления новых текстов и шаблонов необходимо добавить их в соответствующие разделы файлов локализации:

```yaml
texts:
  # Добавляем новый текст
  new_text_key: "Новый текст"

  # Добавляем текст для приветственного сообщения при первом запуске
  welcome_message: "Привет! Добро пожаловать в бот! 👋\nВыберите язык:"

templates:
  # Добавляем новый шаблон
  new_template_key: |
    Шаблон с {placeholder}
```

Важно добавлять новые тексты и шаблоны во все файлы локализации, чтобы обеспечить поддержку всех языков. Например, для русского и украинского языков:

```yaml
# ru.yaml
texts:
  welcome_message: "Привет! Добро пожаловать в бот! 👋\nВыберите язык:"

# ua.yaml
texts:
  welcome_message: "Привіт! Вітаємо у боті! 👋\nОберіть мову:"
```

## Тестирование локализации

Для тестирования локализации можно использовать скрипт `tests/localization_test.py`:

```bash
python3 tests/localization_test.py
```

Скрипт проверяет:
- Получение списка доступных языков
- Получение текстов на разных языках
- Получение шаблонов на разных языках
- Форматирование шаблонов
- Управление языком пользователя
