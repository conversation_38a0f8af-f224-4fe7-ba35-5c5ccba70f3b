# Руководство по работе с Cloud66: Основные команды

## Содержание
1. [Введение](#введение)
2. [Установка и настройка CLI](#установка-и-настройка-cli)
3. [Управление стеками](#управление-стеками)
4. [Управление серверами](#управление-серверами)
5. [Управление сервисами](#управление-сервисами)
6. [Управление переменными окружения](#управление-переменными-окружения)
7. [Управление базами данных](#управление-базами-данных)
8. [Управление SSL-сертификатами](#управление-ssl-сертификатами)
9. [Мониторинг и логи](#мониторинг-и-логи)
10. [Резервное копирование](#резервное-копирование)
11. [Часто используемые сценарии](#часто-используемые-сценарии)

## Введение

Cloud66 - это платформа для развертывания и управления приложениями в облаке. Это руководство содержит основные команды для работы с Cloud66 через командную строку (CLI) и веб-интерфейс.

## Установка и настройка CLI

### Установка Cloud66 Toolbelt

```bash
# macOS (с использованием Homebrew)
brew install cloud66/tap/cx

# Linux
curl -sSL https://s3.amazonaws.com/downloads.cloud66.com/cx_install.sh | bash

# Windows (с использованием Chocolatey)
choco install cloud66-toolbelt
```

### Аутентификация

```bash
# Вход в аккаунт Cloud66
cx login

# Проверка текущего пользователя
cx account
```

## Управление стеками

### Основные команды для стеков

```bash
# Список всех стеков
cx stacks list

# Информация о конкретном стеке
cx stacks info -s "Parser_olx"

# Развертывание стека
cx stacks redeploy -s "Parser_olx" -e production

# Клонирование стека
cx stacks clone -s "Parser_olx" -n "Parser_olx_staging"

# Остановка стека
cx stacks stop -s "Parser_olx"

# Запуск стека
cx stacks start -s "Parser_olx"

# Удаление стека (с подтверждением)
cx stacks delete -s "Parser_olx"
```

## Управление серверами

### Команды для работы с серверами

```bash
# Список серверов в стеке
cx servers list -s "Parser_olx"

# Подключение к серверу по SSH
cx ssh -s "Parser_olx" -e production master

# Перезагрузка сервера
cx servers reboot -s "Parser_olx" -e production --server-name master

# Масштабирование серверов
cx servers scale -s "Parser_olx" -e production --group web --count 2

# Информация о сервере
cx servers info -s "Parser_olx" -e production --server-name master
```

## Управление сервисами

### Команды для работы с сервисами

```bash
# Список сервисов в стеке
cx services list -s "Parser_olx"

# Перезапуск сервиса
cx services restart -s "Parser_olx" -e production --service parser-olx

# Остановка сервиса
cx services stop -s "Parser_olx" -e production --service parser-olx

# Запуск сервиса
cx services start -s "Parser_olx" -e production --service parser-olx

# Просмотр логов сервиса
cx services logs -s "Parser_olx" -e production --service parser-olx

# Масштабирование сервиса
cx services scale -s "Parser_olx" -e production --service parser-olx --count 2
```

## Управление переменными окружения

### Команды для работы с переменными окружения

```bash
# Список всех переменных окружения
cx env-vars list -s "Parser_olx" -e production

# Добавление новой переменной
cx env-vars add -s "Parser_olx" -e production --name TZ --value "Europe/Kiev"

# Обновление существующей переменной
cx env-vars update -s "Parser_olx" -e production --name TZ --value "Europe/Kiev"

# Удаление переменной
cx env-vars delete -s "Parser_olx" -e production --name OLD_VAR

# Применение изменений переменных окружения
cx env-vars apply -s "Parser_olx" -e production
```

## Управление базами данных

### Команды для работы с базами данных

```bash
# Список баз данных
cx databases list -s "Parser_olx"

# Создание резервной копии базы данных
cx databases backup -s "Parser_olx" -e production --db mysql

# Восстановление базы данных из резервной копии
cx databases restore -s "Parser_olx" -e production --db mysql --backup-id 12345

# Подключение к базе данных
cx databases connect -s "Parser_olx" -e production --db mysql
```

## Управление SSL-сертификатами

### Команды для работы с SSL-сертификатами

```bash
# Список SSL-сертификатов
cx ssl list -s "Parser_olx"

# Добавление нового SSL-сертификата
cx ssl add -s "Parser_olx" -e production --domain example.com

# Обновление SSL-сертификата
cx ssl renew -s "Parser_olx" -e production --domain example.com

# Удаление SSL-сертификата
cx ssl remove -s "Parser_olx" -e production --domain example.com
```

## Мониторинг и логи

### Команды для мониторинга и работы с логами

```bash
# Просмотр логов стека
cx logs -s "Parser_olx" -e production

# Просмотр логов конкретного сервиса
cx logs -s "Parser_olx" -e production --service parser-olx

# Просмотр логов с фильтрацией
cx logs -s "Parser_olx" -e production --filter "error"

# Просмотр метрик сервера
cx metrics -s "Parser_olx" -e production --server-name master

# Проверка состояния здоровья сервисов
cx health -s "Parser_olx" -e production
```

## Управление конфигурацией

### Команды для работы с конфигурацией

```bash
# Просмотр текущей конфигурации
cx config list -s "Parser_olx" -e production

# Добавление новой конфигурации
cx config add -s "Parser_olx" -e production --key scheduler.enabled --value true

# Обновление существующей конфигурации
cx config update -s "Parser_olx" -e production --key scheduler.enabled --value false

# Удаление конфигурации
cx config delete -s "Parser_olx" -e production --key old.config

# Применение изменений конфигурации
cx config apply -s "Parser_olx" -e production
```

## Резервное копирование

### Команды для работы с резервными копиями

```bash
# Создание резервной копии стека
cx backups create -s "Parser_olx" -e production

# Список резервных копий
cx backups list -s "Parser_olx" -e production

# Восстановление из резервной копии
cx backups restore -s "Parser_olx" -e production --backup-id 12345

# Удаление резервной копии
cx backups delete -s "Parser_olx" -e production --backup-id 12345
```

## Часто используемые сценарии

### Обновление приложения

```bash
# 1. Подключение к серверу
cx ssh -s "Parser_olx" -e production master

# 2. Переход в директорию приложения
cd /var/deploy/Parser_olx/web_head/current

# 3. Обновление кода из репозитория
git pull origin main

# 4. Перезапуск сервисов
cx services restart -s "Parser_olx" -e production --service parser-olx
cx services restart -s "Parser_olx" -e production --service telegram-bot
```

### Проверка логов на ошибки

```bash
# Проверка логов на наличие ошибок
cx logs -s "Parser_olx" -e production --filter "error" --tail 100

# Проверка логов конкретного сервиса
cx logs -s "Parser_olx" -e production --service parser-olx --filter "error" --tail 100
```

### Обновление переменных окружения и перезапуск

```bash
# 1. Обновление переменной окружения
cx env-vars update -s "Parser_olx" -e production --name TELEGRAM_BOT_TOKEN --value "новый_токен"

# 2. Применение изменений
cx env-vars apply -s "Parser_olx" -e production

# 3. Перезапуск сервиса
cx services restart -s "Parser_olx" -e production --service telegram-bot
```

### Проверка состояния сервисов

```bash
# Проверка состояния всех сервисов
cx services list -s "Parser_olx" -e production

# Проверка использования ресурсов
cx servers info -s "Parser_olx" -e production --server-name master
```

---

**Примечание**: Замените "Parser_olx" на имя вашего стека, "production" на имя вашего окружения, и "master" на имя вашего сервера в соответствии с вашей конфигурацией Cloud66.

Для получения дополнительной информации о командах и параметрах используйте команду `cx help` или посетите [официальную документацию Cloud66](https://help.cloud66.com/references/toolbelt.html).
