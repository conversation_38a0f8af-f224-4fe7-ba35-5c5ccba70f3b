# Нагрузочное тестирование

В этом документе описаны инструкции по проведению нагрузочного тестирования для проекта Parser OLX.

## Цели нагрузочного тестирования

1. **Определение производительности** - измерение времени выполнения операций при различных нагрузках.
2. **Определение стабильности** - проверка стабильности работы системы при длительной нагрузке.
3. **Определение узких мест** - выявление компонентов системы, которые ограничивают производительность.
4. **Определение максимальной нагрузки** - определение максимального количества операций, которые система может обработать.

## Инструменты для нагрузочного тестирования

Для нагрузочного тестирования проекта Parser OLX используются следующие инструменты:

1. **Locust** - инструмент для нагрузочного тестирования веб-приложений.
2. **pytest-benchmark** - расширение для pytest, которое позволяет измерять производительность функций.
3. **cProfile** - встроенный в Python профилировщик для анализа производительности кода.

## Установка инструментов

```bash
pip install locust pytest-benchmark
```

## Нагрузочное тестирование с использованием pytest-benchmark

### Создание тестов производительности

Тесты производительности создаются с использованием pytest-benchmark. Пример теста производительности:

```python
def test_parse_performance(benchmark):
    """Тест производительности парсинга."""
    # Arrange
    parser = OlxParser()
    url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"

    # Act & Assert
    result = benchmark(parser.parse, url)

    assert result is not None
```

### Запуск тестов производительности

```bash
pytest tests/performance/ --benchmark-only
```

### Анализ результатов

После запуска тестов производительности pytest-benchmark выводит таблицу с результатами:

```
------------------------------------------------------------------------------------------ benchmark: 1 tests ------------------------------------------------------------------------------------------
Name (time in ms)                                 Min                Max               Mean            StdDev             Median               IQR            Outliers       OPS            Rounds  Iterations
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
test_parse_performance                        123.4567           234.5678          156.7890          12.3456           150.0000           10.0000           3;1         6.3778             10           1
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
```

## Нагрузочное тестирование с использованием Locust

### Создание сценариев нагрузочного тестирования

Сценарии нагрузочного тестирования создаются с использованием Locust. Пример сценария нагрузочного тестирования:

```python
from locust import HttpUser, task, between

class OlxUser(HttpUser):
    wait_time = between(1, 3)

    @task
    def parse_ad(self):
        self.client.get("/parse?url=https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html")
```

### Запуск нагрузочного тестирования

```bash
locust -f tests/performance/locustfile.py --host=http://localhost:8000
```

После запуска Locust открывается веб-интерфейс на http://localhost:8089, где можно настроить параметры нагрузочного тестирования и запустить его.

### Анализ результатов

Locust предоставляет веб-интерфейс для анализа результатов нагрузочного тестирования, включая графики и таблицы с различными метриками.

## Профилирование кода с использованием cProfile

### Профилирование функций

```python
import cProfile
import pstats

def profile_function(func, *args, **kwargs):
    """Профилирование функции."""
    profiler = cProfile.Profile()
    profiler.enable()
    result = func(*args, **kwargs)
    profiler.disable()
    stats = pstats.Stats(profiler).sort_stats('cumtime')
    stats.print_stats(20)
    return result

# Пример использования
parser = OlxParser()
profile_function(parser.parse, "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html")
```

### Анализ результатов

cProfile выводит таблицу с информацией о времени выполнения каждой функции:

```
         10000 function calls (9000 primitive calls) in 1.234 seconds

   Ordered by: cumulative time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    1.234    1.234 olx_parser.py:100(parse)
        1    0.000    0.000    1.000    1.000 requests.py:100(get)
        1    0.000    0.000    0.200    0.200 html_utils.py:100(parse_html)
```

## Рекомендации по проведению нагрузочного тестирования

1. **Проводите нагрузочное тестирование регулярно** - это позволит отслеживать изменения производительности системы.
2. **Тестируйте различные сценарии** - тестируйте различные операции и сценарии использования системы.
3. **Тестируйте с различными нагрузками** - тестируйте систему с различными уровнями нагрузки, от минимальной до максимальной.
4. **Анализируйте результаты** - анализируйте результаты нагрузочного тестирования и выявляйте узкие места.
5. **Оптимизируйте код** - оптимизируйте код на основе результатов нагрузочного тестирования.

## Примеры тестов производительности

### Тест производительности парсинга

```python
def test_parse_performance(benchmark):
    """Тест производительности парсинга."""
    # Arrange
    parser = OlxParser()
    url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"

    # Act & Assert
    result = benchmark(parser.parse, url)

    assert result is not None
```

### Тест производительности сохранения данных

```python
def test_save_performance(benchmark):
    """Тест производительности сохранения данных."""
    # Arrange
    storage = JsonStorage("tests/data/test_storage.json")
    processed_ad = ProcessedAd(ad_id="12345678", processed_at="2023-01-01")

    # Act & Assert
    benchmark(storage.save, processed_ad)
```

### Тест производительности отправки уведомлений

```python
def test_send_notification_performance(benchmark):
    """Тест производительности отправки уведомлений."""
    # Arrange
    notification_service = NotificationService(MagicMock())
    property_obj = Property(
        ad_id=AdId("12345678"),
        title="Тестовая квартира",
        description="Описание тестовой квартиры",
        price=Price(50000, "USD"),
        address=Address("Киев", "Печерский"),
        url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
    )

    # Act & Assert
    benchmark(notification_service.send_property_notification, property_obj)
```

## Результаты нагрузочного тестирования

Ниже приведены результаты нагрузочного тестирования утилит парсера OLX:

```
---------------------------------------------------------------------------------------------------------- benchmark: 6 tests ----------------------------------------------------------------------------------------------------------
Name (time in ns)                              Min                     Max                    Mean                 StdDev                  Median                    IQR            Outliers  OPS (Kops/s)            Rounds  Iterations
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
test_regex_performance                    500.0000 (1.0)        5,792.0000 (1.0)          555.3134 (1.0)          87.9094 (1.0)          542.0000 (1.0)          42.0000 (1.0)         40;40    1,800.7850 (1.0)        3893           1
test_get_title_performance             28,375.0000 (56.75)    260,375.0000 (44.95)     29,820.4141 (53.70)     5,043.1732 (57.37)     29,209.0000 (53.89)       709.0000 (16.88)     126;613       33.5341 (0.02)       6931           1
test_get_description_performance       43,792.0000 (87.58)    243,708.0000 (42.08)     46,131.9426 (83.07)     7,328.3865 (83.36)     44,625.0000 (82.33)     1,792.0000 (42.67)     105;224       21.6770 (0.01)       4405           1
test_extract_price_performance         46,333.0000 (92.67)    463,500.0000 (80.02)     48,046.2936 (86.52)     6,419.5544 (73.02)     47,083.0000 (86.87)     1,042.0000 (24.81)     192;808       20.8133 (0.01)       8621           1
test_extract_location_performance      80,583.0000 (161.17)   313,291.0000 (54.09)     83,439.0388 (150.26)    7,699.7928 (87.59)     81,542.0000 (150.45)    1,792.0000 (42.67)     147;385       11.9848 (0.01)       3630           1
test_parse_html_performance           322,500.0000 (645.00)   806,250.0000 (139.20)   348,408.6974 (627.41)   42,276.2526 (480.91)   331,562.5000 (611.74)   17,855.0000 (425.12)    158;374        2.8702 (0.00)       1824           1
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
```

Из результатов видно, что:

1. Самой быстрой операцией является регулярное выражение (555 нс).
2. Извлечение заголовка занимает около 30 мкс.
3. Извлечение описания и цены занимает около 45-48 мкс.
4. Извлечение местоположения занимает около 83 мкс.
5. Парсинг HTML является самой медленной операцией и занимает около 348 мкс.

Также было проведено профилирование кода с использованием cProfile, которое показало, что большая часть времени тратится на работу с CSS-селекторами в библиотеке soupsieve.

## Рекомендации по оптимизации

На основе результатов нагрузочного тестирования можно сделать следующие рекомендации по оптимизации парсера OLX:

1. **Оптимизация CSS-селекторов**:
   - Использовать более простые и специфичные CSS-селекторы.
   - Использовать кэширование селекторов для повторного использования.

2. **Кэширование результатов парсинга**:
   - Реализовать кэширование результатов парсинга для избежания повторного парсинга одних и тех же страниц.
   - Использовать временное хранилище для кэширования результатов парсинга.

3. **Параллельный парсинг**:
   - Реализовать параллельный парсинг с использованием многопоточности или асинхронности.
   - Использовать пул потоков для ограничения количества одновременных запросов.

4. **Оптимизация регулярных выражений**:
   - Предварительно компилировать регулярные выражения для повторного использования.
   - Использовать более эффективные регулярные выражения.

5. **Использование альтернативных парсеров**:
   - Рассмотреть возможность использования более быстрых парсеров HTML, таких как lxml.
   - Использовать XPath вместо CSS-селекторов для более быстрого извлечения данных.

Реализация этих рекомендаций позволит значительно улучшить производительность парсера OLX и уменьшить время обработки объявлений.
