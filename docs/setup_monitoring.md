# Настройка мониторинга и логирования

В этом документе описаны инструкции по установке и настройке мониторинга и логирования для проекта Parser OLX.

## Установка зависимостей

Для установки всех необходимых зависимостей выполните:

```bash
pip install -r requirements.txt
```

Или установите только зависимости для мониторинга и логирования:

```bash
pip install python-json-logger sentry-sdk prometheus-client locust
```

## Настройка логирования

### Настройка Sentry

1. Создайте аккаунт на [Sentry](https://sentry.io/) и создайте новый проект.
2. Получите DSN для вашего проекта.
3. Добавьте DSN в файл `.env`:

```
SENTRY_DSN=https://<EMAIL>/your-project-id
ENVIRONMENT=development  # или production
```

### Настройка логирования в файл

1. Создайте директорию для логов:

```bash
mkdir -p logs
```

2. Используйте функцию `setup_logging` из модуля `src.config.logging_config` для настройки логирования:

```python
from src.config.logging_config import setup_logging

# Настройка логирования
logger = setup_logging(log_file="logs/parser.log")

# Использование логгера
logger.info("Информационное сообщение")
logger.warning("Предупреждение")
logger.error("Ошибка", exc_info=True)
```

## Настройка мониторинга с Prometheus

### Запуск сервера метрик

1. Запустите сервер метрик:

```bash
python scripts/start_metrics_server.py
```

Сервер метрик будет доступен по адресу http://localhost:8000/metrics.

### Запуск Prometheus

1. Установите [Prometheus](https://prometheus.io/download/).
2. Создайте файл конфигурации `prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'parser_olx'
    static_configs:
      - targets: ['localhost:8000']
```

3. Запустите Prometheus:

```bash
prometheus --config.file=prometheus.yml
```

Prometheus будет доступен по адресу http://localhost:9090.

### Запуск Grafana

1. Установите [Grafana](https://grafana.com/grafana/download).
2. Запустите Grafana:

```bash
grafana-server
```

Grafana будет доступна по адресу http://localhost:3000.

3. Добавьте источник данных Prometheus (http://localhost:9090).
4. Создайте дашборд с панелями для метрик:
   - Количество спарсенных URL
   - Количество ошибок парсинга
   - Время парсинга
   - Количество отправленных уведомлений
   - Средняя цена недвижимости

## Запуск с Docker

### Запуск Prometheus и Grafana с Docker

1. Создайте файл `docker-compose.yml`:

```yaml
version: '3'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
    depends_on:
      - prometheus

volumes:
  grafana-storage:
```

2. Запустите контейнеры:

```bash
docker-compose up -d
```

## Использование метрик

### Доступные метрики

- `parsed_urls_total` - общее количество спарсенных URL
- `failed_urls_total` - общее количество ошибок парсинга
- `processed_properties_total` - общее количество обработанных объектов недвижимости
- `sent_notifications_total` - общее количество отправленных уведомлений
- `parsing_time_seconds` - время парсинга URL
- `notification_time_seconds` - время отправки уведомлений
- `active_parsers` - количество активных парсеров
- `queue_size` - размер очереди парсинга
- `property_price_dollars` - цена недвижимости в долларах

### Пример использования метрик

```python
from src.monitoring.metrics import PARSED_URLS, FAILED_URLS, PARSING_TIME

# Увеличение счетчика
PARSED_URLS.inc()

# Измерение времени выполнения
with PARSING_TIME.time():
    # Код, время выполнения которого нужно измерить
    result = parser.parse(url)
```

## Нагрузочное тестирование с Locust

### Запуск нагрузочного тестирования

1. Создайте файл `locustfile.py`:

```python
from locust import HttpUser, task, between

class OlxUser(HttpUser):
    wait_time = between(1, 3)
    
    @task
    def parse_ad(self):
        self.client.get("/parse?url=https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html")
```

2. Запустите Locust:

```bash
locust -f locustfile.py --host=http://localhost:8000
```

Locust будет доступен по адресу http://localhost:8089.

## Рекомендации по мониторингу и логированию

1. **Используйте структурированное логирование** - логируйте информацию в структурированном формате (JSON) для удобства анализа.
2. **Логируйте контекст** - включайте в логи контекст (URL, ID объявления, и т.д.) для облегчения отладки.
3. **Используйте уровни логирования** - используйте разные уровни логирования (DEBUG, INFO, WARNING, ERROR) для разных типов сообщений.
4. **Настройте ротацию логов** - настройте ротацию логов для предотвращения переполнения диска.
5. **Мониторьте ключевые метрики** - мониторьте ключевые метрики (количество спарсенных URL, количество ошибок, время парсинга) для выявления проблем.
6. **Настройте алерты** - настройте алерты для уведомления о критических проблемах.
7. **Регулярно анализируйте логи и метрики** - регулярно анализируйте логи и метрики для выявления проблем и оптимизации работы парсера.
