# Настройка CI/CD для проекта Parser OLX

В этом документе описаны шаги по настройке непрерывной интеграции и доставки (CI/CD) для проекта Parser OLX с использованием GitHub Actions.

## Настройка GitHub Actions

### 1. Создание репозитория на GitHub

1. Создайте новый репозиторий на GitHub.
2. Инициализируйте Git в локальном проекте (если еще не сделано):
   ```bash
   git init
   ```
3. Добавьте удаленный репозиторий:
   ```bash
   git remote add origin https://github.com/yourusername/parser_olx.git
   ```

### 2. Настройка секретов в репозитории

Для работы CI/CD необходимо добавить секреты в настройках репозитория:

1. Перейдите в настройки репозитория на GitHub: `Settings > Secrets and variables > Actions`.
2. Добавьте следующие секреты:
   - `TELEGRAM_BOT_TOKEN` - токен Telegram бота
   - `ZYTE_API_KEY` - ключ API Zyte

### 3. Настройка GitHub Actions Workflow

В проекте уже созданы файлы конфигурации для GitHub Actions:

- `.github/workflows/tests.yml` - запуск тестов и анализ покрытия кода
- `.github/workflows/codeql-analysis.yml` - анализ безопасности кода

### 4. Настройка Codecov

1. Зарегистрируйтесь на [Codecov](https://codecov.io/).
2. Добавьте ваш репозиторий в Codecov.
3. Получите токен Codecov и добавьте его в секреты репозитория как `CODECOV_TOKEN`.

## Первый коммит и проверка CI/CD

1. Добавьте все файлы в индекс Git:
   ```bash
   git add .
   ```

2. Создайте первый коммит:
   ```bash
   git commit -m "Initial commit with CI/CD setup"
   ```

3. Отправьте изменения в удаленный репозиторий:
   ```bash
   git push -u origin main
   ```

4. Проверьте, что GitHub Actions запустились и тесты выполняются успешно.

## Дополнительные настройки

### Настройка Dependabot

В проекте уже создан файл конфигурации для Dependabot:

- `.github/dependabot.yml` - автоматическое обновление зависимостей

### Настройка шаблона Pull Request

В проекте уже создан шаблон для Pull Request:

- `.github/PULL_REQUEST_TEMPLATE.md` - шаблон для создания Pull Request

## Рекомендации по работе с CI/CD

1. **Всегда запускайте тесты локально перед отправкой изменений в репозиторий.**
2. **Следите за отчетами о покрытии кода тестами и стремитесь к увеличению покрытия.**
3. **Исправляйте проблемы безопасности, обнаруженные CodeQL.**
4. **Регулярно обновляйте зависимости с помощью Dependabot.**
5. **Используйте ветки и Pull Request для внесения изменений в проект.**
