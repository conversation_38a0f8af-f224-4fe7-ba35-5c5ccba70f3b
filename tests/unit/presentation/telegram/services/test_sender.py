"""
Тесты для TelegramSender.
"""
import logging
import threading
import time
from unittest.mock import MagicMock, patch

import pytest
from telegram import Bot

from src.application.services.property import PropertyService
from src.presentation.telegram.bot import PropertyBot
from src.presentation.telegram.services.sender import TelegramSender


class TestTelegramSender:
    """Тесты для TelegramSender."""

    @pytest.fixture
    def mock_property_service(self):
        """Фикстура для мока PropertyService."""
        return MagicMock(spec=PropertyService)

    @pytest.fixture
    def mock_bot(self):
        """Фикстура для мока PropertyBot."""
        mock = MagicMock(spec=PropertyBot)
        mock.updater = MagicMock()
        mock.updater.bot = MagicMock(spec=Bot)
        mock.active_categories = {}
        return mock

    @pytest.fixture
    def telegram_sender(self, mock_property_service, mock_bot):
        """Фикстура для создания TelegramSender с моками."""
        with patch('src.presentation.telegram.services.sender.PropertyBot', return_value=mock_bot):
            sender = TelegramSender(
                token="**********************************************",
                property_service=mock_property_service,
                logger=logging.getLogger("test_logger")
            )

            # Устанавливаем короткий интервал проверки для тестов
            sender.check_interval = 0.1

            return sender

    def test_init(self, mock_property_service):
        """Тест инициализации сервиса."""
        with patch('src.presentation.telegram.services.sender.PropertyBot') as mock_bot_class:
            mock_bot = MagicMock()
            mock_bot_class.return_value = mock_bot

            sender = TelegramSender(
                token="**********************************************",
                property_service=mock_property_service,
                allowed_users=[123456789],
                logger=logging.getLogger("test_logger")
            )

            assert sender.property_service == mock_property_service
            assert sender.running is False
            assert mock_bot_class.call_args[1]["token"] == "**********************************************"
            assert mock_bot_class.call_args[1]["property_service"] == mock_property_service
            assert mock_bot_class.call_args[1]["allowed_users"] == [123456789]

    def test_start(self, telegram_sender, mock_bot):
        """Тест запуска сервиса."""
        with patch('threading.Thread') as mock_thread_class:
            mock_thread = MagicMock()
            mock_thread_class.return_value = mock_thread

            telegram_sender.start()

            assert telegram_sender.running is True
            mock_bot.start.assert_called_once()
            mock_thread_class.assert_called_once()
            mock_thread.start.assert_called_once()

    def test_start_already_running(self, telegram_sender, mock_bot):
        """Тест запуска уже запущенного сервиса."""
        telegram_sender.running = True

        telegram_sender.start()

        mock_bot.start.assert_not_called()

    def test_stop(self, telegram_sender, mock_bot):
        """Тест остановки сервиса."""
        telegram_sender.running = True

        telegram_sender.stop()

        assert telegram_sender.running is False
        mock_bot.stop.assert_called_once()

    def test_stop_not_running(self, telegram_sender, mock_bot):
        """Тест остановки не запущенного сервиса."""
        telegram_sender.running = False

        telegram_sender.stop()

        mock_bot.stop.assert_not_called()

    def test_send_message(self, telegram_sender, mock_bot):
        """Тест отправки сообщения."""
        telegram_sender.send_message(
            chat_id=123456789,
            text="Test message",
            parse_mode="HTML",
            disable_web_page_preview=True
        )

        mock_bot.updater.bot.send_message.assert_called_once_with(
            chat_id=123456789,
            text="Test message",
            parse_mode="HTML",
            disable_web_page_preview=True
        )

    def test_send_message_error(self, telegram_sender, mock_bot):
        """Тест отправки сообщения с ошибкой."""
        mock_bot.updater.bot.send_message.side_effect = Exception("Test error")

        telegram_sender.send_message(
            chat_id=123456789,
            text="Test message"
        )

        # Проверяем, что ошибка была обработана
        mock_bot.updater.bot.send_message.assert_called_once()

    def test_send_property(self, telegram_sender):
        """Тест отправки информации о недвижимости."""
        property_obj = {"id": "12345678", "title": "Test Property"}

        with patch('src.presentation.telegram.services.sender.format_property_message', return_value="Formatted message"), \
             patch.object(telegram_sender, 'send_message') as mock_send_message:

            telegram_sender.send_property(
                chat_id=123456789,
                property_obj=property_obj
            )

            mock_send_message.assert_called_once_with(
                chat_id=123456789,
                text="Formatted message",
                parse_mode='HTML',
                disable_web_page_preview=False
            )

    def test_send_property_with_category(self, telegram_sender):
        """Тест отправки информации о недвижимости с категорией."""
        property_obj = {"id": "12345678", "title": "Test Property"}

        with patch('src.presentation.telegram.services.sender.format_property_by_category', return_value="Formatted message"), \
             patch.object(telegram_sender, 'send_message') as mock_send_message:

            telegram_sender.send_property(
                chat_id=123456789,
                property_obj=property_obj,
                category="test_category"
            )

            mock_send_message.assert_called_once_with(
                chat_id=123456789,
                text="Formatted message",
                parse_mode='HTML',
                disable_web_page_preview=False
            )

    def test_send_property_error(self, telegram_sender):
        """Тест отправки информации о недвижимости с ошибкой."""
        property_obj = {"id": "12345678", "title": "Test Property"}

        with patch('src.presentation.telegram.services.sender.format_property_message', side_effect=Exception("Test error")):

            telegram_sender.send_property(
                chat_id=123456789,
                property_obj=property_obj
            )

            # Проверяем, что ошибка была обработана
            # Нет явных вызовов, которые можно проверить

    def test_send_price_changed_notification(self, telegram_sender):
        """Тест отправки уведомления об изменении цены."""
        property_obj = {"id": "12345678", "title": "Test Property"}

        with patch('src.presentation.telegram.services.sender.format_notification_price_changed', return_value="Formatted message"), \
             patch.object(telegram_sender, 'send_message') as mock_send_message:

            telegram_sender.send_price_changed_notification(
                chat_id=123456789,
                property_obj=property_obj,
                old_price=50000,
                new_price=45000
            )

            mock_send_message.assert_called_once_with(
                chat_id=123456789,
                text="Formatted message",
                parse_mode='HTML',
                disable_web_page_preview=False
            )

    def test_send_status_notification(self, telegram_sender):
        """Тест отправки уведомления о статусе парсинга."""
        with patch('src.presentation.telegram.services.sender.format_notification_status', return_value="Formatted message"), \
             patch.object(telegram_sender, 'send_message') as mock_send_message:

            telegram_sender.send_status_notification(
                chat_id=123456789,
                category="test_category",
                new_count=5,
                processed_count=10,
                error_count=0,
                execution_time="1 minute"
            )

            mock_send_message.assert_called_once_with(
                chat_id=123456789,
                text="Formatted message",
                parse_mode='HTML',
                disable_web_page_preview=True
            )

    def test_broadcast_message(self, telegram_sender, mock_bot):
        """Тест отправки сообщения всем пользователям."""
        mock_bot.active_categories = {123456789: ["test_category"], 987654321: ["test_category"]}

        with patch.object(telegram_sender, 'send_message') as mock_send_message, \
             patch('time.sleep') as mock_sleep:

            telegram_sender.broadcast_message(
                text="Broadcast message",
                parse_mode="HTML",
                disable_web_page_preview=True
            )

            assert mock_send_message.call_count == 2
            mock_send_message.assert_any_call(
                chat_id=123456789,
                text="Broadcast message",
                parse_mode="HTML",
                disable_web_page_preview=True
            )
            mock_send_message.assert_any_call(
                chat_id=987654321,
                text="Broadcast message",
                parse_mode="HTML",
                disable_web_page_preview=True
            )
            # В зависимости от реализации, может быть вызвано разное количество раз
            assert mock_sleep.call_count >= 1

    def test_check_new_ads(self, telegram_sender, mock_bot, mock_property_service):
        """Тест проверки новых объявлений."""
        mock_bot.active_categories = {123456789: ["test_category"]}
        mock_property_service.process_category.return_value = (5, 10, 0)

        with patch.object(telegram_sender, 'send_status_notification') as mock_send_status:

            telegram_sender._check_new_ads()

            mock_property_service.process_category.assert_called_once_with(
                category="test_category",
                chat_ids=[123456789],
                limit=10
            )
            mock_send_status.assert_called_once_with(
                chat_id=123456789,
                category="test_category",
                new_count=5,
                processed_count=10,
                error_count=0,
                execution_time="Только что"
            )

    def test_check_new_ads_no_new(self, telegram_sender, mock_bot, mock_property_service):
        """Тест проверки новых объявлений без новых объявлений."""
        mock_bot.active_categories = {123456789: ["test_category"]}
        mock_property_service.process_category.return_value = (0, 10, 0)

        with patch.object(telegram_sender, 'send_status_notification') as mock_send_status:

            telegram_sender._check_new_ads()

            mock_property_service.process_category.assert_called_once()
            mock_send_status.assert_not_called()

    def test_check_new_ads_error(self, telegram_sender, mock_bot, mock_property_service):
        """Тест проверки новых объявлений с ошибкой."""
        mock_bot.active_categories = {123456789: ["test_category"]}
        mock_property_service.process_category.side_effect = Exception("Test error")

        telegram_sender._check_new_ads()

        # Проверяем, что ошибка была обработана
        mock_property_service.process_category.assert_called_once()

    def test_check_loop(self, telegram_sender):
        """Тест цикла проверки новых объявлений."""
        # Этот тест проверяет, что цикл проверки запускается и останавливается корректно

        with patch.object(telegram_sender, '_check_new_ads') as mock_check_new_ads:
            # Запускаем сервис
            telegram_sender.start()

            # Ждем немного, чтобы цикл успел выполниться
            time.sleep(0.2)

            # Останавливаем сервис
            telegram_sender.stop()

            # Проверяем, что метод _check_new_ads был вызван хотя бы один раз
            assert mock_check_new_ads.call_count >= 1
