"""
Тесты для форматтеров сообщений Telegram.
"""
from unittest.mock import MagicMock, patch

import pytest
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price
from src.presentation.telegram.messages.formatters import (
    format_command_category_added, format_command_category_list,
    format_command_category_not_found, format_command_category_removed,
    format_command_error, format_command_help, format_command_start,
    format_command_status, format_notification_new_property,
    format_notification_price_changed, format_notification_status,
    format_properties_list, format_properties_short_list, format_property_by_category,
    format_property_message, format_property_short
)


class TestFormatters:
    """Тесты для форматтеров сообщений Telegram."""

    @pytest.fixture
    def sample_property(self):
        """Фикстура для создания тестового объекта Property."""
        return Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=60.5, living=40.0, kitchen=12.0),
            rooms=2,
            floor=5,
            total_floors=9,
            property_type="квартира",
            contact=Contact(name="Иван", phones=["+380991234567"]),
            images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        )

    def test_format_property_message(self, sample_property):
        """Тест форматирования сообщения о недвижимости."""
        with patch('src.presentation.telegram.messages.formatters._property_formatter.format_to_string', return_value="Formatted message"):
            result = format_property_message(sample_property)

            assert result == "Formatted message"

    def test_format_properties_list(self, sample_property):
        """Тест форматирования списка объектов недвижимости."""
        with patch('src.presentation.telegram.messages.formatters.format_property_message', return_value="Formatted message"):
            result = format_properties_list([sample_property, sample_property])

            assert len(result) == 2
            assert result[0] == "Formatted message"
            assert result[1] == "Formatted message"

    def test_format_property_short(self, sample_property):
        """Тест форматирования краткого сообщения о недвижимости."""
        with patch('src.presentation.telegram.messages.formatters._property_formatter.format_to_string', return_value="Formatted short message"):
            result = format_property_short(sample_property)

            assert result == "Formatted short message"

    def test_format_properties_short_list_empty(self):
        """Тест форматирования краткого списка объектов недвижимости (пустой список)."""
        result = format_properties_short_list([])

        assert result == "Объекты недвижимости не найдены"

    def test_format_properties_short_list(self, sample_property):
        """Тест форматирования краткого списка объектов недвижимости."""
        with patch('src.presentation.telegram.messages.formatters._property_formatter.format', return_value={
            'title': 'Тестовая квартира',
            'price': '50 000 USD',
            'url': 'https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html'
        }):
            result = format_properties_short_list([sample_property, sample_property], limit=1)

            assert "Найдено 2 объектов" in result
            assert "Тестовая квартира" in result
            assert "50 000 USD" in result
            assert "Показано 1 из 2 объектов" in result

    def test_format_property_by_category(self, sample_property):
        """Тест форматирования сообщения о недвижимости в соответствии с категорией."""
        with patch('src.presentation.telegram.messages.formatters._property_formatter.format_to_string', return_value="Formatted by category"):
            result = format_property_by_category(sample_property, "apartments_sale")

            assert result == "Formatted by category"

    def test_format_command_start(self):
        """Тест форматирования сообщения для команды /start."""
        result = format_command_start("Test User")

        assert "Привет, Test User" in result

    def test_format_command_help(self):
        """Тест форматирования сообщения для команды /help."""
        result = format_command_help()

        assert "Доступные команды" in result

    def test_format_command_status_with_categories(self):
        """Тест форматирования сообщения для команды /status с категориями."""
        result = format_command_status("Запущен", ["test_category1", "test_category2"])

        assert "Статус бота: Запущен" in result
        assert "Активные категории" in result
        assert "test_category1" in result
        assert "test_category2" in result

    def test_format_command_status_without_categories(self):
        """Тест форматирования сообщения для команды /status без категорий."""
        result = format_command_status("Запущен", [])

        assert "Статус бота: Запущен" in result
        assert "У вас нет активных категорий" in result

    def test_format_command_category_added(self):
        """Тест форматирования сообщения для команды /category add."""
        result = format_command_category_added("test_category")

        assert "test_category" in result
        assert "добавлена" in result

    def test_format_command_category_removed(self):
        """Тест форматирования сообщения для команды /category remove."""
        result = format_command_category_removed("test_category")

        assert "test_category" in result
        assert "удалена" in result

    def test_format_command_category_list_with_categories(self):
        """Тест форматирования сообщения для команды /category list с категориями."""
        result = format_command_category_list(["test_category1", "test_category2"])

        assert "Активные категории" in result
        assert "test_category1" in result
        assert "test_category2" in result

    def test_format_command_category_list_without_categories(self):
        """Тест форматирования сообщения для команды /category list без категорий."""
        result = format_command_category_list([])

        assert "У вас нет активных категорий" in result

    def test_format_command_category_not_found(self):
        """Тест форматирования сообщения для случая, когда категория не найдена."""
        result = format_command_category_not_found("test_category")

        assert "test_category" in result
        assert "не найдена" in result

    def test_format_command_error(self):
        """Тест форматирования сообщения об ошибке при обработке команды."""
        result = format_command_error()

        assert "Произошла ошибка" in result

    def test_format_notification_new_property(self, sample_property):
        """Тест форматирования уведомления о новом объявлении."""
        with patch('src.presentation.telegram.messages.formatters.format_property_by_category', return_value="Formatted property"):
            result = format_notification_new_property(sample_property, "apartments_sale")

            # В шаблоне нет строки "Новое объявление", проверяем только наличие форматированного объекта
            assert "Formatted property" in result

    def test_format_notification_price_changed(self, sample_property):
        """Тест форматирования уведомления об изменении цены."""
        with patch('src.presentation.telegram.messages.formatters._value_formatter.format_price', side_effect=["50 000 USD", "45 000 USD", "5 000 USD"]):
            result = format_notification_price_changed(sample_property, 50000, 45000)

            assert "Изменение цены" in result
            assert "Тестовая квартира" in result
            assert "50 000 USD" in result
            assert "45 000 USD" in result
            assert "Разница: 5 000 USD" in result

    def test_format_notification_status(self):
        """Тест форматирования уведомления о статусе парсинга."""
        result = format_notification_status("test_category", 5, 10, 0, "1 minute")

        assert "Статус парсинга" in result
        assert "test_category" in result
        assert "Новых объявлений: 5" in result
        assert "Обработано: 10" in result
        assert "Ошибок: 0" in result
        assert "Время выполнения: 1 minute" in result
