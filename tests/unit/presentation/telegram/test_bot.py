"""
Тесты для PropertyBot.
"""
import logging
from unittest.mock import MagicMock, patch

import pytest
from telegram import Bot, Message, Update, User
from telegram.ext import Updater

from src.application.services.property import PropertyService
from src.presentation.telegram.bot import PropertyBot


class TestPropertyBot:
    """Тесты для PropertyBot."""

    @pytest.fixture
    def mock_property_service(self):
        """Фикстура для мока PropertyService."""
        return MagicMock(spec=PropertyService)

    @pytest.fixture
    def mock_updater(self):
        """Фикстура для мока Updater."""
        mock = MagicMock(spec=Updater)
        mock.dispatcher = MagicMock()
        mock.bot = MagicMock(spec=Bot)
        return mock

    @pytest.fixture
    def mock_update(self):
        """Фикстура для мока Update."""
        mock = MagicMock(spec=Update)
        mock.effective_user = MagicMock(spec=User)
        mock.effective_user.id = 123456789
        mock.effective_user.first_name = "Test User"
        mock.message = MagicMock(spec=Message)
        mock.message.from_user = mock.effective_user
        mock.message.reply_text = MagicMock()
        return mock

    @pytest.fixture
    def mock_context(self):
        """Фикстура для мока CallbackContext."""
        mock = MagicMock()
        mock.args = []
        return mock

    @pytest.fixture
    def property_bot(self, mock_property_service):
        """Фикстура для создания PropertyBot с моками."""
        with patch('telegram.ext.Updater') as mock_updater_class, \
             patch('src.presentation.telegram.bot.Updater', create=True) as mock_bot_updater:
            # Создаем моки для Updater и Dispatcher
            mock_updater = MagicMock()
            mock_updater.dispatcher = MagicMock()
            mock_updater.bot = MagicMock()

            # Настраиваем моки
            mock_updater_class.return_value = mock_updater
            mock_bot_updater.return_value = mock_updater

            # Создаем объект PropertyBot
            bot = PropertyBot(
                token="7372806539:AAG3s78Q7niNVf8M1wZMCX_RhLBvHE9bVvI",
                property_service=mock_property_service,
                logger=logging.getLogger("test_logger")
            )

            return bot

    def test_init(self, mock_property_service):
        """Тест инициализации бота."""
        with patch('telegram.ext.Updater') as mock_updater_class, \
             patch('src.presentation.telegram.bot.Updater', create=True) as mock_bot_updater:
            # Создаем моки для Updater и Dispatcher
            mock_updater = MagicMock()
            mock_updater.dispatcher = MagicMock()
            mock_updater.bot = MagicMock()

            # Настраиваем моки
            mock_updater_class.return_value = mock_updater
            mock_bot_updater.return_value = mock_updater

            # Создаем объект PropertyBot
            bot = PropertyBot(
                token="7372806539:AAG3s78Q7niNVf8M1wZMCX_RhLBvHE9bVvI",
                property_service=mock_property_service,
                allowed_users=[123456789],
                logger=logging.getLogger("test_logger")
            )

            assert bot.token == "7372806539:AAG3s78Q7niNVf8M1wZMCX_RhLBvHE9bVvI"
            assert bot.property_service == mock_property_service
            assert bot.allowed_users == [123456789]
            assert bot.running is False
            assert bot.active_categories == {}
            assert mock_bot_updater.call_args[1]["token"] == "7372806539:AAG3s78Q7niNVf8M1wZMCX_RhLBvHE9bVvI"
            assert mock_bot_updater.call_args[1]["use_context"] is True
            assert mock_updater.dispatcher.add_handler.call_count == 6  # 5 команды + 1 текстовый обработчик

    def test_is_user_allowed_with_empty_list(self, property_bot, mock_update):
        """Тест проверки разрешенного пользователя с пустым списком."""
        property_bot.allowed_users = []

        result = property_bot._is_user_allowed(mock_update)

        assert result is True
        mock_update.message.reply_text.assert_not_called()

    def test_is_user_allowed_with_allowed_user(self, property_bot, mock_update):
        """Тест проверки разрешенного пользователя."""
        property_bot.allowed_users = [123456789]

        result = property_bot._is_user_allowed(mock_update)

        assert result is True
        mock_update.message.reply_text.assert_not_called()

    def test_is_user_allowed_with_not_allowed_user(self, property_bot, mock_update):
        """Тест проверки неразрешенного пользователя."""
        property_bot.allowed_users = [987654321]

        result = property_bot._is_user_allowed(mock_update)

        assert result is False
        mock_update.message.reply_text.assert_called_once()
        assert "нет доступа" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_start(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /start."""
        with patch('src.presentation.telegram.bot.handle_start') as mock_handle_start:
            property_bot._handle_start(mock_update, mock_context)

            mock_handle_start.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_handle_help(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /help."""
        with patch('src.presentation.telegram.bot.handle_help') as mock_handle_help:
            property_bot._handle_help(mock_update, mock_context)

            mock_handle_help.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_handle_status(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /status."""
        with patch('src.presentation.telegram.bot.handle_status') as mock_handle_status:
            property_bot._handle_status(mock_update, mock_context)

            mock_handle_status.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_handle_stop(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /stop."""
        with patch('src.presentation.telegram.bot.handle_stop') as mock_handle_stop:
            property_bot._handle_stop(mock_update, mock_context)

            mock_handle_stop.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_handle_category(self, property_bot, mock_update, mock_context):
        """Тест обработки команды /category."""
        with patch('src.presentation.telegram.bot.handle_category') as mock_handle_category:
            property_bot._handle_category(mock_update, mock_context)

            mock_handle_category.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_handle_text(self, property_bot, mock_update, mock_context):
        """Тест обработки текстового сообщения."""
        property_bot._handle_text(mock_update, mock_context)

        mock_update.message.reply_text.assert_called_once()
        assert "не понимаю" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_error(self, property_bot, mock_update, mock_context):
        """Тест обработки ошибки."""
        with patch('src.presentation.telegram.bot.handle_error') as mock_handle_error:
            property_bot._handle_error(mock_update, mock_context)

            mock_handle_error.assert_called_once_with(mock_update, mock_context, property_bot)

    def test_start(self, property_bot):
        """Тест запуска бота."""
        property_bot.start()

        assert property_bot.running is True
        property_bot.updater.start_polling.assert_called_once()

    def test_start_already_running(self, property_bot):
        """Тест запуска уже запущенного бота."""
        property_bot.running = True

        property_bot.start()

        property_bot.updater.start_polling.assert_not_called()

    def test_stop(self, property_bot):
        """Тест остановки бота."""
        property_bot.running = True

        property_bot.stop()

        assert property_bot.running is False
        property_bot.updater.stop.assert_called_once()

    def test_stop_not_running(self, property_bot):
        """Тест остановки не запущенного бота."""
        property_bot.running = False

        property_bot.stop()

        property_bot.updater.stop.assert_not_called()

    def test_add_category(self, property_bot):
        """Тест добавления категории."""
        user_id = 123456789
        category = "test_category"

        property_bot.add_category(user_id, category)

        assert user_id in property_bot.active_categories
        assert category in property_bot.active_categories[user_id]

    def test_add_category_existing_user(self, property_bot):
        """Тест добавления категории для существующего пользователя."""
        user_id = 123456789
        category1 = "test_category1"
        category2 = "test_category2"

        property_bot.active_categories[user_id] = [category1]

        property_bot.add_category(user_id, category2)

        assert user_id in property_bot.active_categories
        assert category1 in property_bot.active_categories[user_id]
        assert category2 in property_bot.active_categories[user_id]

    def test_add_category_duplicate(self, property_bot):
        """Тест добавления дублирующейся категории."""
        user_id = 123456789
        category = "test_category"

        property_bot.active_categories[user_id] = [category]

        property_bot.add_category(user_id, category)

        assert user_id in property_bot.active_categories
        assert category in property_bot.active_categories[user_id]
        assert len(property_bot.active_categories[user_id]) == 1

    def test_remove_category(self, property_bot):
        """Тест удаления категории."""
        user_id = 123456789
        category1 = "test_category1"
        category2 = "test_category2"

        property_bot.active_categories[user_id] = [category1, category2]

        property_bot.remove_category(user_id, category1)

        assert user_id in property_bot.active_categories
        assert category1 not in property_bot.active_categories[user_id]
        assert category2 in property_bot.active_categories[user_id]

    def test_remove_category_non_existing(self, property_bot):
        """Тест удаления несуществующей категории."""
        user_id = 123456789
        category1 = "test_category1"
        category2 = "test_category2"

        property_bot.active_categories[user_id] = [category1]

        property_bot.remove_category(user_id, category2)

        assert user_id in property_bot.active_categories
        assert category1 in property_bot.active_categories[user_id]
        assert len(property_bot.active_categories[user_id]) == 1

    def test_get_categories(self, property_bot):
        """Тест получения категорий."""
        user_id = 123456789
        category1 = "test_category1"
        category2 = "test_category2"

        property_bot.active_categories[user_id] = [category1, category2]

        result = property_bot.get_categories(user_id)

        assert result == [category1, category2]

    def test_get_categories_non_existing_user(self, property_bot):
        """Тест получения категорий для несуществующего пользователя."""
        user_id = 123456789

        result = property_bot.get_categories(user_id)

        assert result == []
