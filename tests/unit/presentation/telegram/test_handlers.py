"""
Тесты для обработчиков команд Telegram бота.
"""
import json
import os
from unittest.mock import MagicMock, patch

import pytest
from telegram import Message, Update, User
from telegram.ext import CallbackContext

from src.presentation.telegram.handlers import (
    handle_category, handle_error, handle_help, handle_start, handle_status,
    handle_stop
)


class TestHandlers:
    """Тесты для обработчиков команд Telegram бота."""

    @pytest.fixture
    def mock_update(self):
        """Фикстура для мока Update."""
        mock = MagicMock(spec=Update)
        mock.effective_user = MagicMock(spec=User)
        mock.effective_user.id = 123456789
        mock.effective_user.first_name = "Test User"
        mock.message = MagicMock(spec=Message)
        mock.message.from_user = mock.effective_user
        mock.message.reply_text = MagicMock()
        return mock

    @pytest.fixture
    def mock_context(self):
        """Фикстура для мока CallbackContext."""
        mock = MagicMock(spec=CallbackContext)
        mock.args = []
        return mock

    @pytest.fixture
    def mock_bot(self):
        """Фикстура для мока PropertyBot."""
        mock = MagicMock()
        mock.get_categories.return_value = []
        mock.running = True
        mock.active_categories = {}
        mock.logger = MagicMock()
        return mock

    def test_handle_start(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /start."""
        handle_start(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Привет" in mock_update.message.reply_text.call_args[0][0]
        assert "Test User" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_start_no_message(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /start без сообщения."""
        mock_update.message = None
        
        handle_start(mock_update, mock_context, mock_bot)
        
        # Проверяем, что ничего не произошло
        mock_bot.assert_not_called()

    def test_handle_help(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /help."""
        handle_help(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Доступные команды" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_help_no_message(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /help без сообщения."""
        mock_update.message = None
        
        handle_help(mock_update, mock_context, mock_bot)
        
        # Проверяем, что ничего не произошло
        mock_bot.assert_not_called()

    def test_handle_status_no_categories(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /status без категорий."""
        handle_status(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Статус бота: Запущен" in mock_update.message.reply_text.call_args[0][0]
        assert "У вас нет активных категорий" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_status_with_categories(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /status с категориями."""
        mock_bot.get_categories.return_value = ["test_category1", "test_category2"]
        
        handle_status(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Статус бота: Запущен" in mock_update.message.reply_text.call_args[0][0]
        assert "Активные категории" in mock_update.message.reply_text.call_args[0][0]
        assert "test_category1" in mock_update.message.reply_text.call_args[0][0]
        assert "test_category2" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_status_no_message(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /status без сообщения."""
        mock_update.message = None
        
        handle_status(mock_update, mock_context, mock_bot)
        
        # Проверяем, что ничего не произошло
        mock_bot.get_categories.assert_not_called()

    def test_handle_stop(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /stop."""
        mock_bot.active_categories = {123456789: ["test_category"]}
        
        handle_stop(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Уведомления остановлены" in mock_update.message.reply_text.call_args[0][0]
        assert mock_bot.active_categories[123456789] == []

    def test_handle_stop_no_message(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /stop без сообщения."""
        mock_update.message = None
        
        handle_stop(mock_update, mock_context, mock_bot)
        
        # Проверяем, что ничего не произошло
        assert mock_bot.active_categories == {}

    def test_handle_category_no_args(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category без аргументов."""
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Используйте команду" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_add_no_category(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category add без категории."""
        mock_context.args = ["add"]
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Используйте команду" in mock_update.message.reply_text.call_args[0][0]
        assert "add" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_add_invalid_category(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category add с несуществующей категорией."""
        mock_context.args = ["add", "invalid_category"]
        
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', MagicMock()), \
             patch('json.load', return_value={"categories": [{"id": "valid_category"}]}):
            
            handle_category(mock_update, mock_context, mock_bot)
            
            mock_update.message.reply_text.assert_called_once()
            assert "не найдена" in mock_update.message.reply_text.call_args[0][0]
            assert "invalid_category" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_add_valid_category(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category add с существующей категорией."""
        mock_context.args = ["add", "valid_category"]
        
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', MagicMock()), \
             patch('json.load', return_value={"categories": [{"id": "valid_category"}]}):
            
            handle_category(mock_update, mock_context, mock_bot)
            
            mock_bot.add_category.assert_called_once_with(123456789, "valid_category")
            mock_update.message.reply_text.assert_called_once()
            assert "добавлена" in mock_update.message.reply_text.call_args[0][0]
            assert "valid_category" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_remove(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category remove."""
        mock_context.args = ["remove", "test_category"]
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_bot.remove_category.assert_called_once_with(123456789, "test_category")
        mock_update.message.reply_text.assert_called_once()
        assert "удалена" in mock_update.message.reply_text.call_args[0][0]
        assert "test_category" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_remove_no_category(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category remove без категории."""
        mock_context.args = ["remove"]
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Используйте команду" in mock_update.message.reply_text.call_args[0][0]
        assert "remove" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_list_no_categories(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category list без категорий."""
        mock_context.args = ["list"]
        mock_bot.get_categories.return_value = []
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "У вас нет активных категорий" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_list_with_categories(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category list с категориями."""
        mock_context.args = ["list"]
        mock_bot.get_categories.return_value = ["test_category1", "test_category2"]
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Активные категории" in mock_update.message.reply_text.call_args[0][0]
        assert "test_category1" in mock_update.message.reply_text.call_args[0][0]
        assert "test_category2" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_unknown_action(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category с неизвестным действием."""
        mock_context.args = ["unknown"]
        
        handle_category(mock_update, mock_context, mock_bot)
        
        mock_update.message.reply_text.assert_called_once()
        assert "Неизвестное действие" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_category_no_message(self, mock_update, mock_context, mock_bot):
        """Тест обработки команды /category без сообщения."""
        mock_update.message = None
        
        handle_category(mock_update, mock_context, mock_bot)
        
        # Проверяем, что ничего не произошло
        mock_bot.add_category.assert_not_called()
        mock_bot.remove_category.assert_not_called()
        mock_bot.get_categories.assert_not_called()

    def test_handle_error(self, mock_update, mock_context, mock_bot):
        """Тест обработки ошибки."""
        mock_context.error = Exception("Test error")
        
        handle_error(mock_update, mock_context, mock_bot)
        
        mock_bot.logger.error.assert_called_once()
        assert "Test error" in str(mock_bot.logger.error.call_args[0][0])
        mock_update.message.reply_text.assert_called_once()
        assert "Произошла ошибка" in mock_update.message.reply_text.call_args[0][0]

    def test_handle_error_no_update(self, mock_context, mock_bot):
        """Тест обработки ошибки без объекта Update."""
        mock_context.error = Exception("Test error")
        
        handle_error(None, mock_context, mock_bot)
        
        mock_bot.logger.error.assert_called_once()
        assert "Test error" in str(mock_bot.logger.error.call_args[0][0])
