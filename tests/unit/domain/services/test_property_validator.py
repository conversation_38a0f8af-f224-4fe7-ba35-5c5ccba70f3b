"""
Тесты для доменного сервиса PropertyValidator.
"""
import pytest
from src.domain.services.property_validator import PropertyValidator
from src.domain.entities.property import Property
from src.domain.value_objects.price import Price
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.ad_id import AdId


class TestPropertyValidator:
    """Тесты для доменного сервиса PropertyValidator."""

    def test_validate_valid_property(self):
        """Тест валидации корректного объекта недвижимости."""
        # Создаем валидный объект недвижимости
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=60.5, living=40.0, kitchen=10.0),
            rooms=2,
            floor=5,
            total_floors=9,
            property_type="квартира",
            contact=Contact(name="Иван", phones=["+380991234567"]),
            images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        )

        # Проверяем, что валидация проходит без ошибок
        validator = PropertyValidator()
        is_valid, errors = validator.validate(property_obj)
        assert is_valid is True
        assert len(errors) == 0

    def test_validate_missing_required_fields(self):
        """Тест валидации объекта недвижимости с отсутствующими обязательными полями."""
        # В этом тесте мы не можем создать объект с отсутствующими обязательными полями,
        # так как валидация происходит в __post_init__ класса Property
        # Вместо этого проверим дополнительные бизнес-правила

        # Создаем объект недвижимости с подозрительно низкой ценой
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(1000, "USD"),  # Подозрительно низкая цена
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=60.5),
            property_type="квартира",  # Указываем тип недвижимости
            contact=Contact(name="Иван", phones=[])
        )

        # Проверяем, что валидация находит ошибки
        validator = PropertyValidator()
        is_valid, errors = validator.validate(property_obj)
        assert is_valid is False
        assert len(errors) > 0

        # Проверяем наличие конкретных ошибок
        assert any("низкая цена" in error.lower() for error in errors)
        assert any("телефон" in error.lower() for error in errors)

    def test_validate_invalid_values(self):
        """Тест валидации объекта недвижимости с некорректными значениями полей."""
        # Создаем объект недвижимости с некорректными значениями
        try:
            # Пробуем создать объект с некорректными значениями этажей
            property_obj = Property(
                ad_id=AdId("12345678"),
                title="Тестовая квартира",
                description="Описание тестовой квартиры",
                price=Price(50000, "USD"),
                address=Address("Киев", "Печерский"),
                url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
                floor=10,
                total_floors=5  # Этаж больше общего количества этажей
            )
            pytest.fail("Должно было вызвать исключение ValueError")
        except ValueError as e:
            # Проверяем, что исключение содержит ожидаемое сообщение
            assert "этаж" in str(e).lower() and "больше" in str(e).lower()

        try:
            # Пробуем создать объект с отрицательным этажом
            property_obj = Property(
                ad_id=AdId("12345678"),
                title="Тестовая квартира",
                description="Описание тестовой квартиры",
                price=Price(50000, "USD"),
                address=Address("Киев", "Печерский"),
                url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
                floor=-1,  # Отрицательный этаж
                total_floors=5
            )
            pytest.fail("Должно было вызвать исключение ValueError")
        except ValueError as e:
            # Проверяем, что исключение содержит ожидаемое сообщение
            assert "этаж" in str(e).lower() and "положительным" in str(e).lower()

        # Тестируем валидацию комнат
        try:
            # Пробуем создать объект с отрицательным количеством комнат
            property_obj = Property(
                ad_id=AdId("12345678"),
                title="Тестовая квартира",
                description="Описание тестовой квартиры",
                price=Price(50000, "USD"),
                address=Address("Киев", "Печерский"),
                url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
                rooms=-1  # Отрицательное количество комнат
            )
            pytest.fail("Должно было вызвать исключение ValueError")
        except ValueError as e:
            # Проверяем, что исключение содержит ожидаемое сообщение
            assert "комнат" in str(e).lower() and "положительным" in str(e).lower()
