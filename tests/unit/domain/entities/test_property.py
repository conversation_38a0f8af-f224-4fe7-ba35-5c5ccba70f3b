"""
Тесты для доменной сущности Property.
"""
from datetime import datetime
from unittest.mock import patch

import pytest

from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price


class TestProperty:
    """Тесты для доменной сущности Property."""

    @pytest.fixture
    def valid_property_data(self):
        """Фикстура для создания валидных данных Property."""
        return {
            "ad_id": AdId("12345678"),
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": Price(50000, "USD"),
            "address": Address("Киев", "Печерский"),
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            "area": Area(total=60.5, living=40.0, kitchen=12.0),
            "rooms": 2,
            "floor": 5,
            "total_floors": 9,
            "property_type": "квартира",
            "contact": Contact(name="Иван", phones=["+380991234567"]),
            "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
            "published_at": datetime(2023, 1, 1, 12, 0, 0),
            "updated_at": datetime(2023, 1, 2, 12, 0, 0),
            "additional_info": {"Тип стен": "Кирпич", "Состояние": "После ремонта"}
        }

    def test_create_property(self, valid_property_data):
        """Тест создания объекта Property."""
        # Act
        property_obj = Property(**valid_property_data)

        # Assert
        assert property_obj.ad_id == valid_property_data["ad_id"]
        assert property_obj.title == valid_property_data["title"]
        assert property_obj.description == valid_property_data["description"]
        assert property_obj.price == valid_property_data["price"]
        assert property_obj.address == valid_property_data["address"]
        assert property_obj.url == valid_property_data["url"]
        assert property_obj.area == valid_property_data["area"]
        assert property_obj.rooms == valid_property_data["rooms"]
        assert property_obj.floor == valid_property_data["floor"]
        assert property_obj.total_floors == valid_property_data["total_floors"]
        assert property_obj.property_type == valid_property_data["property_type"]
        assert property_obj.contact == valid_property_data["contact"]
        assert property_obj.images == valid_property_data["images"]
        assert property_obj.published_at == valid_property_data["published_at"]
        assert property_obj.updated_at == valid_property_data["updated_at"]
        assert property_obj.additional_info == valid_property_data["additional_info"]

    def test_create_property_with_minimal_data(self):
        """Тест создания объекта Property с минимальным набором данных."""
        # Arrange
        minimal_data = {
            "ad_id": AdId("12345678"),
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": Price(50000, "USD"),
            "address": Address("Киев", "Печерский"),
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        }

        # Act
        property_obj = Property(**minimal_data)

        # Assert
        assert property_obj.ad_id == minimal_data["ad_id"]
        assert property_obj.title == minimal_data["title"]
        assert property_obj.description == minimal_data["description"]
        assert property_obj.price == minimal_data["price"]
        assert property_obj.address == minimal_data["address"]
        assert property_obj.url == minimal_data["url"]
        assert property_obj.area is None
        assert property_obj.rooms is None
        assert property_obj.floor is None
        assert property_obj.total_floors is None
        assert property_obj.property_type is None
        assert property_obj.contact is None
        assert property_obj.images is None
        assert property_obj.published_at is None
        assert property_obj.updated_at is None
        assert property_obj.additional_info is None

    def test_create_property_missing_required_fields(self):
        """Тест создания объекта Property с отсутствующими обязательными полями."""
        # Arrange
        base_data = {
            "ad_id": AdId("12345678"),
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": Price(50000, "USD"),
            "address": Address("Киев", "Печерский"),
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        }

        invalid_data_sets = [
            # Отсутствует ad_id
            {k: v for k, v in base_data.items() if k != "ad_id"},
            # Отсутствует title
            {k: v for k, v in base_data.items() if k != "title"},
            # Отсутствует description
            {k: v for k, v in base_data.items() if k != "description"},
            # Отсутствует price
            {k: v for k, v in base_data.items() if k != "price"},
            # Отсутствует address
            {k: v for k, v in base_data.items() if k != "address"},
            # Отсутствует url
            {k: v for k, v in base_data.items() if k != "url"},
        ]

        # Act & Assert
        for i, invalid_data in enumerate(invalid_data_sets):
            try:
                with pytest.raises((ValueError, TypeError)):
                    Property(**invalid_data)
            except Exception as e:
                pytest.fail(f"Тест #{i} не прошел: {e}")

    def test_create_property_invalid_floor_values(self):
        """Тест создания объекта Property с некорректными значениями этажей."""
        # Arrange
        base_data = {
            "ad_id": AdId("12345678"),
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": Price(50000, "USD"),
            "address": Address("Киев", "Печерский"),
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        }

        invalid_floor_data_sets = [
            # Этаж больше общего количества этажей
            {**base_data, "floor": 10, "total_floors": 9},
            # Отрицательный этаж
            {**base_data, "floor": -1, "total_floors": 9},
            # Нулевой этаж
            {**base_data, "floor": 0, "total_floors": 9},
            # Отрицательное общее количество этажей
            {**base_data, "floor": 5, "total_floors": -1},
            # Нулевое общее количество этажей
            {**base_data, "floor": 5, "total_floors": 0},
        ]

        # Act & Assert
        for invalid_data in invalid_floor_data_sets:
            with pytest.raises(ValueError):
                Property(**invalid_data)

    def test_create_property_invalid_rooms_value(self):
        """Тест создания объекта Property с некорректным значением количества комнат."""
        # Arrange
        base_data = {
            "ad_id": AdId("12345678"),
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": Price(50000, "USD"),
            "address": Address("Киев", "Печерский"),
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        }

        invalid_rooms_data_sets = [
            # Отрицательное количество комнат
            {**base_data, "rooms": -1},
            # Нулевое количество комнат
            {**base_data, "rooms": 0},
        ]

        # Act & Assert
        for invalid_data in invalid_rooms_data_sets:
            with pytest.raises(ValueError):
                Property(**invalid_data)

    def test_is_new_building_true(self):
        """Тест метода is_new_building, когда объект является новостройкой."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            additional_info={"Тип дома": "Новостройка", "Состояние": "После ремонта"}
        )

        # Act
        result = property_obj.is_new_building

        # Assert
        assert result is True

    def test_is_new_building_false(self):
        """Тест метода is_new_building, когда объект не является новостройкой."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            additional_info={"Тип дома": "Кирпичный", "Состояние": "После ремонта"}
        )

        # Act
        result = property_obj.is_new_building

        # Assert
        assert result is False

    def test_is_new_building_no_additional_info(self):
        """Тест метода is_new_building, когда отсутствует additional_info."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        )

        # Act
        result = property_obj.is_new_building

        # Assert
        assert result is False

    def test_price_per_meter(self):
        """Тест метода price_per_meter."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=50.0)
        )

        # Act
        result = property_obj.price_per_meter

        # Assert
        assert result == 1000.0

    def test_price_per_meter_no_area(self):
        """Тест метода price_per_meter при отсутствии площади."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        )

        # Act
        result = property_obj.price_per_meter

        # Assert
        assert result is None

    def test_price_per_meter_zero_area(self):
        """Тест метода price_per_meter при нулевой площади."""
        # В данном случае мы не можем создать Area с нулевой площадью,
        # так как это вызовет исключение ValueError.
        # Поэтому мы проверим, что при попытке создать Area с нулевой площадью
        # выбрасывается исключение ValueError.

        # Act & Assert
        with pytest.raises(ValueError):
            Area(total=0.0)

    def test_str_representation(self):
        """Тест строкового представления объекта Property."""
        # Arrange
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        )

        # Act
        result = str(property_obj)

        # Assert
        assert result == "Тестовая квартира - 50000 USD (Киев, Печерский)"

    def test_from_dict_valid_data(self):
        """Тест метода from_dict с валидными данными."""
        # Arrange
        data = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            "area": {"total": 60.5, "living": 40.0, "kitchen": 12.0},
            "rooms": 2,
            "floor": 5,
            "total_floors": 9,
            "property_type": "квартира",
            "contact": {"name": "Иван", "phones": ["+380991234567"]},
            "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
            "published_at": "2023-01-01T12:00:00",
            "updated_at": "2023-01-02T12:00:00",
            "additional_info": {"Тип стен": "Кирпич", "Состояние": "После ремонта"}
        }

        # Act
        property_obj = Property.from_dict(data)

        # Assert
        assert property_obj is not None
        assert property_obj.ad_id.value == "12345678"
        assert property_obj.title == "Тестовая квартира"
        assert property_obj.description == "Описание тестовой квартиры"
        assert property_obj.price.amount == 50000
        assert property_obj.price.currency == "USD"
        assert property_obj.address.city == "Киев"
        assert property_obj.address.district == "Печерский"
        assert property_obj.url == "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"

        # Проверяем наличие и значения area
        assert property_obj.area is not None
        if property_obj.area:
            assert property_obj.area.total == 60.5
            assert property_obj.area.living == 40.0
            assert property_obj.area.kitchen == 12.0

        assert property_obj.rooms == 2
        assert property_obj.floor == 5
        assert property_obj.total_floors == 9
        assert property_obj.property_type == "квартира"

        # Проверяем наличие и значения contact
        assert property_obj.contact is not None
        if property_obj.contact:
            assert property_obj.contact.name == "Иван"
            assert property_obj.contact.phones == ["+380991234567"]

        # В методе from_dict количество изображений ограничивается до 1
        assert property_obj.images == ["https://example.com/image1.jpg"]
        assert property_obj.published_at == datetime(2023, 1, 1, 12, 0, 0)
        assert property_obj.updated_at == datetime(2023, 1, 2, 12, 0, 0)
        assert property_obj.additional_info == {"Тип стен": "Кирпич", "Состояние": "После ремонта"}

    def test_from_dict_with_ad_id_object(self):
        """Тест метода from_dict с объектом AdId."""
        # Arrange
        data = {
            "id": "12345678",  # Используем id вместо ad_id
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        }

        # Act
        property_obj = Property.from_dict(data)

        # Assert
        assert property_obj is not None
        assert property_obj.ad_id.value == "12345678"

    def test_from_dict_with_datetime_objects(self):
        """Тест метода from_dict с объектами datetime."""
        # Arrange
        published_at = datetime(2023, 1, 1, 12, 0, 0)
        updated_at = datetime(2023, 1, 2, 12, 0, 0)
        data = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            "published_at": published_at,
            "updated_at": updated_at,
        }

        # Act
        property_obj = Property.from_dict(data)

        # Assert
        assert property_obj is not None
        assert property_obj.published_at == published_at
        assert property_obj.updated_at == updated_at

    def test_from_dict_missing_required_fields(self):
        """Тест метода from_dict с отсутствующими обязательными полями."""
        # Arrange
        invalid_data_sets = [
            # Отсутствует id
            {
                "title": "Тестовая квартира",
                "price": {"amount": 50000, "currency": "USD"},
                "address": {"city": "Киев", "district": "Печерский"},
                "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            },
            # Отсутствует title
            {
                "id": "12345678",
                "price": {"amount": 50000, "currency": "USD"},
                "address": {"city": "Киев", "district": "Печерский"},
                "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            },
            # Отсутствует price
            {
                "id": "12345678",
                "title": "Тестовая квартира",
                "address": {"city": "Киев", "district": "Печерский"},
                "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            },
            # Отсутствует address
            {
                "id": "12345678",
                "title": "Тестовая квартира",
                "price": {"amount": 50000, "currency": "USD"},
                "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            },
            # Отсутствует url
            {
                "id": "12345678",
                "title": "Тестовая квартира",
                "price": {"amount": 50000, "currency": "USD"},
                "address": {"city": "Киев", "district": "Печерский"},
            },
        ]

        # Act & Assert
        for invalid_data in invalid_data_sets:
            assert Property.from_dict(invalid_data) is None

    def test_from_dict_empty_data(self):
        """Тест метода from_dict с пустыми данными."""
        # Act & Assert
        assert Property.from_dict({}) is None

        # Проверяем с патчем, чтобы избежать ошибки типизации
        with patch.object(Property, 'from_dict', return_value=None) as mock_from_dict:
            result = mock_from_dict(None)
            assert result is None

    def test_from_dict_with_exception(self):
        """Тест метода from_dict с исключением."""
        # Arrange
        data = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            "floor": 10,
            "total_floors": 9,  # Это вызовет исключение в __post_init__
        }

        # Act
        with patch('builtins.print') as mock_print:
            property_obj = Property.from_dict(data)

        # Assert
        assert property_obj is None
        # Метод from_dict может вызывать print несколько раз при обработке исключений
        assert mock_print.call_count > 0
        # Проверяем, что в одном из вызовов есть сообщение об ошибке
        error_message_found = False
        for call in mock_print.call_args_list:
            args, _ = call
            if args and "Этаж не может быть больше общего количества этажей" in str(args):
                error_message_found = True
                break
        assert error_message_found
