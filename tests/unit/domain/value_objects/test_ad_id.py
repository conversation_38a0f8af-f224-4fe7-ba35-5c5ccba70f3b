"""
Тесты для Value Object AdId.
"""
import pytest
from src.domain.value_objects.ad_id import AdId


class TestAdId:
    """Тесты для Value Object AdId."""

    def test_create_valid_ad_id(self):
        """Тест создания валидного AdId."""
        ad_id = AdId("12345678")
        assert ad_id.value == "12345678"
        assert str(ad_id) == "12345678"

    def test_create_from_string(self):
        """Тест создания AdId из строки."""
        ad_id = AdId.from_string("12345678")
        assert ad_id.value == "12345678"

    def test_create_from_dict_with_ad_id(self):
        """Тест создания AdId из словаря с полем ad_id."""
        ad_id = AdId.from_dict({"ad_id": "12345678"})
        assert ad_id.value == "12345678"

    def test_create_from_dict_with_numeric_id(self):
        """Тест создания AdId из словаря с числовым полем id (из элемента span)."""
        ad_id = AdId.from_dict({"id": "880459925"})
        assert ad_id.value == "880459925"

    def test_create_from_dict_with_url_id(self):
        """Тест создания AdId из словаря с нечисловым полем id (из URL)."""
        ad_id = AdId.from_dict({"id": "prodatsya-2-h-kmnatna-kvartira-v-tsentr-cherngova-potrebu-remontu-IDXLAaQ"})
        assert ad_id.value == "prodatsya-2-h-kmnatna-kvartira-v-tsentr-cherngova-potrebu-remontu-IDXLAaQ"

    def test_create_from_dict_missing_key(self):
        """Тест создания AdId из словаря с отсутствующим ключом."""
        ad_id = AdId.from_dict({})
        assert ad_id is None

    def test_equality(self):
        """Тест сравнения AdId."""
        ad_id1 = AdId("12345678")
        ad_id2 = AdId("12345678")
        ad_id3 = AdId("87654321")

        assert ad_id1 == ad_id2
        assert ad_id1 != ad_id3
        assert ad_id1 != "12345678"  # Сравнение с другим типом

    def test_hash(self):
        """Тест хеширования AdId."""
        ad_id1 = AdId("12345678")
        ad_id2 = AdId("12345678")
        ad_id3 = AdId("87654321")

        # Одинаковые объекты должны иметь одинаковый хеш
        assert hash(ad_id1) == hash(ad_id2)
        # Разные объекты должны иметь разный хеш
        assert hash(ad_id1) != hash(ad_id3)

        # Проверка использования в словаре
        d = {ad_id1: "value1", ad_id3: "value3"}
        assert d[ad_id2] == "value1"  # ad_id2 эквивалентен ad_id1

    def test_empty_value(self):
        """Тест создания AdId с пустым значением."""
        with pytest.raises(ValueError):
            AdId("")

    def test_non_string_value(self):
        """Тест создания AdId с нестроковым значением."""
        with pytest.raises(TypeError):
            AdId(12345678)
