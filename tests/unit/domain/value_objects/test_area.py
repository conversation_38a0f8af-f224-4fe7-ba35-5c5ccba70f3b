"""
Тесты для Value Object Area.
"""
import pytest
from src.domain.value_objects.area import Area


class TestArea:
    """Тесты для Value Object Area."""

    def test_create_valid_area(self):
        """Тест создания валидной площади."""
        area = Area(total=60.5)
        assert area.total == 60.5
        assert str(area) == "60.5 м²"

        # С дополнительными параметрами
        area = Area(total=60.5, living=40.0, kitchen=10.0)
        assert area.total == 60.5
        assert area.living == 40.0
        assert area.kitchen == 10.0
        assert str(area) == "60.5 м² (жилая: 40.0 м²) (кухня: 10.0 м²)"

    def test_create_from_dict(self):
        """Тест создания Area из словаря."""
        area = Area.from_dict({"total": 60.5})
        assert area.total == 60.5

        # С дополнительными параметрами
        area = Area.from_dict({"total": 60.5, "living": 40.0, "kitchen": 10.0})
        assert area.total == 60.5
        assert area.living == 40.0
        assert area.kitchen == 10.0

    def test_create_from_dict_missing_key(self):
        """Тест создания Area из словаря с отсутствующим ключом."""
        area = Area.from_dict({})
        assert area is None

    def test_negative_area(self):
        """Тест создания Area с отрицательной площадью."""
        with pytest.raises(ValueError):
            Area(total=-60.5)

    def test_zero_area(self):
        """Тест создания Area с нулевой площадью."""
        with pytest.raises(ValueError):
            Area(total=0)

    def test_invalid_living_area(self):
        """Тест создания Area с жилой площадью больше общей."""
        with pytest.raises(ValueError):
            Area(total=60.5, living=70.0)

    def test_invalid_kitchen_area(self):
        """Тест создания Area с площадью кухни больше общей."""
        with pytest.raises(ValueError):
            Area(total=60.5, kitchen=70.0)

    def test_equality(self):
        """Тест сравнения Area."""
        area1 = Area(total=60.5)
        area2 = Area(total=60.5)
        area3 = Area(total=70.0)

        assert area1 == area2
        assert area1 != area3
        assert area1 != 60.5  # Сравнение с другим типом
