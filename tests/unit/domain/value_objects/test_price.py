"""
Тесты для Value Object Price.
"""
import pytest
from src.domain.value_objects.price import Price


class TestPrice:
    """Тесты для Value Object Price."""

    def test_create_valid_price(self):
        """Тест создания валидной цены."""
        price = Price(50000, "USD")
        assert price.amount == 50000
        assert price.currency == "USD"
        assert str(price) == "50000 USD"

    def test_create_from_dict(self):
        """Тест создания Price из словаря."""
        price = Price.from_dict({"amount": 50000, "currency": "USD"})
        assert price.amount == 50000
        assert price.currency == "USD"

    def test_create_from_dict_missing_keys(self):
        """Тест создания Price из словаря с отсутствующими ключами."""
        price = Price.from_dict({})
        assert price is None

        price = Price.from_dict({"amount": 50000})
        assert price is None

        price = Price.from_dict({"currency": "USD"})
        assert price is None

    def test_negative_amount(self):
        """Тест создания Price с отрицательной суммой."""
        with pytest.raises(ValueError):
            Price(-50000, "USD")

    def test_empty_currency(self):
        """Тест создания Price с пустой валютой."""
        with pytest.raises(ValueError):
            Price(50000, "")

    def test_invalid_currency(self):
        """Тест создания Price с недопустимой валютой."""
        with pytest.raises(ValueError):
            Price(50000, "BTC")

    def test_is_free(self):
        """Тест проверки, является ли объект бесплатным."""
        free_price = Price(0, "USD")
        paid_price = Price(50000, "USD")

        assert free_price.is_free is True
        assert paid_price.is_free is False
