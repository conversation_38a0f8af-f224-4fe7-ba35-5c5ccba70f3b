"""
Тесты для Value Object Address.
"""
import pytest
from src.domain.value_objects.address import Address


class TestAddress:
    """Тесты для Value Object Address."""

    def test_create_valid_address(self):
        """Тест создания валидного адреса."""
        address = Address("Киев", "Печерский")
        assert address.city == "Киев"
        assert address.district == "Печерский"
        assert str(address) == "Киев, Печерский"

    def test_string_representation(self):
        """Тест строкового представления адреса."""
        # Полный формат: город, район
        address = Address(city="Киев", district="Печерский")
        assert str(address) == "Киев, Печерский"

        # Только город
        address = Address(city="Киев")
        assert str(address) == "Киев"

        # С улицей и номером дома
        address = Address(city="Киев", district="Печерский", street="Крещатик", house_number="1")
        assert str(address) == "Киев, Печерский, Крещатик, 1"

        # С полным адресом
        address = Address(city="Киев", full_address="г. Киев, Печерский район, ул. Крещатик, 1")
        assert str(address) == "г. Киев, Печерский район, ул. Крещатик, 1"

    def test_create_from_dict(self):
        """Тест создания Address из словаря."""
        address = Address.from_dict({"city": "Киев", "district": "Печерский"})
        assert address.city == "Киев"
        assert address.district == "Печерский"

        # Только с городом
        address = Address.from_dict({"city": "Киев"})
        assert address.city == "Киев"
        assert address.district is None

    def test_create_from_dict_missing_keys(self):
        """Тест создания Address из словаря с отсутствующими ключами."""
        address = Address.from_dict({})
        assert address is None

    def test_empty_city(self):
        """Тест создания Address с пустым городом."""
        with pytest.raises(ValueError):
            Address("", "Печерский")

    def test_equality(self):
        """Тест сравнения Address."""
        address1 = Address("Киев", "Печерский")
        address2 = Address("Киев", "Печерский")
        address3 = Address("Киев", "Шевченковский")
        address4 = Address("Львов", "Галицкий")

        assert address1 == address2
        assert address1 != address3
        assert address1 != address4
        assert address1 != "Киев, Печерский"  # Сравнение с другим типом
