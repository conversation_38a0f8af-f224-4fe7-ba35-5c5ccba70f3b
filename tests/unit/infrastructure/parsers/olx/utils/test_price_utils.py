"""
Тесты для утилит обработки цен OLX.
"""
import pytest
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.price_utils import PriceUtils


class TestPriceUtils:
    """Тесты для утилит обработки цен OLX."""

    def test_extract_price_from_text_valid(self):
        """Тест извлечения цены и валюты из валидного текста."""
        text = "50 000 грн."
        price, currency = PriceUtils.extract_price_from_text(text)

        assert price == 50000.0
        assert currency == "UAH"

    def test_extract_price_from_text_with_comma(self):
        """Тест извлечения цены с запятой."""
        text = "50,000 $"
        price, currency = PriceUtils.extract_price_from_text(text)

        assert price == 50.0
        assert currency == "USD"

    def test_extract_price_from_text_with_spaces(self):
        """Тест извлечения цены с пробелами."""
        text = "1 234 567 грн"
        price, currency = PriceUtils.extract_price_from_text(text)

        assert price == 1234567.0
        assert currency == "UAH"

    def test_extract_price_from_text_empty(self):
        """Тест извлечения цены из пустого текста."""
        text = ""
        price, currency = PriceUtils.extract_price_from_text(text)

        assert price is None
        assert currency is None

    def test_extract_price_from_text_invalid(self):
        """Тест извлечения цены из невалидного текста."""
        text = "Цена договорная"
        price, currency = PriceUtils.extract_price_from_text(text)

        assert price is None
        assert currency is None

    def test_extract_price_from_html_valid(self):
        """Тест извлечения цены из валидного HTML."""
        html = '<div><h3 class="css-ddweki">50 000 грн.</h3></div>'
        price, currency = PriceUtils.extract_price_from_html(html)

        assert price == 50000.0
        assert currency == "UAH"

    def test_extract_price_from_html_no_price(self):
        """Тест извлечения цены из HTML без цены."""
        html = '<div><h3 class="other-class">Текст</h3></div>'
        price, currency = PriceUtils.extract_price_from_html(html)

        assert price is None
        assert currency is None

    def test_extract_price_from_soup_valid(self):
        """Тест извлечения цены из валидного объекта BeautifulSoup."""
        soup = BeautifulSoup('<div><h3 class="css-ddweki">50 000 грн.</h3></div>', 'html.parser')
        price, currency = PriceUtils.extract_price_from_soup(soup)

        assert price == 50000.0
        assert currency == "UAH"

    def test_extract_price_from_soup_no_price(self):
        """Тест извлечения цены из объекта BeautifulSoup без цены."""
        soup = BeautifulSoup('<div><h3 class="other-class">Текст</h3></div>', 'html.parser')
        price, currency = PriceUtils.extract_price_from_soup(soup)

        assert price is None
        assert currency is None

    def test_format_price_integer(self):
        """Тест форматирования целочисленной цены."""
        price = 50000
        currency = "грн"

        result = PriceUtils.format_price(price, currency)

        assert result == "50 000 ГРН"

    def test_format_price_float(self):
        """Тест форматирования цены с плавающей точкой."""
        price = 50000.50
        currency = "грн"

        result = PriceUtils.format_price(price, currency)

        assert result == "50 000,50 ГРН"

    def test_format_price_float_integer_value(self):
        """Тест форматирования цены с плавающей точкой, но целым значением."""
        price = 50000.0
        currency = "грн"

        result = PriceUtils.format_price(price, currency)

        assert result == "50 000 ГРН"

    def test_convert_currency_same(self):
        """Тест конвертации цены в ту же валюту."""
        price = 50000
        from_currency = "USD"
        to_currency = "USD"
        exchange_rates = {"USD": 1.0, "EUR": 0.85, "UAH": 27.5}

        result = PriceUtils.convert_currency(price, from_currency, to_currency, exchange_rates)

        assert result == price

    def test_convert_currency_different(self):
        """Тест конвертации цены в другую валюту."""
        price = 1000
        from_currency = "USD"
        to_currency = "UAH"
        exchange_rates = {"USD": 1.0, "EUR": 0.85, "UAH": 27.5}

        result = PriceUtils.convert_currency(price, from_currency, to_currency, exchange_rates)

        assert result == 27500.0

    def test_convert_currency_missing_rate(self):
        """Тест конвертации цены с отсутствующим курсом."""
        price = 1000
        from_currency = "USD"
        to_currency = "GBP"
        exchange_rates = {"USD": 1.0, "EUR": 0.85, "UAH": 27.5}

        result = PriceUtils.convert_currency(price, from_currency, to_currency, exchange_rates)

        assert result is None

    def test_normalize_currency_known(self):
        """Тест нормализации известной валюты."""
        currency = "грн"

        result = PriceUtils.normalize_currency(currency)

        assert result == "UAH"

    def test_normalize_currency_dollar_sign(self):
        """Тест нормализации знака доллара."""
        currency = "$"

        result = PriceUtils.normalize_currency(currency)

        assert result == "USD"

    def test_normalize_currency_euro_sign(self):
        """Тест нормализации знака евро."""
        currency = "€"

        result = PriceUtils.normalize_currency(currency)

        assert result == "EUR"

    def test_normalize_currency_unknown(self):
        """Тест нормализации неизвестной валюты."""
        currency = "XYZ"

        result = PriceUtils.normalize_currency(currency)

        assert result == "XYZ"

    def test_is_negotiable_true(self):
        """Тест проверки договорной цены (положительный)."""
        text = "Цена договорная"

        result = PriceUtils.is_negotiable(text)

        assert result is True

    def test_is_negotiable_true_with_price(self):
        """Тест проверки договорной цены с указанной ценой (положительный)."""
        text = "50 000 грн, торг уместен"

        result = PriceUtils.is_negotiable(text)

        assert result is True

    def test_is_negotiable_false(self):
        """Тест проверки договорной цены (отрицательный)."""
        text = "50 000 грн"

        result = PriceUtils.is_negotiable(text)

        assert result is False
