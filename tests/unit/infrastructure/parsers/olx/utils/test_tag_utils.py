"""
Тесты для tag_utils.
"""
from unittest.mock import patch, MagicMock

import pytest
from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.tag_utils import TagExtractor, TagUtils


class TestTagExtractor:
    """Тесты для TagExtractor."""

    @pytest.fixture
    def sample_soup(self):
        """Фикстура для создания тестового объекта BeautifulSoup."""
        html = """
        <html>
            <head>
                <meta property="og:title" content="Test Title">
                <meta property="og:description" content="Test Description">
            </head>
            <body>
                <h1 class="css-1soizd2">Тестовый заголовок</h1>
                <div data-cy="ad_parameters">
                    <p>Тип недвижимости: Квартира</p>
                    <p>Количество комнат: 2</p>
                    <p>Общая площадь: 60 м²</p>
                    <p>Текст без двоеточия</p>
                </div>
                <div class="css-41yf00">
                    <p>Тип операции: Продажа</p>
                    <p>Этаж: 5</p>
                    <p>Этажность: 9</p>
                </div>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    def test_extract_tags(self, sample_soup):
        """Тест извлечения тегов."""
        extractor = TagExtractor()
        result = extractor.extract_tags(sample_soup)

        # Проверяем, что извлечены теги
        assert len(result) >= 3

        # Проверяем содержимое тегов
        assert {'key': 'Тип операции', 'value': 'Продажа'} in result
        assert {'key': 'Этаж', 'value': '5'} in result
        assert {'key': 'Этажность', 'value': '9'} in result

    def test_extract_tags_no_parameters(self):
        """Тест извлечения тегов без параметров."""
        soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
        extractor = TagExtractor()
        result = extractor.extract_tags(soup)

        assert result == []

    def test_extract_tags_exception(self):
        """Тест извлечения тегов с исключением."""
        soup = MagicMock()
        soup.find.side_effect = Exception("Test error")

        extractor = TagExtractor()
        result = extractor.extract_tags(soup)

        assert result == []

    def test_extract_mapped_tags(self, sample_soup):
        """Тест извлечения и маппинга тегов."""
        extractor = TagExtractor()

        # Патчим TAG_TO_FIELD_MAPPING для теста
        with patch('src.infrastructure.parsers.olx.utils.tag_utils.TAG_TO_FIELD_MAPPING', {
            'Тип операции': 'operation_type',
            'Этаж': 'floor',
            'Этажность': 'total_floors'
        }):
            result = extractor.extract_mapped_tags(sample_soup)

            # Проверяем маппинг тегов
            assert 'operation_type' in result
            assert result['operation_type'] == 'Продажа'
            assert 'floor' in result
            assert result['floor'] == '5'
            assert 'total_floors' in result
            assert result['total_floors'] == '9'


class TestTagUtils:
    """Тесты для TagUtils."""

    @pytest.fixture
    def sample_soup(self):
        """Фикстура для создания тестового объекта BeautifulSoup."""
        html = """
        <html>
            <body>
                <h1 class="css-1soizd2">Тестовый заголовок</h1>
                <div id="test-id">Тестовый ID</div>
                <div class="test-class">Тестовый класс 1</div>
                <div class="test-class">Тестовый класс 2</div>
                <p>Текст для поиска</p>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    def test_extract_tags_from_soup(self, sample_soup):
        """Тест извлечения тегов из soup."""
        result = TagUtils.extract_tags_from_soup(sample_soup)

        assert len(result) > 0
        assert "Тестовый" in result
        assert "заголовок" in result

    def test_extract_property_tags(self, sample_soup):
        """Тест извлечения тегов недвижимости."""
        # Проверяем, что метод возвращает пустой словарь при отсутствии параметров
        result = TagUtils.extract_property_tags(sample_soup)
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_extract_meta_tags(self, sample_soup):
        """Тест извлечения мета-тегов."""
        # Добавляем мета-теги в soup
        head = sample_soup.find('head') or sample_soup.new_tag('head')
        if not sample_soup.head:
            sample_soup.html.insert(0, head)

        meta1 = sample_soup.new_tag('meta')
        meta1['property'] = 'og:title'
        meta1['content'] = 'Test Title'
        head.append(meta1)

        meta2 = sample_soup.new_tag('meta')
        meta2['property'] = 'og:description'
        meta2['content'] = 'Test Description'
        head.append(meta2)

        result = TagUtils.extract_meta_tags(sample_soup)

        assert 'og:title' in result
        assert result['og:title'] == 'Test Title'
        assert 'og:description' in result
        assert result['og:description'] == 'Test Description'

    def test_find_element_by_text(self, sample_soup):
        """Тест поиска элемента по тексту."""
        result = TagUtils.find_element_by_text(sample_soup, "Текст для поиска")

        assert result is not None
        assert result.name == 'p'
        assert result.text == 'Текст для поиска'

    def test_find_element_by_text_not_found(self, sample_soup):
        """Тест поиска несуществующего элемента по тексту."""
        result = TagUtils.find_element_by_text(sample_soup, "Несуществующий текст")

        assert result is None

    def test_find_elements_by_class(self, sample_soup):
        """Тест поиска элементов по классу."""
        result = TagUtils.find_elements_by_class(sample_soup, "test-class")

        assert len(result) == 2
        assert all(element.name == 'div' for element in result)
        assert result[0].text == 'Тестовый класс 1'
        assert result[1].text == 'Тестовый класс 2'

    def test_find_elements_by_class_not_found(self, sample_soup):
        """Тест поиска несуществующих элементов по классу."""
        result = TagUtils.find_elements_by_class(sample_soup, "non-existent-class")

        assert result == []

    def test_find_element_by_id(self, sample_soup):
        """Тест поиска элемента по ID."""
        result = TagUtils.find_element_by_id(sample_soup, "test-id")

        assert result is not None
        assert result.name == 'div'
        assert result.text == 'Тестовый ID'

    def test_find_element_by_id_not_found(self, sample_soup):
        """Тест поиска несуществующего элемента по ID."""
        result = TagUtils.find_element_by_id(sample_soup, "non-existent-id")

        assert result is None
