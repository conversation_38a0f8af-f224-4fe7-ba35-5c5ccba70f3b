"""
Тесты для DateUtils.
"""
from datetime import datetime
from unittest.mock import patch

from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.date_utils import DateUtils


class TestDateUtils:
    """Тесты для DateUtils."""

    def test_extract_date_from_text_today(self):
        """Тест извлечения даты из текста с 'сегодня'."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Наследуем поведение datetime
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            text = "Сегодня, 10:30"
            result = DateUtils.extract_date_from_text(text)

            assert result == datetime(2023, 5, 15, 10, 30, 0)

    def test_extract_date_from_text_yesterday(self):
        """Тест извлечения даты из текста с 'вчера'."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Наследуем поведение datetime
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            text = "Вчера, 10:30"
            result = DateUtils.extract_date_from_text(text)

            assert result == datetime(2023, 5, 14, 10, 30, 0)

    def test_extract_date_from_text_day_month(self):
        """Тест извлечения даты из текста с днем и месяцем."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Наследуем поведение datetime
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            text = "10 мая, 10:30"
            result = DateUtils.extract_date_from_text(text)

            assert result == datetime(2023, 5, 10, 10, 30, 0)

    def test_extract_date_from_text_day_month_previous_year(self):
        """Тест извлечения даты из текста с днем и месяцем из предыдущего года."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Наследуем поведение datetime
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            text = "10 июня, 10:30"  # Июнь еще не наступил в текущем году
            result = DateUtils.extract_date_from_text(text)

            assert result == datetime(2022, 6, 10, 10, 30, 0)

    def test_extract_date_from_text_full_date(self):
        """Тест извлечения даты из текста с полной датой."""
        text = "10.05.2023, 10:30"
        result = DateUtils.extract_date_from_text(text)

        assert result == datetime(2023, 5, 10, 10, 30, 0)

    def test_extract_date_from_text_full_date_short_year(self):
        """Тест извлечения даты из текста с полной датой и коротким годом."""
        text = "10.05.23, 10:30"
        result = DateUtils.extract_date_from_text(text)

        assert result == datetime(2023, 5, 10, 10, 30, 0)

    def test_extract_date_from_text_empty(self):
        """Тест извлечения даты из пустого текста."""
        text = ""
        result = DateUtils.extract_date_from_text(text)

        assert result is None

    def test_extract_date_from_text_invalid(self):
        """Тест извлечения даты из невалидного текста."""
        text = "Дата не указана"
        result = DateUtils.extract_date_from_text(text)

        assert result is None

    def test_extract_date_from_soup(self):
        """Тест извлечения даты из объекта BeautifulSoup."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Наследуем поведение datetime
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            html = """
            <div>
                <span class="css-19yf5ek">Сегодня, 10:30</span>
            </div>
            """
            soup = BeautifulSoup(html, 'html.parser')
            result = DateUtils.extract_date_from_soup(soup)

            assert result == datetime(2023, 5, 15, 10, 30, 0)

    def test_extract_date_from_soup_empty(self):
        """Тест извлечения даты из пустого объекта BeautifulSoup."""
        html = """
        <div>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        result = DateUtils.extract_date_from_soup(soup)

        assert result is None

    def test_extract_date_from_soup_with_json_ld(self):
        """Тест извлечения даты из JSON-LD в BeautifulSoup."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            mock_datetime.fromisoformat = datetime.fromisoformat

            # HTML с JSON-LD
            html = """
            <html>
            <head>
                <script type="application/ld+json">
                {
                    "@type": "Product",
                    "name": "Тестовое объявление",
                    "datePublished": "2023-05-14T11:00:00"
                }
                </script>
            </head>
            <body>
                <div>
                    <span class="css-19yf5ek">Сегодня, 10:30</span>
                </div>
            </body>
            </html>
            """
            soup = BeautifulSoup(html, 'html.parser')

            # Патчим метод extract_date_from_json_ld, чтобы он возвращал дату из JSON-LD
            with patch('src.infrastructure.parsers.olx.utils.date_utils.DateUtils.extract_date_from_json_ld') as mock_extract_json_ld:
                mock_extract_json_ld.return_value = datetime(2023, 5, 14, 11, 0, 0)

                # Тестируем
                result = DateUtils.extract_date_from_soup(soup)

                # Проверяем, что метод extract_date_from_json_ld был вызван
                assert mock_extract_json_ld.called
                # Проверяем результат
                assert result == datetime(2023, 5, 14, 11, 0, 0)

    def test_format_date_today(self):
        """Тест форматирования даты 'сегодня'."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 15, 10, 30, 0)
            result = DateUtils.format_date(date)

            assert result == "Сегодня, 10:30"

    def test_format_date_yesterday(self):
        """Тест форматирования даты 'вчера'."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 14, 10, 30, 0)
            result = DateUtils.format_date(date)

            assert result == "Вчера, 10:30"

    def test_format_date_this_year(self):
        """Тест форматирования даты в текущем году."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 10, 10, 30, 0)
            result = DateUtils.format_date(date)

            assert "10" in result
            assert "10:30" in result

    def test_format_date_other_year(self):
        """Тест форматирования даты в другом году."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2022, 5, 10, 10, 30, 0)
            result = DateUtils.format_date(date)

            assert "10" in result
            assert "2022" in result
            assert "10:30" in result

    def test_get_relative_date_just_now(self):
        """Тест получения относительной даты 'только что'."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 15, 12, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "Только что"

    def test_get_relative_date_minutes(self):
        """Тест получения относительной даты в минутах."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 15, 11, 55, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "5 минут назад"

    def test_get_relative_date_hours(self):
        """Тест получения относительной даты в часах."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 15, 8, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "4 часа назад"

    def test_get_relative_date_days(self):
        """Тест получения относительной даты в днях."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 13, 12, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "2 дня назад"

    def test_get_relative_date_weeks(self):
        """Тест получения относительной даты в неделях."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 5, 1, 12, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "2 недели назад"

    def test_get_relative_date_months(self):
        """Тест получения относительной даты в месяцах."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2023, 3, 15, 12, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "2 месяца назад"

    def test_get_relative_date_years(self):
        """Тест получения относительной даты в годах."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            date = datetime(2021, 5, 15, 12, 0, 0)
            result = DateUtils.get_relative_date(date)

            assert result == "2 года назад"

    def test_is_ad_too_old_none_date(self):
        """Тест проверки устаревшего объявления с датой None."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем
            result = DateUtils.is_ad_too_old(None)

            assert result is True

    def test_is_ad_too_old_future_date(self):
        """Тест проверки устаревшего объявления с датой в будущем."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем
            future_date = datetime(2023, 5, 16, 12, 0, 0)  # Завтра
            result = DateUtils.is_ad_too_old(future_date)

            assert result is True

    def test_is_ad_too_old_date_only(self):
        """Тест проверки устаревшего объявления с датой без времени."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем дату, которая старше 24 часов (1 день)
            old_date = datetime(2023, 5, 13, 0, 0, 0)  # 2 дня назад, без времени
            result = DateUtils.is_ad_too_old(old_date, max_age_hours=24)

            assert result is True

            # Тестируем дату, которая не старше 48 часов (2 дня)
            result = DateUtils.is_ad_too_old(old_date, max_age_hours=48)

            assert result is False

    def test_is_ad_too_old_with_time(self):
        """Тест проверки устаревшего объявления с датой и временем."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)

            # Тестируем дату, которая старше 24 часов
            old_date = datetime(2023, 5, 14, 11, 0, 0)  # 25 часов назад
            result = DateUtils.is_ad_too_old(old_date, max_age_hours=24)

            assert result is True

            # Тестируем дату, которая не старше 24 часов
            recent_date = datetime(2023, 5, 14, 13, 0, 0)  # 23 часа назад
            result = DateUtils.is_ad_too_old(recent_date, max_age_hours=24)

            assert result is False

    def test_is_ad_too_old_string_date(self):
        """Тест проверки устаревшего объявления со строковой датой."""
        # Используем фиксированную текущую дату
        fixed_now = datetime(2023, 5, 15, 12, 0, 0)

        # Тестируем с использованием моков для ключевых методов
        with patch('src.infrastructure.parsers.olx.utils.date_utils.DateUtils.is_future_date', return_value=False), \
             patch('src.infrastructure.parsers.olx.utils.date_utils.DateUtils.extract_date_from_text') as mock_extract_date, \
             patch('src.infrastructure.parsers.olx.utils.date_utils.DateUtils.is_date_only', return_value=False):

            # Для первого теста - дата старше 24 часов
            old_date = datetime(2023, 5, 14, 11, 0, 0)  # 25 часов назад
            mock_extract_date.return_value = old_date

            # Тестируем строковую дату в формате ISO
            date_str = "2023-05-14T11:00:00"  # 25 часов назад

            # Ручной расчет разницы во времени
            time_diff = fixed_now - old_date
            hours_diff = time_diff.total_seconds() / 3600

            # Проверяем, что разница больше 24 часов
            assert hours_diff > 24

            # Тестируем метод is_ad_too_old с фиксированными параметрами
            result = DateUtils.is_ad_too_old(date_str, max_age_hours=24, current_time=fixed_now)

            # Объявление должно считаться устаревшим
            assert result is True

            # Для второго теста - дата не старше 24 часов
            recent_date = datetime(2023, 5, 14, 13, 0, 0)  # 23 часа назад
            mock_extract_date.return_value = recent_date

            # Тестируем строковую дату в формате DD.MM.YYYY HH:MM
            date_str = "14.05.2023 13:00"  # 23 часа назад

            # Ручной расчет разницы во времени
            time_diff = fixed_now - recent_date
            hours_diff = time_diff.total_seconds() / 3600

            # Проверяем, что разница меньше 24 часов
            assert hours_diff < 24

            # Тестируем метод is_ad_too_old с фиксированными параметрами
            result = DateUtils.is_ad_too_old(date_str, max_age_hours=24, current_time=fixed_now)

            # Объявление не должно считаться устаревшим
            assert result is False

    def test_extract_date_from_json_ld(self):
        """Тест извлечения даты из JSON-LD."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            mock_datetime.fromisoformat = datetime.fromisoformat

            # Создаем тестовые JSON-LD данные
            json_ld_data = [
                {
                    "@type": "Product",
                    "name": "Тестовое объявление",
                    "datePublished": "2023-05-14T11:00:00"
                }
            ]

            # Тестируем
            result = DateUtils.extract_date_from_json_ld(json_ld_data)

            assert result == datetime(2023, 5, 14, 11, 0, 0)

    def test_extract_date_from_json_ld_empty(self):
        """Тест извлечения даты из пустого JSON-LD."""
        # Тестируем пустой список
        result = DateUtils.extract_date_from_json_ld([])

        assert result is None

        # Тестируем список без даты
        json_ld_data = [
            {
                "@type": "Product",
                "name": "Тестовое объявление"
            }
        ]

        result = DateUtils.extract_date_from_json_ld(json_ld_data)

        assert result is None

    def test_is_date_only(self):
        """Тест проверки, содержит ли дата только дату без времени."""
        # Дата без времени
        date = datetime(2023, 5, 15, 0, 0, 0)
        result = DateUtils.is_date_only(date)

        assert result is True

        # Дата с временем
        date = datetime(2023, 5, 15, 12, 30, 0)
        result = DateUtils.is_date_only(date)

        assert result is False

    def test_compute_ad_age(self):
        """Тест вычисления возраста объявления."""
        with patch('src.infrastructure.parsers.olx.utils.date_utils.datetime') as mock_datetime:
            # Устанавливаем фиксированную текущую дату
            mock_now = datetime(2023, 5, 15, 12, 0, 0)
            mock_datetime.now.return_value = mock_now

            # Тестируем с разными единицами измерения
            date = datetime(2023, 5, 14, 12, 0, 0)  # 24 часа назад

            # В днях
            result = DateUtils.compute_ad_age(date, unit="days")
            assert result == 1.0

            # В часах
            result = DateUtils.compute_ad_age(date, unit="hours")
            assert result == 24.0

            # В минутах
            result = DateUtils.compute_ad_age(date, unit="minutes")
            assert result == 24.0 * 60

    def test_is_future_date(self):
        """Тест проверки, является ли дата будущей."""
        # Используем фиксированную текущую дату
        fixed_now = datetime(2023, 5, 15, 12, 0, 0)

        # Дата в будущем
        future_date = datetime(2023, 5, 16, 12, 0, 0)  # Завтра
        result = DateUtils.is_future_date(future_date, current_time=fixed_now)

        assert result is True

        # Дата в прошлом
        past_date = datetime(2023, 5, 14, 12, 0, 0)  # Вчера
        result = DateUtils.is_future_date(past_date, current_time=fixed_now)

        assert result is False

        # Патчим метод extract_date_from_text, чтобы он возвращал реальные даты
        with patch('src.infrastructure.parsers.olx.utils.date_utils.DateUtils.extract_date_from_text') as mock_extract_date:
            # Для строковой даты в будущем
            mock_extract_date.return_value = datetime(2023, 5, 16, 12, 0, 0)  # Завтра

            # Строковая дата в будущем
            future_date_str = "2023-05-16T12:00:00"  # Завтра
            result = DateUtils.is_future_date(future_date_str, current_time=fixed_now)

            assert result is True

            # Для строковой даты в прошлом
            mock_extract_date.return_value = datetime(2023, 5, 14, 12, 0, 0)  # Вчера

            # Строковая дата в прошлом
            past_date_str = "2023-05-14T12:00:00"  # Вчера
            result = DateUtils.is_future_date(past_date_str, current_time=fixed_now)

            assert result is False
