"""
Тесты для BuildingUtils.
"""
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.building_utils import BuildingUtils


class TestBuildingUtils:
    """Тесты для BuildingUtils."""

    def test_extract_floor_info_from_text_with_slash(self):
        """Тест извлечения информации о этаже и этажности из текста с разделителем '/'."""
        text = "5/9 этаж"
        floor, total_floors = BuildingUtils.extract_floor_info_from_text(text)
        
        assert floor == 5
        assert total_floors == 9

    def test_extract_floor_info_from_text_with_word(self):
        """Тест извлечения информации о этаже и этажности из текста с разделителем 'из'."""
        text = "5 из 9 этаж"
        floor, total_floors = BuildingUtils.extract_floor_info_from_text(text)
        
        assert floor == 5
        assert total_floors == 9

    def test_extract_floor_info_from_text_floor_only(self):
        """Тест извлечения информации только о этаже из текста."""
        text = "5 этаж"
        floor, total_floors = BuildingUtils.extract_floor_info_from_text(text)
        
        assert floor == 5
        assert total_floors is None

    def test_extract_floor_info_from_text_empty(self):
        """Тест извлечения информации о этаже и этажности из пустого текста."""
        text = ""
        floor, total_floors = BuildingUtils.extract_floor_info_from_text(text)
        
        assert floor is None
        assert total_floors is None

    def test_extract_floor_info_from_text_invalid(self):
        """Тест извлечения информации о этаже и этажности из невалидного текста."""
        text = "Этаж не указан"
        floor, total_floors = BuildingUtils.extract_floor_info_from_text(text)
        
        assert floor is None
        assert total_floors is None

    def test_extract_floor_info_from_parameters_both(self):
        """Тест извлечения информации о этаже и этажности из параметров с обоими значениями."""
        params = {
            "Этаж": "5",
            "Этажность": "9"
        }
        floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
        
        assert floor == 5
        assert total_floors == 9

    def test_extract_floor_info_from_parameters_floor_only(self):
        """Тест извлечения информации только о этаже из параметров."""
        params = {
            "Этаж": "5"
        }
        floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
        
        assert floor == 5
        assert total_floors is None

    def test_extract_floor_info_from_parameters_total_floors_only(self):
        """Тест извлечения информации только о этажности из параметров."""
        params = {
            "Этажность": "9"
        }
        floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
        
        assert floor is None
        assert total_floors == 9

    def test_extract_floor_info_from_parameters_empty(self):
        """Тест извлечения информации о этаже и этажности из пустых параметров."""
        params = {}
        floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
        
        assert floor is None
        assert total_floors is None

    def test_extract_floor_info_from_parameters_invalid(self):
        """Тест извлечения информации о этаже и этажности из невалидных параметров."""
        params = {
            "Этаж": "не указан",
            "Этажность": "не указана"
        }
        floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
        
        assert floor is None
        assert total_floors is None

    def test_extract_floor_info_from_soup(self):
        """Тест извлечения информации о этаже и этажности из объекта BeautifulSoup."""
        html = """
        <div class="css-1epmoz1">
            <p>Этаж: 5</p>
            <p>Этажность: 9</p>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        floor, total_floors = BuildingUtils.extract_floor_info_from_soup(soup)
        
        assert floor == 5
        assert total_floors == 9

    def test_extract_floor_info_from_soup_empty(self):
        """Тест извлечения информации о этаже и этажности из пустого объекта BeautifulSoup."""
        html = """
        <div class="css-1epmoz1">
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        floor, total_floors = BuildingUtils.extract_floor_info_from_soup(soup)
        
        assert floor is None
        assert total_floors is None

    def test_extract_building_type_from_parameters(self):
        """Тест извлечения типа здания из параметров."""
        params = {
            "Тип строения": "Кирпичный"
        }
        building_type = BuildingUtils.extract_building_type_from_parameters(params)
        
        assert building_type == "Кирпичный"

    def test_extract_building_type_from_parameters_empty(self):
        """Тест извлечения типа здания из пустых параметров."""
        params = {}
        building_type = BuildingUtils.extract_building_type_from_parameters(params)
        
        assert building_type is None

    def test_extract_building_type_from_soup(self):
        """Тест извлечения типа здания из объекта BeautifulSoup."""
        html = """
        <div class="css-1epmoz1">
            <p>Тип строения: Кирпичный</p>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        building_type = BuildingUtils.extract_building_type_from_soup(soup)
        
        assert building_type == "Кирпичный"

    def test_extract_building_type_from_soup_empty(self):
        """Тест извлечения типа здания из пустого объекта BeautifulSoup."""
        html = """
        <div class="css-1epmoz1">
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        building_type = BuildingUtils.extract_building_type_from_soup(soup)
        
        assert building_type is None

    def test_normalize_building_type_known(self):
        """Тест нормализации известного типа здания."""
        building_type = "кирпичный"
        normalized = BuildingUtils.normalize_building_type(building_type)
        
        assert normalized == "Кирпичный"

    def test_normalize_building_type_alias(self):
        """Тест нормализации типа здания с использованием алиаса."""
        building_type = "кирпич"
        normalized = BuildingUtils.normalize_building_type(building_type)
        
        assert normalized == "Кирпичный"

    def test_normalize_building_type_unknown(self):
        """Тест нормализации неизвестного типа здания."""
        building_type = "необычный"
        normalized = BuildingUtils.normalize_building_type(building_type)
        
        assert normalized == "Необычный"

    def test_normalize_building_type_empty(self):
        """Тест нормализации пустого типа здания."""
        building_type = ""
        normalized = BuildingUtils.normalize_building_type(building_type)
        
        assert normalized == ""

    def test_format_floor_info_both(self):
        """Тест форматирования информации о этаже и этажности с обоими значениями."""
        floor = 5
        total_floors = 9
        formatted = BuildingUtils.format_floor_info(floor, total_floors)
        
        assert formatted == "5/9 этаж"

    def test_format_floor_info_floor_only(self):
        """Тест форматирования информации только о этаже."""
        floor = 5
        total_floors = None
        formatted = BuildingUtils.format_floor_info(floor, total_floors)
        
        assert formatted == "5 этаж"

    def test_format_floor_info_none(self):
        """Тест форматирования информации о этаже и этажности с None."""
        floor = None
        total_floors = None
        formatted = BuildingUtils.format_floor_info(floor, total_floors)
        
        assert formatted == ""
