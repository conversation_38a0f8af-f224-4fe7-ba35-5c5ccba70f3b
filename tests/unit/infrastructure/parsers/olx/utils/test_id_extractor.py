"""
Тесты для утилит извлечения ID OLX.
"""
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.id_extractor import IdExtractor


class TestIdExtractor:
    """Тесты для утилит извлечения ID OLX."""

    def test_extract_id_from_url_valid(self):
        """Тест извлечения ID из валидного URL."""
        url = "https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html"
        ad_id = IdExtractor.extract_id_from_url(url)

        assert ad_id == "kvartira-ID12345678"

    def test_extract_id_from_url_with_query_params(self):
        """Тест извлечения ID из URL с параметрами запроса."""
        url = "https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html?param=value"
        ad_id = IdExtractor.extract_id_from_url(url)

        assert ad_id == "kvartira-ID12345678"

    def test_extract_id_from_url_empty(self):
        """Тест извлечения ID из пустого URL."""
        url = ""
        ad_id = IdExtractor.extract_id_from_url(url)

        assert ad_id is None

    def test_extract_id_from_url_invalid(self):
        """Тест извлечения ID из невалидного URL."""
        url = "https://www.olx.ua/d/uk/kvartiry/"
        ad_id = IdExtractor.extract_id_from_url(url)

        assert ad_id is None

    def test_extract_id_from_html_span(self):
        """Тест извлечения ID из HTML с элементом span."""
        html = '''
        <html>
            <head></head>
            <body>
                <span class="css-w85dhy">ID: 880459925</span>
            </body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id == "880459925"

    def test_extract_id_from_html_span_with_different_format(self):
        """Тест извлечения ID из HTML с элементом span в другом формате."""
        html = '''
        <html>
            <head></head>
            <body>
                <span class="css-w85dhy">880459925</span>
            </body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id == "880459925"

    def test_extract_id_from_html_meta(self):
        """Тест извлечения ID из HTML с мета-тегом (запасной вариант)."""
        html = '''
        <html>
            <head>
                <meta property="og:url" content="https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html" />
            </head>
            <body></body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id == "kvartira-ID12345678"

    def test_extract_id_from_html_script(self):
        """Тест извлечения ID из HTML со скриптом."""
        html = '''
        <html>
            <head></head>
            <body>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "id": "12345678"
                        }
                    };
                </script>
            </body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id == "12345678"

    def test_extract_id_from_json_ld(self):
        """Тест извлечения ID из JSON-LD разметки."""
        html = '''
        <html>
            <head>
                <script type="application/ld+json">
                {
                    "@context": "http://schema.org",
                    "@type": "Product",
                    "productID": "987654321",
                    "name": "Квартира в аренду"
                }
                </script>
            </head>
            <body></body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id == "987654321"

    def test_extract_id_from_html_empty(self):
        """Тест извлечения ID из пустого HTML."""
        html = ""
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id is None

    def test_extract_id_from_html_no_id(self):
        """Тест извлечения ID из HTML без ID."""
        html = '''
        <html>
            <head></head>
            <body>
                <div>Текст объявления</div>
            </body>
        </html>
        '''
        ad_id = IdExtractor.extract_id_from_html(html)

        assert ad_id is None

    def test_extract_ids_from_listing_html_valid(self):
        """Тест извлечения ID из HTML страницы листинга."""
        html = '''
        <html>
            <body>
                <div data-cy="l-card" data-id="12345678">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html">Квартира</a>
                </div>
                <div data-cy="l-card" data-id="87654321">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/dom-ID87654321.html">Дом</a>
                </div>
            </body>
        </html>
        '''
        ids = IdExtractor.extract_ids_from_listing_html(html)

        assert len(ids) == 2
        assert "12345678" in ids
        assert "87654321" in ids

    def test_extract_ids_from_listing_html_no_data_id(self):
        """Тест извлечения ID из HTML страницы листинга без атрибута data-id."""
        html = '''
        <html>
            <body>
                <div data-cy="l-card">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html">Квартира</a>
                </div>
                <div data-cy="l-card">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/dom-ID87654321.html">Дом</a>
                </div>
            </body>
        </html>
        '''
        ids = IdExtractor.extract_ids_from_listing_html(html)

        assert len(ids) == 2
        assert "kvartira-ID12345678" in ids
        assert "dom-ID87654321" in ids

    def test_extract_ids_from_listing_html_empty(self):
        """Тест извлечения ID из пустого HTML страницы листинга."""
        html = ""
        ids = IdExtractor.extract_ids_from_listing_html(html)

        assert ids == []

    def test_extract_ids_from_listing_html_no_ads(self):
        """Тест извлечения ID из HTML страницы листинга без объявлений."""
        html = '''
        <html>
            <body>
                <div>Нет объявлений</div>
            </body>
        </html>
        '''
        ids = IdExtractor.extract_ids_from_listing_html(html)

        assert ids == []

    def test_extract_ids_from_soup_valid(self):
        """Тест извлечения ID из объекта BeautifulSoup."""
        html = '''
        <html>
            <body>
                <div data-cy="l-card" data-id="12345678">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html">Квартира</a>
                </div>
                <div data-cy="l-card" data-id="87654321">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/dom-ID87654321.html">Дом</a>
                </div>
            </body>
        </html>
        '''
        soup = BeautifulSoup(html, 'html.parser')
        ids = IdExtractor.extract_ids_from_soup(soup)

        assert len(ids) == 2
        assert "12345678" in ids
        assert "87654321" in ids

    def test_extract_ids_from_soup_no_data_id(self):
        """Тест извлечения ID из объекта BeautifulSoup без атрибута data-id."""
        html = '''
        <html>
            <body>
                <div data-cy="l-card">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/kvartira-ID12345678.html">Квартира</a>
                </div>
                <div data-cy="l-card">
                    <a href="https://www.olx.ua/d/uk/obyavlenie/dom-ID87654321.html">Дом</a>
                </div>
            </body>
        </html>
        '''
        soup = BeautifulSoup(html, 'html.parser')
        ids = IdExtractor.extract_ids_from_soup(soup)

        assert len(ids) == 2
        assert "kvartira-ID12345678" in ids
        assert "dom-ID87654321" in ids

    def test_extract_ids_from_soup_no_ads(self):
        """Тест извлечения ID из объекта BeautifulSoup без объявлений."""
        html = '''
        <html>
            <body>
                <div>Нет объявлений</div>
            </body>
        </html>
        '''
        soup = BeautifulSoup(html, 'html.parser')
        ids = IdExtractor.extract_ids_from_soup(soup)

        assert ids == []

    def test_is_valid_id_valid(self):
        """Тест проверки валидного ID."""
        ad_id = "12345678"
        result = IdExtractor.is_valid_id(ad_id)

        assert result is True

    def test_is_valid_id_with_letters(self):
        """Тест проверки валидного ID с буквами."""
        ad_id = "kvartira-ID12345678"
        result = IdExtractor.is_valid_id(ad_id)

        assert result is True

    def test_is_valid_id_with_id_prefix(self):
        """Тест проверки валидного ID с префиксом ID."""
        ad_id = "ID12345678"
        result = IdExtractor.is_valid_id(ad_id)

        assert result is True

    def test_is_valid_id_empty(self):
        """Тест проверки пустого ID."""
        ad_id = ""
        result = IdExtractor.is_valid_id(ad_id)

        assert result is False

    def test_is_valid_id_invalid_chars(self):
        """Тест проверки ID с недопустимыми символами."""
        ad_id = "12345678!"
        result = IdExtractor.is_valid_id(ad_id)

        assert result is False
