"""
Тесты для LocationUtils.
"""
from unittest.mock import MagicMock, patch

import pytest
from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.location_utils import LocationUtils


class TestLocationUtils:
    """Тесты для LocationUtils."""

    @pytest.fixture
    def sample_soup(self):
        """Фикстура для создания тестового объекта BeautifulSoup."""
        html = """
        <html>
            <body>
                <a href="/nedvizhimost/kvartiry/prodazha/">
                    <p>Киев, Печерский, ул. Крещатик</p>
                </a>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "map": {
                                "lat": 50.4501,
                                "lon": 30.5234
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    @pytest.fixture
    def sample_soup_without_location(self):
        """Фикстура для создания тестового объекта BeautifulSoup без местоположения."""
        html = """
        <html>
            <body>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "map": {
                                "lat": 50.4501,
                                "lon": 30.5234
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    @pytest.fixture
    def sample_soup_without_coordinates(self):
        """Фикстура для создания тестового объекта BeautifulSoup без координат."""
        html = """
        <html>
            <body>
                <a href="/nedvizhimost/kvartiry/prodazha/">
                    <p>Киев, Печерский, ул. Крещатик</p>
                </a>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "map": {}
                        }
                    };
                </script>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    def test_extract_location_from_text_full(self):
        """Тест извлечения местоположения из текста с полной информацией."""
        text = "Киев, Печерский, ул. Крещатик"
        result = LocationUtils.extract_location_from_text(text)

        assert result["city"] == "Киев"
        assert result["district"] == "Печерский"
        assert result["street"] == "ул. Крещатик"
        assert result["full_address"] == "Киев, Печерский, ул. Крещатик"

    def test_extract_location_from_text_city_district(self):
        """Тест извлечения местоположения из текста с городом и районом."""
        text = "Киев, Печерский"
        result = LocationUtils.extract_location_from_text(text)

        assert result["city"] == "Киев"
        assert result["district"] == "Печерский"
        assert result["full_address"] == "Киев, Печерский"

    def test_extract_location_from_text_city_only(self):
        """Тест извлечения местоположения из текста только с городом."""
        text = "Киев"
        result = LocationUtils.extract_location_from_text(text)

        assert result["city"] == "Киев"
        assert result["full_address"] == "Киев"

    def test_extract_location_from_text_empty(self):
        """Тест извлечения местоположения из пустого текста."""
        result = LocationUtils.extract_location_from_text("")

        assert result == {}

    def test_extract_location_from_text_none(self):
        """Тест извлечения местоположения из None."""
        result = LocationUtils.extract_location_from_text(None)

        assert result == {}

    def test_extract_location_from_soup(self, sample_soup):
        """Тест извлечения местоположения из объекта BeautifulSoup."""
        result = LocationUtils.extract_location_from_soup(sample_soup)

        assert result["city"] == "Киев"
        assert result["district"] == "Печерский"
        assert result["street"] == "ул. Крещатик"
        assert result["full_address"] == "Киев, Печерский, ул. Крещатик"

    def test_extract_location_from_soup_without_location(self, sample_soup_without_location):
        """Тест извлечения местоположения из объекта BeautifulSoup без местоположения."""
        result = LocationUtils.extract_location_from_soup(sample_soup_without_location)

        assert "city" in result
        assert result["city"] == "Чернигов"
        assert "full_address" in result

    def test_extract_location_from_soup_exception(self, sample_soup):
        """Тест извлечения местоположения из объекта BeautifulSoup с исключением."""
        with patch('bs4.BeautifulSoup.select_one', side_effect=Exception("Test error")), \
             patch('builtins.print') as mock_print:

            result = LocationUtils.extract_location_from_soup(sample_soup)

            assert "city" in result
            assert result["city"] == "Чернигов"
            assert "full_address" in result
            mock_print.assert_called_once()
            assert "Ошибка при извлечении местоположения" in mock_print.call_args[0][0]

    def test_extract_coordinates_from_soup(self, sample_soup):
        """Тест извлечения координат из объекта BeautifulSoup."""
        result = LocationUtils.extract_coordinates_from_soup(sample_soup)

        assert result == (50.4501, 30.5234)

    def test_extract_coordinates_from_soup_without_coordinates(self, sample_soup_without_coordinates):
        """Тест извлечения координат из объекта BeautifulSoup без координат."""
        result = LocationUtils.extract_coordinates_from_soup(sample_soup_without_coordinates)

        assert result == (None, None)

    def test_extract_coordinates_from_soup_invalid_coordinates(self):
        """Тест извлечения координат из объекта BeautifulSoup с невалидными координатами."""
        html = """
        <html>
            <body>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "map": {
                                "lat": "invalid",
                                "lon": "invalid"
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """
        soup = BeautifulSoup(html, 'html.parser')

        result = LocationUtils.extract_coordinates_from_soup(soup)

        assert result == (None, None)

    def test_extract_coordinates_from_soup_exception(self, sample_soup):
        """Тест извлечения координат из объекта BeautifulSoup с исключением."""
        with patch('bs4.BeautifulSoup.find_all', side_effect=Exception("Test error")), \
             patch('builtins.print') as mock_print:

            result = LocationUtils.extract_coordinates_from_soup(sample_soup)

            assert result == (None, None)
            mock_print.assert_called_once()
            assert "Ошибка при извлечении координат" in mock_print.call_args[0][0]

    def test_normalize_city_known(self):
        """Тест нормализации известного города."""
        result = LocationUtils.normalize_city("киев")

        assert result == "Киев"

    def test_normalize_city_unknown(self):
        """Тест нормализации неизвестного города."""
        result = LocationUtils.normalize_city("Буча")

        assert result == "Буча"

    def test_normalize_city_empty(self):
        """Тест нормализации пустого города."""
        result = LocationUtils.normalize_city("")

        assert result == ""

    def test_normalize_city_none(self):
        """Тест нормализации None."""
        result = LocationUtils.normalize_city(None)

        assert result is None

    def test_normalize_district_with_rayon(self):
        """Тест нормализации района со словом 'район'."""
        result = LocationUtils.normalize_district("Печерский район")

        assert result == "Печерский"

    def test_normalize_district_without_rayon(self):
        """Тест нормализации района без слова 'район'."""
        result = LocationUtils.normalize_district("Печерский")

        assert result == "Печерский"

    def test_normalize_district_empty(self):
        """Тест нормализации пустого района."""
        result = LocationUtils.normalize_district("")

        assert result == ""

    def test_normalize_district_none(self):
        """Тест нормализации None."""
        result = LocationUtils.normalize_district(None)

        assert result is None

    def test_format_location_full(self):
        """Тест форматирования местоположения с полной информацией."""
        location = {
            "city": "Киев",
            "district": "Печерский",
            "street": "ул. Крещатик"
        }

        result = LocationUtils.format_location(location)

        assert result == "Киев, Печерский, ул. Крещатик"

    def test_format_location_city_district(self):
        """Тест форматирования местоположения с городом и районом."""
        location = {
            "city": "Киев",
            "district": "Печерский"
        }

        result = LocationUtils.format_location(location)

        assert result == "Киев, Печерский"

    def test_format_location_city_only(self):
        """Тест форматирования местоположения только с городом."""
        location = {
            "city": "Киев"
        }

        result = LocationUtils.format_location(location)

        assert result == "Киев"

    def test_format_location_empty(self):
        """Тест форматирования пустого местоположения."""
        result = LocationUtils.format_location({})

        assert result == ""

    def test_get_nearby_cities_known(self):
        """Тест получения ближайших городов для известного города."""
        result = LocationUtils.get_nearby_cities("Киев")

        assert result == ['Бровары', 'Борисполь', 'Вишневое', 'Ирпень', 'Буча', 'Вышгород', 'Обухов', 'Украинка', 'Васильков']

    def test_get_nearby_cities_unknown(self):
        """Тест получения ближайших городов для неизвестного города."""
        result = LocationUtils.get_nearby_cities("Буча")

        assert result == []

    def test_get_nearby_cities_with_radius(self):
        """Тест получения ближайших городов с указанным радиусом."""
        # Радиус не влияет на результат в текущей реализации
        result = LocationUtils.get_nearby_cities("Киев", radius=100)

        assert result == ['Бровары', 'Борисполь', 'Вишневое', 'Ирпень', 'Буча', 'Вышгород', 'Обухов', 'Украинка', 'Васильков']
