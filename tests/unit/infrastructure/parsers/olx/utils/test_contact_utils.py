"""
Тесты для ContactUtils.
"""
import json
from unittest.mock import MagicMock, patch

import pytest
from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.contact_utils import ContactUtils


class TestContactUtils:
    """Тесты для ContactUtils."""

    @pytest.fixture
    def sample_soup(self):
        """Фикстура для создания тестового объекта BeautifulSoup."""
        html = """
        <div>
            <h4 class="css-1lcz6o7">Иван Петров</h4>
        </div>
        """
        return BeautifulSoup(html, 'html.parser')

    @pytest.fixture
    def sample_soup_without_contact(self):
        """Фикстура для создания тестового объекта BeautifulSoup без контакта."""
        html = """
        <div>
            <h4 class="other-class">Иван Петров</h4>
        </div>
        """
        return BeautifulSoup(html, 'html.parser')

    @pytest.fixture
    def sample_zyte_response(self):
        """Фикстура для создания тестового ответа Zyte API."""
        return {
            'httpResponseBody': json.dumps({
                'data': {
                    'phones': [
                        {'number': '+380991234567'},
                        {'number': '+380997654321'}
                    ]
                }
            })
        }

    def test_extract_contact_name_from_soup(self, sample_soup):
        """Тест извлечения имени контакта из объекта BeautifulSoup."""
        result = ContactUtils.extract_contact_name_from_soup(sample_soup)

        assert result == "Иван Петров"

    def test_extract_contact_name_from_soup_no_contact(self, sample_soup_without_contact):
        """Тест извлечения имени контакта из объекта BeautifulSoup без контакта."""
        result = ContactUtils.extract_contact_name_from_soup(sample_soup_without_contact)

        assert result is None

    def test_extract_contact_name_from_soup_exception(self, sample_soup):
        """Тест извлечения имени контакта из объекта BeautifulSoup с исключением."""
        # Используем патч для имитации исключения при выборе элемента
        with patch.object(BeautifulSoup, 'select_one', side_effect=Exception("Test error")):
            result = ContactUtils.extract_contact_name_from_soup(sample_soup)

            assert result is None

    def test_extract_phones_from_api_no_api_key(self):
        """Тест извлечения телефонов из API без API ключа."""
        result = ContactUtils.extract_phones_from_api(
            ad_id="12345678",
            base_url="https://www.olx.ua",
            user_agent="Test User Agent",
            zyte_api_key=None
        )

        assert result == []

    def test_extract_phones_from_api_success(self, sample_zyte_response):
        """Тест успешного извлечения телефонов из API."""
        with patch('requests.post') as mock_post:
            # Настраиваем мок для имитации успешного ответа
            mock_response = MagicMock()
            mock_response.json.return_value = sample_zyte_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = ContactUtils.extract_phones_from_api(
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == ["+380991234567", "+380997654321"]

            # Проверяем, что запрос был отправлен с правильными параметрами
            mock_post.assert_called_once()
            args, kwargs = mock_post.call_args
            assert kwargs['headers']['Authorization'] == 'Basic test_key'
            assert kwargs['json']['url'] == 'https://www.olx.ua/api/v1/offers/12345678/limited-phones/'

    def test_extract_phones_from_api_request_exception(self):
        """Тест извлечения телефонов из API с исключением запроса."""
        with patch('requests.post', side_effect=Exception("Test error")):
            result = ContactUtils.extract_phones_from_api(
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == []

    def test_extract_phones_from_api_json_decode_error(self):
        """Тест извлечения телефонов из API с ошибкой декодирования JSON."""
        with patch('requests.post') as mock_post:
            # Настраиваем мок для имитации ответа с невалидным JSON
            mock_response = MagicMock()
            mock_response.json.return_value = {'httpResponseBody': 'invalid json'}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = ContactUtils.extract_phones_from_api(
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == []

    def test_extract_phones_from_api_no_phones_in_response(self):
        """Тест извлечения телефонов из API без телефонов в ответе."""
        with patch('requests.post') as mock_post:
            # Настраиваем мок для имитации ответа без телефонов
            mock_response = MagicMock()
            mock_response.json.return_value = {'httpResponseBody': json.dumps({'data': {}})}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response

            result = ContactUtils.extract_phones_from_api(
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == []

    def test_format_phone_with_country_code(self):
        """Тест форматирования телефонного номера с кодом страны."""
        result = ContactUtils.format_phone("+380991234567")

        assert result == "+38 (099) 123-45-67"

    def test_format_phone_without_country_code(self):
        """Тест форматирования телефонного номера без кода страны."""
        result = ContactUtils.format_phone("0991234567")

        assert result == "+38 (099) 123-45-67"

    def test_format_phone_invalid(self):
        """Тест форматирования невалидного телефонного номера."""
        result = ContactUtils.format_phone("invalid")

        assert result == "invalid"

    def test_normalize_phones(self):
        """Тест нормализации списка телефонных номеров."""
        phones = ["+380991234567", "0991234567", "+380991234567"]
        result = ContactUtils.normalize_phones(phones)

        assert len(result) == 1
        assert result[0] == "+38 (099) 123-45-67"

    def test_extract_contact_info(self, sample_soup, sample_zyte_response):
        """Тест извлечения контактной информации."""
        with patch.object(ContactUtils, 'extract_contact_name_from_soup', return_value="Иван Петров"), \
             patch.object(ContactUtils, 'extract_phones_from_api', return_value=["+380991234567"]), \
             patch.object(ContactUtils, 'normalize_phones', return_value=["+38 (099) 123-45-67"]):

            result = ContactUtils.extract_contact_info(
                soup=sample_soup,
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == {
                'name': "Иван Петров",
                'phones': ["+38 (099) 123-45-67"]
            }

    def test_extract_contact_info_no_name(self, sample_soup, sample_zyte_response):
        """Тест извлечения контактной информации без имени."""
        with patch.object(ContactUtils, 'extract_contact_name_from_soup', return_value=None), \
             patch.object(ContactUtils, 'extract_phones_from_api', return_value=["+380991234567"]), \
             patch.object(ContactUtils, 'normalize_phones', return_value=["+38 (099) 123-45-67"]):

            result = ContactUtils.extract_contact_info(
                soup=sample_soup,
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == {
                'phones': ["+38 (099) 123-45-67"]
            }

    def test_extract_contact_info_no_phones(self, sample_soup):
        """Тест извлечения контактной информации без телефонов."""
        with patch.object(ContactUtils, 'extract_contact_name_from_soup', return_value="Иван Петров"), \
             patch.object(ContactUtils, 'extract_phones_from_api', return_value=[]):

            result = ContactUtils.extract_contact_info(
                soup=sample_soup,
                ad_id="12345678",
                base_url="https://www.olx.ua",
                user_agent="Test User Agent",
                zyte_api_key="test_key"
            )

            assert result == {
                'name': "Иван Петров"
            }
