"""
Тесты для RoomUtils.
"""
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.room_utils import RoomUtils


class TestRoomUtils:
    """Тесты для RoomUtils."""

    def test_extract_rooms_count_from_text_with_dash(self):
        """Тест извлечения количества комнат из текста с дефисом."""
        text = "2-комнатная квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_text_with_abbreviation(self):
        """Тест извлечения количества комнат из текста с сокращением."""
        text = "2-комн. квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_text_with_short_abbreviation(self):
        """Тест извлечения количества комнат из текста с коротким сокращением."""
        text = "2-к. квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_text_without_suffix(self):
        """Тест извлечения количества комнат из текста без суффикса."""
        text = "2 квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_text_studio(self):
        """Тест извлечения количества комнат из текста со словом 'студия'."""
        text = "Квартира-студия"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count == 1

    def test_extract_rooms_count_from_text_empty(self):
        """Тест извлечения количества комнат из пустого текста."""
        text = ""
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count is None

    def test_extract_rooms_count_from_text_invalid(self):
        """Тест извлечения количества комнат из невалидного текста."""
        text = "Количество комнат не указано"
        rooms_count = RoomUtils.extract_rooms_count_from_text(text)
        
        assert rooms_count is None

    def test_extract_rooms_count_from_parameters(self):
        """Тест извлечения количества комнат из параметров."""
        params = {
            "Количество комнат": "2"
        }
        rooms_count = RoomUtils.extract_rooms_count_from_parameters(params)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_parameters_empty(self):
        """Тест извлечения количества комнат из пустых параметров."""
        params = {}
        rooms_count = RoomUtils.extract_rooms_count_from_parameters(params)
        
        assert rooms_count is None

    def test_extract_rooms_count_from_soup_from_parameters(self):
        """Тест извлечения количества комнат из объекта BeautifulSoup через параметры."""
        html = """
        <div class="css-1epmoz1">
            <p>Количество комнат: 2</p>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        rooms_count = RoomUtils.extract_rooms_count_from_soup(soup)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_soup_from_title(self):
        """Тест извлечения количества комнат из объекта BeautifulSoup через заголовок."""
        html = """
        <div>
            <h1 class="css-1soizd2">2-комнатная квартира</h1>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        rooms_count = RoomUtils.extract_rooms_count_from_soup(soup)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_soup_empty(self):
        """Тест извлечения количества комнат из пустого объекта BeautifulSoup."""
        html = """
        <div>
        </div>
        """
        soup = BeautifulSoup(html, 'html.parser')
        rooms_count = RoomUtils.extract_rooms_count_from_soup(soup)
        
        assert rooms_count is None

    def test_extract_rooms_count_from_title_with_dash(self):
        """Тест извлечения количества комнат из заголовка с дефисом."""
        title = "2-комнатная квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_title(title)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_title_with_abbreviation(self):
        """Тест извлечения количества комнат из заголовка с сокращением."""
        title = "2-комн. квартира"
        rooms_count = RoomUtils.extract_rooms_count_from_title(title)
        
        assert rooms_count == 2

    def test_extract_rooms_count_from_title_studio(self):
        """Тест извлечения количества комнат из заголовка со словом 'студия'."""
        title = "Квартира-студия"
        rooms_count = RoomUtils.extract_rooms_count_from_title(title)
        
        assert rooms_count == 1

    def test_extract_rooms_count_from_title_empty(self):
        """Тест извлечения количества комнат из пустого заголовка."""
        title = ""
        rooms_count = RoomUtils.extract_rooms_count_from_title(title)
        
        assert rooms_count is None

    def test_extract_rooms_count_from_title_invalid(self):
        """Тест извлечения количества комнат из невалидного заголовка."""
        title = "Квартира в центре"
        rooms_count = RoomUtils.extract_rooms_count_from_title(title)
        
        assert rooms_count is None

    def test_format_rooms_count_1(self):
        """Тест форматирования количества комнат (1)."""
        rooms_count = 1
        formatted = RoomUtils.format_rooms_count(rooms_count)
        
        assert formatted == "1-комнатная"

    def test_format_rooms_count_2(self):
        """Тест форматирования количества комнат (2)."""
        rooms_count = 2
        formatted = RoomUtils.format_rooms_count(rooms_count)
        
        assert formatted == "2-комнатная"

    def test_format_rooms_count_3(self):
        """Тест форматирования количества комнат (3)."""
        rooms_count = 3
        formatted = RoomUtils.format_rooms_count(rooms_count)
        
        assert formatted == "3-комнатная"

    def test_format_rooms_count_4(self):
        """Тест форматирования количества комнат (4)."""
        rooms_count = 4
        formatted = RoomUtils.format_rooms_count(rooms_count)
        
        assert formatted == "4-комнатная"

    def test_format_rooms_count_other(self):
        """Тест форматирования количества комнат (другое)."""
        rooms_count = 5
        formatted = RoomUtils.format_rooms_count(rooms_count)
        
        assert formatted == "5-комнатная"
