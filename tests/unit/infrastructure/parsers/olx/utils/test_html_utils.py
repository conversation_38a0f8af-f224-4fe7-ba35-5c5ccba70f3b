"""
Тесты для html_utils.
"""
from unittest.mock import patch

import pytest
from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import (
    clean_text, extract_meta_property, extract_number, extract_url_param,
    extract_url_path_segment, get_description, get_images, get_pagination_info,
    get_parameters, get_title, make_absolute_url, parse_html
)


class TestHtmlUtils:
    """Тесты для html_utils."""

    @pytest.fixture
    def sample_soup(self):
        """Фикстура для создания тестового объекта BeautifulSoup."""
        html = """
        <html>
            <head>
                <meta property="og:title" content="Test Title">
                <meta property="og:description" content="Test Description">
            </head>
            <body>
                <h1 class="css-1soizd2">Тестовый заголовок</h1>
                <div data-cy="ad_description">
                    <div>Тестовое описание</div>
                </div>
                <div class="css-1epmoz1">
                    <p>Параметр 1: Значение 1</p>
                    <p>Параметр 2: Значение 2</p>
                    <p>Текст без двоеточия</p>
                </div>
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="swiper-zoom-container">
                            <img src="https://example.com/image1.jpg" alt="Image 1">
                        </div>
                    </div>
                    <div class="swiper-slide">
                        <div class="swiper-zoom-container">
                            <img src="https://example.com/image2.jpg" alt="Image 2">
                        </div>
                    </div>
                </div>
                <ul class="pagination-list">
                    <li><a href="?page=1">1</a></li>
                    <li class="active"><span>2</span></li>
                    <li><a href="?page=3">3</a></li>
                    <li><a href="?page=4">4</a></li>
                    <li><a href="?page=5">5</a></li>
                </ul>
            </body>
        </html>
        """
        return BeautifulSoup(html, 'html.parser')

    def test_clean_text(self):
        """Тест очистки текста."""
        text = "  Тестовый   текст  \n  с переносами  \t  строк  "
        result = clean_text(text)

        assert result == "Тестовый текст с переносами строк"

    def test_clean_text_empty(self):
        """Тест очистки пустого текста."""
        result = clean_text("")

        assert result == ""

    def test_clean_text_none(self):
        """Тест очистки None."""
        result = clean_text(None)

        assert result == ""

    def test_extract_number(self):
        """Тест извлечения числа из текста."""
        text = "Цена: 50 000,50 грн"
        result = extract_number(text)

        assert result == 50000.50

    def test_extract_number_with_dot(self):
        """Тест извлечения числа с точкой из текста."""
        text = "Цена: 50 000.50 грн"
        result = extract_number(text)

        assert result == 50000.50

    def test_extract_number_no_number(self):
        """Тест извлечения числа из текста без числа."""
        text = "Цена: не указана"
        result = extract_number(text)

        assert result is None

    def test_extract_number_empty(self):
        """Тест извлечения числа из пустого текста."""
        result = extract_number("")

        assert result is None

    def test_extract_number_none(self):
        """Тест извлечения числа из None."""
        result = extract_number(None)

        assert result is None

    def test_extract_number_invalid(self):
        """Тест извлечения числа из невалидного текста."""
        text = "Цена: 50,000,00 грн"
        result = extract_number(text)

        assert result is None

    def test_extract_url_param(self):
        """Тест извлечения параметра из URL."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/?page=2&search[filter_float_price:from]=50000"
        result = extract_url_param(url, "page")

        assert result == "2"

    def test_extract_url_param_not_found(self):
        """Тест извлечения несуществующего параметра из URL."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/?page=2"
        result = extract_url_param(url, "limit")

        assert result is None

    def test_extract_url_param_empty(self):
        """Тест извлечения параметра из пустого URL."""
        result = extract_url_param("", "page")

        assert result is None

    def test_extract_url_param_none(self):
        """Тест извлечения параметра из None."""
        result = extract_url_param(None, "page")

        assert result is None

    def test_extract_url_path_segment(self):
        """Тест извлечения сегмента пути из URL."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/"
        result = extract_url_path_segment(url, 0)

        assert result == "nedvizhimost"

    def test_extract_url_path_segment_not_found(self):
        """Тест извлечения несуществующего сегмента пути из URL."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/"
        result = extract_url_path_segment(url, 10)

        assert result is None

    def test_extract_url_path_segment_empty(self):
        """Тест извлечения сегмента пути из пустого URL."""
        result = extract_url_path_segment("", 0)

        assert result is None

    def test_extract_url_path_segment_none(self):
        """Тест извлечения сегмента пути из None."""
        result = extract_url_path_segment(None, 0)

        assert result is None

    def test_extract_meta_property(self, sample_soup):
        """Тест извлечения мета-тега."""
        result = extract_meta_property(sample_soup, "og:title")

        assert result == "Test Title"

    def test_extract_meta_property_not_found(self, sample_soup):
        """Тест извлечения несуществующего мета-тега."""
        result = extract_meta_property(sample_soup, "og:image")

        assert result is None

    def test_make_absolute_url(self):
        """Тест преобразования относительного URL в абсолютный."""
        base_url = "https://www.olx.ua"
        relative_url = "/nedvizhimost/kvartiry/prodazha/"
        result = make_absolute_url(base_url, relative_url)

        assert result == "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/"

    def test_make_absolute_url_already_absolute(self):
        """Тест преобразования абсолютного URL."""
        base_url = "https://www.olx.ua"
        absolute_url = "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/"
        result = make_absolute_url(base_url, absolute_url)

        assert result == "https://www.olx.ua/nedvizhimost/kvartiry/prodazha/"

    def test_parse_html(self):
        """Тест парсинга HTML."""
        html = "<html><body><h1>Test</h1></body></html>"
        result = parse_html(html)

        assert isinstance(result, BeautifulSoup)
        assert result.h1 is not None and result.h1.text == "Test"

    def test_get_title(self, sample_soup):
        """Тест извлечения заголовка."""
        result = get_title(sample_soup)

        assert result == "Тестовый заголовок"

    def test_get_title_not_found(self):
        """Тест извлечения несуществующего заголовка."""
        soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
        result = get_title(soup)

        assert result is None

    def test_get_description(self, sample_soup):
        """Тест извлечения описания."""
        result = get_description(sample_soup)

        assert result == "Тестовое описание"

    def test_get_description_not_found(self):
        """Тест извлечения несуществующего описания."""
        soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
        result = get_description(soup)

        assert result is None

    def test_get_parameters(self, sample_soup):
        """Тест извлечения параметров."""
        result = get_parameters(sample_soup)

        assert result == {
            "Параметр 1": "Значение 1",
            "Параметр 2": "Значение 2"
        }

    def test_get_parameters_exception(self):
        """Тест извлечения параметров с исключением."""
        with patch('bs4.BeautifulSoup.select', side_effect=Exception("Test error")):
            soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
            result = get_parameters(soup)

            assert result == {}

    def test_get_images(self, sample_soup):
        """Тест извлечения изображений."""
        result = get_images(sample_soup)

        assert result == [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        ]

    def test_get_images_with_limit(self, sample_soup):
        """Тест извлечения изображений с лимитом."""
        result = get_images(sample_soup, max_images=1)

        assert result == ["https://example.com/image1.jpg"]

    def test_get_images_exception(self):
        """Тест извлечения изображений с исключением."""
        with patch('bs4.BeautifulSoup.select', side_effect=Exception("Test error")):
            soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
            result = get_images(soup)

            assert result == []

    def test_get_pagination_info(self, sample_soup):
        """Тест извлечения информации о пагинации."""
        result = get_pagination_info(sample_soup)

        assert result == (2, 5)

    def test_get_pagination_info_no_pagination(self):
        """Тест извлечения информации о пагинации без пагинации."""
        soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
        result = get_pagination_info(soup)

        assert result == (1, 1)

    def test_get_pagination_info_exception(self):
        """Тест извлечения информации о пагинации с исключением."""
        with patch('bs4.BeautifulSoup.select_one', side_effect=Exception("Test error")):
            soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
            result = get_pagination_info(soup)

            assert result == (1, 1)
