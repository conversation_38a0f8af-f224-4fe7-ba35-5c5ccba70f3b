"""
Тесты для утилит обработки площади OLX.
"""
from bs4 import BeautifulSoup
from src.infrastructure.parsers.olx.utils.area_utils import AreaUtils


class TestAreaUtils:
    """Тесты для утилит обработки площади OLX."""

    def test_extract_area_from_text_valid(self):
        """Тест извлечения площади из валидного текста."""
        text = "60.5 м²"
        area = AreaUtils.extract_area_from_text(text)

        assert area == 60.5

    def test_extract_area_from_text_with_comma(self):
        """Тест извлечения площади с запятой."""
        text = "60,5 м²"
        area = AreaUtils.extract_area_from_text(text)

        assert area == 60.5

    def test_extract_area_from_text_with_different_units(self):
        """Тест извлечения площади с разными единицами измерения."""
        # Квадратные метры
        assert AreaUtils.extract_area_from_text("60.5 м²") == 60.5
        assert AreaUtils.extract_area_from_text("60.5 кв.м") == 60.5
        assert AreaUtils.extract_area_from_text("60.5 м2") == 60.5
        assert AreaUtils.extract_area_from_text("60.5 кв.м.") == 60.5

    def test_extract_area_from_text_without_units(self):
        """Тест извлечения площади без единиц измерения."""
        text = "60.5"
        area = AreaUtils.extract_area_from_text(text)

        assert area == 60.5

    def test_extract_area_from_text_empty(self):
        """Тест извлечения площади из пустого текста."""
        text = ""
        area = AreaUtils.extract_area_from_text(text)

        assert area is None

    def test_extract_area_from_text_invalid(self):
        """Тест извлечения площади из невалидного текста."""
        text = "Площадь не указана"
        area = AreaUtils.extract_area_from_text(text)

        assert area is None

    def test_extract_areas_from_parameters_all_areas(self):
        """Тест извлечения всех типов площадей из параметров."""
        params = {
            'Общая площадь': '60.5 м²',
            'Жилая площадь': '40.0 м²',
            'Площадь кухни': '10.0 м²',
            'Площадь участка': '6 соток'
        }

        areas = AreaUtils.extract_areas_from_parameters(params)

        assert areas['total_area'] == 60.5
        assert areas['living_area'] == 40.0
        assert areas['kitchen_area'] == 10.0
        assert areas['land_area'] == 6.0

    def test_extract_areas_from_parameters_partial_areas(self):
        """Тест извлечения части площадей из параметров."""
        params = {
            'Общая площадь': '60.5 м²',
            'Площадь кухни': '10.0 м²'
        }

        areas = AreaUtils.extract_areas_from_parameters(params)

        assert areas['total_area'] == 60.5
        assert areas['kitchen_area'] == 10.0
        assert 'living_area' not in areas
        assert 'land_area' not in areas

    def test_extract_areas_from_parameters_no_areas(self):
        """Тест извлечения площадей из параметров без площадей."""
        params = {
            'Этаж': '5',
            'Количество комнат': '2'
        }

        areas = AreaUtils.extract_areas_from_parameters(params)

        assert areas == {}

    def test_extract_areas_from_soup_valid(self):
        """Тест извлечения площадей из валидного объекта BeautifulSoup."""
        html = '''
        <div class="css-1epmoz1">
            <p>Общая площадь: 60.5 м²</p>
            <p>Жилая площадь: 40.0 м²</p>
            <p>Площадь кухни: 10.0 м²</p>
        </div>
        '''
        soup = BeautifulSoup(html, 'html.parser')

        areas = AreaUtils.extract_areas_from_soup(soup)

        assert areas['total_area'] == 60.5
        assert areas['living_area'] == 40.0
        assert areas['kitchen_area'] == 10.0

    def test_extract_areas_from_soup_no_areas(self):
        """Тест извлечения площадей из объекта BeautifulSoup без площадей."""
        html = '''
        <div class="css-1epmoz1">
            <p>Этаж: 5</p>
            <p>Количество комнат: 2</p>
        </div>
        '''
        soup = BeautifulSoup(html, 'html.parser')

        areas = AreaUtils.extract_areas_from_soup(soup)

        assert areas == {}

    def test_format_area_integer(self):
        """Тест форматирования целочисленной площади."""
        area = 60.0

        result = AreaUtils.format_area(area)

        assert result == "60 м²"

    def test_format_area_float(self):
        """Тест форматирования площади с плавающей точкой."""
        area = 60.5

        result = AreaUtils.format_area(area)

        assert result == "60,5 м²"

    def test_extract_land_area_with_unit_sotki(self):
        """Тест извлечения площади участка с единицей измерения 'сотки'."""
        text = "6 соток"
        area, unit = AreaUtils.extract_land_area(text)

        assert area == 6.0
        assert unit == "сот"

    def test_extract_land_area_with_unit_ga(self):
        """Тест извлечения площади участка с единицей измерения 'га'."""
        text = "1.5 га"
        area, unit = AreaUtils.extract_land_area(text)

        assert area == 1.5
        assert unit == "га"

    def test_extract_land_area_with_unit_m2(self):
        """Тест извлечения площади участка с единицей измерения 'м²'."""
        text = "600 м²"
        area, unit = AreaUtils.extract_land_area(text)

        assert area == 600.0
        assert unit == "м²"

    def test_extract_land_area_without_unit(self):
        """Тест извлечения площади участка без единицы измерения."""
        text = "6"
        area, unit = AreaUtils.extract_land_area(text)

        assert area == 6.0
        assert unit == "сот"

    def test_extract_land_area_empty(self):
        """Тест извлечения площади участка из пустого текста."""
        text = ""
        area, unit = AreaUtils.extract_land_area(text)

        assert area is None
        assert unit is None

    def test_extract_land_area_invalid(self):
        """Тест извлечения площади участка из невалидного текста."""
        text = "Площадь не указана"
        area, unit = AreaUtils.extract_land_area(text)

        assert area is None
        assert unit is None

    def test_convert_land_area_same_unit(self):
        """Тест конвертации площади участка в ту же единицу измерения."""
        area = 6.0
        from_unit = "сот"
        to_unit = "сот"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 6.0

    def test_convert_land_area_sotki_to_m2(self):
        """Тест конвертации площади участка из соток в квадратные метры."""
        area = 6.0
        from_unit = "сот"
        to_unit = "м²"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 600.0

    def test_convert_land_area_sotki_to_ga(self):
        """Тест конвертации площади участка из соток в гектары."""
        area = 100.0
        from_unit = "сот"
        to_unit = "га"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 1.0

    def test_convert_land_area_ga_to_sotki(self):
        """Тест конвертации площади участка из гектаров в сотки."""
        area = 1.0
        from_unit = "га"
        to_unit = "сот"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 100.0

    def test_convert_land_area_ga_to_m2(self):
        """Тест конвертации площади участка из гектаров в квадратные метры."""
        area = 1.0
        from_unit = "га"
        to_unit = "м²"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 10000.0

    def test_convert_land_area_m2_to_sotki(self):
        """Тест конвертации площади участка из квадратных метров в сотки."""
        area = 600.0
        from_unit = "м²"
        to_unit = "сот"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 6.0

    def test_convert_land_area_m2_to_ga(self):
        """Тест конвертации площади участка из квадратных метров в гектары."""
        area = 10000.0
        from_unit = "м²"
        to_unit = "га"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 1.0

    def test_convert_land_area_unknown_unit(self):
        """Тест конвертации площади участка с неизвестной единицей измерения."""
        area = 6.0
        from_unit = "unknown"
        to_unit = "м²"

        result = AreaUtils.convert_land_area(area, from_unit, to_unit)

        assert result == 6.0

    def test_extract_area_info_from_text_all_areas(self):
        """Тест извлечения всех типов площадей из текста."""
        text = """
        Общая площадь: 60.5 м²
        Жилая площадь: 40.0 м²
        Площадь кухни: 10.0 м²
        Площадь участка: 6 соток
        """

        result = AreaUtils.extract_area_info_from_text(text)

        assert result['total_area'] == 60.5
        assert result['living_area'] == 40.0
        assert result['kitchen_area'] == 10.0
        assert result['land_area'] == 600.0  # 6 соток = 600 м²

    def test_extract_area_info_from_text_partial_areas(self):
        """Тест извлечения части площадей из текста."""
        text = """
        Общая площадь: 60.5 м²
        Площадь кухни: 10.0 м²
        """

        result = AreaUtils.extract_area_info_from_text(text)

        assert result['total_area'] == 60.5
        assert result['kitchen_area'] == 10.0
        assert result['living_area'] is None
        assert result['land_area'] is None

    def test_extract_area_info_from_text_no_areas(self):
        """Тест извлечения площадей из текста без площадей."""
        text = """
        Этаж: 5
        Количество комнат: 2
        """

        result = AreaUtils.extract_area_info_from_text(text)

        assert result['total_area'] is None
        assert result['living_area'] is None
        assert result['kitchen_area'] is None
        assert result['land_area'] is None

    def test_validate_area_info_valid(self):
        """Тест валидации корректной информации о площадях."""
        total_area = 60.5
        living_area = 40.0
        kitchen_area = 10.0
        land_area = 600.0

        result = AreaUtils.validate_area_info(total_area, living_area, kitchen_area, land_area)

        assert result is True

    def test_validate_area_info_negative_area(self):
        """Тест валидации информации о площадях с отрицательной площадью."""
        total_area = -60.5
        living_area = 40.0
        kitchen_area = 10.0
        land_area = 600.0

        result = AreaUtils.validate_area_info(total_area, living_area, kitchen_area, land_area)

        assert result is False

    def test_validate_area_info_living_greater_than_total(self):
        """Тест валидации информации о площадях с жилой площадью больше общей."""
        total_area = 60.5
        living_area = 70.0
        kitchen_area = 10.0
        land_area = 600.0

        result = AreaUtils.validate_area_info(total_area, living_area, kitchen_area, land_area)

        assert result is False

    def test_validate_area_info_kitchen_greater_than_total(self):
        """Тест валидации информации о площадях с площадью кухни больше общей."""
        total_area = 60.5
        living_area = 40.0
        kitchen_area = 70.0
        land_area = 600.0

        result = AreaUtils.validate_area_info(total_area, living_area, kitchen_area, land_area)

        assert result is False

    def test_validate_area_info_too_large_total(self):
        """Тест валидации информации о площадях с слишком большой общей площадью."""
        total_area = 2000.0
        living_area = 40.0
        kitchen_area = 10.0
        land_area = 600.0

        result = AreaUtils.validate_area_info(total_area, living_area, kitchen_area, land_area)

        assert result is False

    def test_normalize_area_valid(self):
        """Тест нормализации валидной площади."""
        area = 60.555

        result = AreaUtils.normalize_area(area)

        assert result == 60.55

    def test_normalize_area_none(self):
        """Тест нормализации None."""
        area = None

        result = AreaUtils.normalize_area(area)

        assert result is None

    def test_format_area_with_unit(self):
        """Тест форматирования площади с указанием единицы измерения."""
        area = 60.5
        unit = "сот."

        result = AreaUtils.format_area(area, unit)

        # Проверяем, что результат содержит правильное число и единицу измерения
        assert "60,5" in result
        assert "сот" in result

    def test_format_area_with_conversion_to_sotki(self):
        """Тест форматирования площади с конвертацией в сотки."""
        area = 600.0
        unit = "м²"
        convert_to_sotki = True

        result = AreaUtils.format_area(area, unit, convert_to_sotki)

        assert result == "6 сот."
