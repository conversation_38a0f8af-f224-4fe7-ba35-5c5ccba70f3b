"""
Тесты для TextProcessors.
"""
from unittest.mock import patch

import pytest

from src.infrastructure.parsers.olx.utils.text_processors import TextProcessors


class TestTextProcessors:
    """Тесты для TextProcessors."""

    def test_clean_title(self):
        """Тест очистки заголовка объявления."""
        title = "  Продам 2-к квартиру в Киеве!!! #Срочно@  "
        result = TextProcessors.clean_title(title)

        assert result == "Продам 2-к квартиру в Киеве Срочно"

    def test_clean_title_empty(self):
        """Тест очистки пустого заголовка."""
        result = TextProcessors.clean_title("")

        assert result == ""

    def test_clean_title_none(self):
        """Тест очистки None."""
        result = TextProcessors.clean_title(None)

        assert result == ""

    def test_clean_description(self):
        """Тест очистки описания объявления."""
        description = "  Продам <b>2-к</b> квартиру в Киеве!!! \n\n#Срочно@  "
        result = TextProcessors.clean_description(description)

        assert result == "Продам 2-к квартиру в Киеве!!! #Срочно@"

    def test_clean_description_empty(self):
        """Тест очистки пустого описания."""
        result = TextProcessors.clean_description("")

        assert result == ""

    def test_clean_description_none(self):
        """Тест очистки None."""
        result = TextProcessors.clean_description(None)

        assert result == ""

    def test_extract_keywords_from_title(self):
        """Тест извлечения ключевых слов из заголовка объявления."""
        title = "Продам 2-к квартиру в Киеве на Печерске"
        result = TextProcessors.extract_keywords_from_title(title)

        assert "Продам" in result
        assert "квартиру" in result
        assert "Киеве" in result
        assert "Печерске" in result
        assert "2-к" not in result  # слово короче 4 символов

    def test_extract_keywords_from_title_empty(self):
        """Тест извлечения ключевых слов из пустого заголовка."""
        result = TextProcessors.extract_keywords_from_title("")

        assert result == []

    def test_extract_keywords_from_title_none(self):
        """Тест извлечения ключевых слов из None."""
        result = TextProcessors.extract_keywords_from_title(None)

        assert result == []

    def test_extract_keywords_from_description(self):
        """Тест извлечения ключевых слов из описания объявления."""
        description = "Продам 2-к квартиру в Киеве на Печерске. Хороший ремонт, мебель, техника."
        result = TextProcessors.extract_keywords_from_description(description)

        assert "Продам" in result
        assert "квартиру" in result
        assert "Киеве" in result
        assert "Печерске." in result
        assert "Хороший" in result
        assert "ремонт," in result
        assert "мебель," in result
        assert "техника." in result
        assert "2-к" not in result  # слово короче 4 символов

    def test_extract_keywords_from_description_empty(self):
        """Тест извлечения ключевых слов из пустого описания."""
        result = TextProcessors.extract_keywords_from_description("")

        assert result == []

    def test_extract_keywords_from_description_none(self):
        """Тест извлечения ключевых слов из None."""
        result = TextProcessors.extract_keywords_from_description(None)

        assert result == []

    def test_extract_keywords_from_description_limit(self):
        """Тест ограничения количества ключевых слов из описания."""
        # Создаем длинное описание с более чем 50 словами длиннее 3 символов
        description = " ".join(["слово" + str(i) for i in range(100)])
        result = TextProcessors.extract_keywords_from_description(description)

        assert len(result) == 50

    def test_extract_property_features_from_description(self):
        """Тест извлечения особенностей недвижимости из описания объявления."""
        description = "Продам квартиру с ремонтом, с мебелью, с балконом, с кондиционером."
        result = TextProcessors.extract_property_features_from_description(description)

        assert "с ремонтом" in result
        assert "с мебелью" in result
        assert "с балконом" in result
        assert "с кондиционером" in result

    def test_extract_property_features_from_description_empty(self):
        """Тест извлечения особенностей недвижимости из пустого описания."""
        result = TextProcessors.extract_property_features_from_description("")

        assert result == []

    def test_extract_property_features_from_description_none(self):
        """Тест извлечения особенностей недвижимости из None."""
        result = TextProcessors.extract_property_features_from_description(None)

        assert result == []

    def test_extract_property_features_from_description_no_features(self):
        """Тест извлечения особенностей недвижимости из описания без особенностей."""
        description = "Продам квартиру в Киеве на Печерске."
        result = TextProcessors.extract_property_features_from_description(description)

        assert result == []

    def test_extract_property_type_from_text(self):
        """Тест извлечения типа недвижимости из текста."""
        # В реальной реализации тип недвижимости ищется по точному совпадению слова
        text = "Продам квартира в Киеве на Печерске."
        result = TextProcessors.extract_property_type_from_text(text)

        assert result == "Квартира"

    def test_extract_property_type_from_text_multiple_types(self):
        """Тест извлечения типа недвижимости из текста с несколькими типами."""
        # В реальной реализации тип недвижимости ищется по точному совпадению слова
        text = "Продам квартира или дом в Киеве на Печерске."
        result = TextProcessors.extract_property_type_from_text(text)

        # Возвращается первый найденный тип
        assert result in ["Квартира", "Дом"]

    def test_extract_property_type_from_text_empty(self):
        """Тест извлечения типа недвижимости из пустого текста."""
        result = TextProcessors.extract_property_type_from_text("")

        assert result is None

    def test_extract_property_type_from_text_none(self):
        """Тест извлечения типа недвижимости из None."""
        result = TextProcessors.extract_property_type_from_text(None)

        assert result is None

    def test_extract_property_type_from_text_no_type(self):
        """Тест извлечения типа недвижимости из текста без типа."""
        text = "Продам недвижимость в Киеве на Печерске."
        result = TextProcessors.extract_property_type_from_text(text)

        assert result is None

    def test_extract_operation_type_from_text(self):
        """Тест извлечения типа операции из текста."""
        text = "Продам квартиру в Киеве на Печерске."
        result = TextProcessors.extract_operation_type_from_text(text)

        assert result == "Продажа"

    def test_extract_operation_type_from_text_multiple_types(self):
        """Тест извлечения типа операции из текста с несколькими типами."""
        text = "Продам или сдам квартиру в Киеве на Печерске."
        result = TextProcessors.extract_operation_type_from_text(text)

        # Возвращается первый найденный тип
        assert result == "Продажа"

    def test_extract_operation_type_from_text_empty(self):
        """Тест извлечения типа операции из пустого текста."""
        result = TextProcessors.extract_operation_type_from_text("")

        assert result is None

    def test_extract_operation_type_from_text_none(self):
        """Тест извлечения типа операции из None."""
        result = TextProcessors.extract_operation_type_from_text(None)

        assert result is None

    def test_extract_operation_type_from_text_no_type(self):
        """Тест извлечения типа операции из текста без типа."""
        text = "Квартира в Киеве на Печерске."
        result = TextProcessors.extract_operation_type_from_text(text)

        assert result is None

    def test_extract_price_from_text(self):
        """Тест извлечения цены и валюты из текста."""
        text = "Цена: 50 000 USD"
        price, currency = TextProcessors.extract_price_from_text(text)

        assert price == 50000.0
        assert currency == "USD"

    def test_extract_price_from_text_with_comma(self):
        """Тест извлечения цены с запятой и валюты из текста."""
        text = "Цена: 50,000 USD"
        price, currency = TextProcessors.extract_price_from_text(text)

        assert price == 50.0
        assert currency == "USD"

    def test_extract_price_from_text_empty(self):
        """Тест извлечения цены и валюты из пустого текста."""
        price, currency = TextProcessors.extract_price_from_text("")

        assert price is None
        assert currency is None

    def test_extract_price_from_text_none(self):
        """Тест извлечения цены и валюты из None."""
        price, currency = TextProcessors.extract_price_from_text(None)

        assert price is None
        assert currency is None

    def test_extract_price_from_text_no_price(self):
        """Тест извлечения цены и валюты из текста без цены."""
        text = "Квартира в Киеве на Печерске."
        price, currency = TextProcessors.extract_price_from_text(text)

        assert price is None
        assert currency is None

    def test_extract_price_from_text_invalid_price(self):
        """Тест извлечения невалидной цены и валюты из текста."""
        text = "Цена: пятьдесят тысяч USD"
        price, currency = TextProcessors.extract_price_from_text(text)

        assert price is None
        assert currency is None
