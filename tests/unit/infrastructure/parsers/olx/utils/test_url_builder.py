"""
Тесты для утилит построения URL для OLX.
"""
import pytest
from src.infrastructure.parsers.olx.utils.url_builder import (
    add_query_params,
    build_ad_url,
    build_phone_api_url,
    build_category_url,
    build_page_url
)


class TestUrlBuilder:
    """Тесты для утилит построения URL для OLX."""

    def test_add_query_params_empty_url(self):
        """Тест добавления параметров запроса к URL без параметров."""
        url = "https://www.olx.ua/nedvizhimost/"
        params = {"search[filter_float_price:from]": "10000", "search[filter_float_price:to]": "50000"}
        
        result = add_query_params(url, params)
        
        assert "search%5Bfilter_float_price%3Afrom%5D=10000" in result
        assert "search%5Bfilter_float_price%3Ato%5D=50000" in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/")

    def test_add_query_params_existing_params(self):
        """Тест добавления параметров запроса к URL с существующими параметрами."""
        url = "https://www.olx.ua/nedvizhimost/?search[filter_float_price:from]=5000"
        params = {"search[filter_float_price:to]": "50000", "search[filter_float_area:from]": "40"}
        
        result = add_query_params(url, params)
        
        assert "search%5Bfilter_float_price%3Afrom%5D=5000" in result
        assert "search%5Bfilter_float_price%3Ato%5D=50000" in result
        assert "search%5Bfilter_float_area%3Afrom%5D=40" in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/")

    def test_add_query_params_override_existing(self):
        """Тест перезаписи существующих параметров запроса."""
        url = "https://www.olx.ua/nedvizhimost/?search[filter_float_price:from]=5000"
        params = {"search[filter_float_price:from]": "10000"}
        
        result = add_query_params(url, params)
        
        assert "search%5Bfilter_float_price%3Afrom%5D=10000" in result
        assert "search%5Bfilter_float_price%3Afrom%5D=5000" not in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/")

    def test_build_ad_url(self):
        """Тест формирования URL объявления."""
        base_url = "https://www.olx.ua/d"
        ad_id = "ID12345678"
        
        result = build_ad_url(base_url, ad_id)
        
        assert result == "https://www.olx.ua/d/uk/obyavlenie/ID12345678.html"

    def test_build_phone_api_url(self):
        """Тест формирования URL для API телефонов."""
        base_url = "https://www.olx.ua"
        ad_id = "12345678"
        
        result = build_phone_api_url(base_url, ad_id)
        
        assert result == "https://www.olx.ua/api/v1/offers/12345678/limited-phones/"

    def test_build_category_url_with_path(self):
        """Тест формирования URL категории с путем."""
        base_url = "https://www.olx.ua"
        category_path = "nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"
        
        result = build_category_url(base_url, category_path)
        
        assert result == "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"

    def test_build_category_url_with_full_url(self):
        """Тест формирования URL категории с полным URL."""
        base_url = "https://www.olx.ua"
        category_path = "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"
        
        result = build_category_url(base_url, category_path)
        
        assert result == "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"

    def test_build_category_url_with_params(self):
        """Тест формирования URL категории с параметрами."""
        base_url = "https://www.olx.ua"
        category_path = "nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"
        params = {"search[filter_float_price:from]": "10000", "search[filter_float_price:to]": "50000"}
        
        result = build_category_url(base_url, category_path, params)
        
        assert "search%5Bfilter_float_price%3Afrom%5D=10000" in result
        assert "search%5Bfilter_float_price%3Ato%5D=50000" in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/")

    def test_build_page_url(self):
        """Тест формирования URL страницы с пагинацией."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/"
        page = 2
        
        result = build_page_url(url, page)
        
        assert "page=2" in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/")

    def test_build_page_url_with_existing_params(self):
        """Тест формирования URL страницы с пагинацией и существующими параметрами."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/?search[filter_float_price:from]=10000"
        page = 3
        
        result = build_page_url(url, page)
        
        assert "page=3" in result
        assert "search%5Bfilter_float_price%3Afrom%5D=10000" in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/")

    def test_build_page_url_override_existing_page(self):
        """Тест перезаписи существующего параметра страницы."""
        url = "https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/?page=1"
        page = 5
        
        result = build_page_url(url, page)
        
        assert "page=5" in result
        assert "page=1" not in result
        assert result.startswith("https://www.olx.ua/nedvizhimost/kvartiry-komnaty/prodazha-kvartir-komnat/")
