"""
Тесты для OlxParser (упрощенная версия).
"""
import logging
from unittest.mock import patch

import pytest
from src.domain.value_objects.ad_id import AdId
from src.infrastructure.parsers.olx.olx_parser import OlxParser


class TestOlxParserSimplified:
    """Тесты для OlxParser (упрощенная версия)."""

    @pytest.fixture
    def config(self):
        """Фикстура для конфигурации парсера."""
        return {
            'base_url': 'https://www.olx.ua',
            'requests': {
                'timeout': 30,
                'retries': 3,
                'delay': 0.1  # Уменьшаем задержку для тестов
            },
            'zyte': {
                'api_key': 'test_key',
                'api_url': 'https://api.zyte.com/v1/extract',
                'timeout': 60
            },
            'parsing': {
                'max_ads': 10,
                'max_pages': 2,
                'extract_phones': True,
                'extract_images': True,
                'max_images': 5
            },
            'auth': {
                'enabled': False
            },
            'categories': {
                'test_category': {
                    'url': '/test_category',
                    'active': True
                }
            }
        }

    @pytest.fixture
    def logger(self):
        """Фикстура для логгера."""
        return logging.getLogger('test_logger')

    @pytest.fixture
    def processed_ad_storage(self):
        """Фикстура для хранилища обработанных объявлений."""
        from unittest.mock import MagicMock
        mock_storage = MagicMock()
        mock_storage.exists.return_value = False
        return mock_storage

    @pytest.fixture
    def parser(self, config, logger, processed_ad_storage):
        """Фикстура для создания парсера."""
        parser = OlxParser(config, logger)
        parser._processed_ad_storage = processed_ad_storage
        return parser

    def test_init(self, config, logger):
        """Тест инициализации парсера."""
        parser = OlxParser(config, logger)

        assert parser._base_url == 'https://www.olx.ua'
        assert parser._timeout == 30
        assert parser._retries == 3
        assert parser._delay == 0.1
        assert parser._zyte_api_key == 'test_key'
        assert parser._max_ads == 10
        assert parser._max_pages == 2
        assert parser._extract_phones is True
        assert parser._extract_images is True
        assert parser._max_images == 5
        assert parser._auth_enabled is False
        assert 'test_category' in parser._categories

    def test_get_ad_ids_mocked(self, parser):
        """Тест получения списка ID объявлений с моками."""
        with patch.object(parser, '_get_html', return_value="<html></html>"), \
             patch.object(parser, '_extract_ad_ids', return_value=["12345678", "87654321"]):
            # Вызываем метод с ограничением количества объявлений
            result = parser.get_ad_ids('test_category', limit=1)

            # Проверяем результаты
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], AdId)
            assert result[0].value in ["12345678", "87654321"]

    def test_get_ad_ids_with_chunk_size(self, parser):
        """Тест получения списка ID объявлений с указанием размера чанка."""
        with patch.object(parser, '_get_html', return_value="<html></html>"), \
             patch.object(parser, '_extract_ad_ids', return_value=["12345678", "87654321", "11223344", "55667788"]):
            # Вызываем метод с указанием размера чанка
            result = parser.get_ad_ids('test_category', chunk_size=2)

            # Проверяем результаты
            assert isinstance(result, list)
            assert len(result) == 2  # Должно быть ограничено размером чанка
            assert isinstance(result[0], AdId)
            assert result[0].value in ["12345678", "87654321", "11223344", "55667788"]
            assert result[1].value in ["12345678", "87654321", "11223344", "55667788"]

    def test_get_ad_ids_invalid_category(self, parser):
        """Тест получения списка ID объявлений для несуществующей категории."""
        # Вызываем метод
        result = parser.get_ad_ids('invalid_category')

        # Проверяем результаты
        assert isinstance(result, list)
        assert len(result) == 0

    def test_get_ad_details_mocked(self, parser):
        """Тест получения деталей объявления с моками."""
        mock_details = {
            'id': '12345678',
            'title': '2-комнатная квартира',
            'description': 'Описание тестовой квартиры',
            'price': {'amount': 50000.0, 'currency': 'USD'},
            'rooms_count': 2,
            'total_area': 60.0,
            'floor': 5,
            'floors_count': 9,
            'building_type': 'Кирпичный',
            'contact': {'phones': ['+380991234567']},
            'images': ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
        }

        with patch.object(parser, '_get_html', return_value="<html></html>"), \
             patch.object(parser, '_extract_ad_details', return_value=mock_details):
            # Вызываем метод
            result = parser.get_ad_details(AdId("12345678"))

            # Проверяем результаты
            assert result == mock_details

    def test_get_ad_details_no_html(self, parser):
        """Тест получения деталей объявления с ошибкой получения HTML."""
        with patch.object(parser, '_get_html', return_value=None):
            # Вызываем метод
            result = parser.get_ad_details(AdId("12345678"))

            # Проверяем результаты
            assert result is None

    def test_get_ad_details_extraction_error(self, parser):
        """Тест получения деталей объявления с ошибкой извлечения данных."""
        with patch.object(parser, '_get_html', return_value="<html></html>"), \
             patch.object(parser, '_extract_ad_details', return_value=None):
            # Вызываем метод
            result = parser.get_ad_details(AdId("12345678"))

            # Проверяем результаты
            assert result is None

    def test_is_ad_too_old(self, parser):
        """Тест проверки возраста объявления."""
        from datetime import datetime, timedelta

        # Устанавливаем максимальный возраст объявления
        parser._max_ad_age_hours = 24

        # Текущая дата
        now = datetime.now()

        # Свежее объявление (12 часов назад)
        fresh_date = now - timedelta(hours=12)
        fresh_date_str = fresh_date.isoformat()

        # Старое объявление (48 часов назад)
        old_date = now - timedelta(hours=48)
        old_date_str = old_date.isoformat()

        # Проверяем свежее объявление
        with patch('src.infrastructure.parsers.olx.olx_parser.datetime') as mock_datetime:
            mock_datetime.now.return_value = now
            mock_datetime.fromisoformat.return_value = fresh_date

            result = parser._is_ad_too_old(fresh_date_str)
            assert result is False

        # Проверяем старое объявление
        with patch('src.infrastructure.parsers.olx.olx_parser.datetime') as mock_datetime:
            mock_datetime.now.return_value = now
            mock_datetime.fromisoformat.return_value = old_date

            result = parser._is_ad_too_old(old_date_str)
            assert result is True

        # Проверяем случай, когда max_ad_age_hours = 0 (нет ограничения)
        parser._max_ad_age_hours = 0
        result = parser._is_ad_too_old(old_date_str)
        assert result is False
