"""
Тесты для ValueFormatter.
"""
# from datetime import datetime - не используется в тестах
from src.infrastructure.formatters.value_formatter import ValueFormatter


class TestValueFormatter:
    """Тесты для ValueFormatter."""

    def test_format_price_with_currency(self):
        """Тест форматирования цены с валютой."""
        formatter = ValueFormatter()
        result = formatter.format_price(50000, "USD")
        assert result == "50 000 USD"

    def test_format_price_without_currency(self):
        """Тест форматирования цены без валюты."""
        formatter = ValueFormatter()
        result = formatter.format_price(50000)
        assert result == "50 000"

    def test_format_price_none(self):
        """Тест форматирования цены None."""
        formatter = ValueFormatter()
        result = formatter.format_price(None)
        assert result == "Цена не указана"

    def test_format_area_with_default_unit(self):
        """Тест форматирования площади с единицей измерения по умолчанию."""
        formatter = ValueFormatter()
        result = formatter.format_area(60.5)
        assert result == "60,5 м²"

    def test_format_area_with_custom_unit(self):
        """Тест форматирования площади с пользовательской единицей измерения."""
        formatter = ValueFormatter()
        result = formatter.format_area(6.5, "соток")
        assert result == "6,5 соток"

    def test_format_area_none(self):
        """Тест форматирования площади None."""
        formatter = ValueFormatter()
        result = formatter.format_area(None)
        assert result == "Площадь не указана"

    def test_format_date_valid_with_time(self):
        """Тест форматирования валидной даты с временем."""
        formatter = ValueFormatter()
        result = formatter.format_date("2023-05-15 10:30:00")
        assert result == "15.05.2023 10:30"

    def test_format_date_valid_with_zero_time(self):
        """Тест форматирования валидной даты с временем 00:00."""
        formatter = ValueFormatter()
        result = formatter.format_date("2023-05-15 00:00:00")
        assert result == "15.05.2023"

    def test_format_date_invalid(self):
        """Тест форматирования невалидной даты."""
        formatter = ValueFormatter()
        result = formatter.format_date("invalid-date")
        assert result == "invalid-date"

    def test_format_date_none(self):
        """Тест форматирования даты None."""
        formatter = ValueFormatter()
        result = formatter.format_date(None)
        assert result == "Дата не указана"

    def test_format_floor_info_both(self):
        """Тест форматирования информации о этаже с этажом и количеством этажей."""
        formatter = ValueFormatter()
        result = formatter.format_floor_info(5, 9)
        assert result == "5/9"

    def test_format_floor_info_floor_only(self):
        """Тест форматирования информации о этаже только с этажом."""
        formatter = ValueFormatter()
        result = formatter.format_floor_info(5, None)
        assert result == "5 / не указано"

    def test_format_floor_info_floors_count_only(self):
        """Тест форматирования информации о этаже только с количеством этажей."""
        formatter = ValueFormatter()
        result = formatter.format_floor_info(None, 9)
        assert result == "не указан / 9"

    def test_format_floor_info_none(self):
        """Тест форматирования информации о этаже None."""
        formatter = ValueFormatter()
        result = formatter.format_floor_info(None, None)
        assert result == "не указан"

    def test_format_rooms_count_valid(self):
        """Тест форматирования валидного количества комнат."""
        formatter = ValueFormatter()
        result = formatter.format_rooms_count(2)
        assert result == "2"

    def test_format_rooms_count_none(self):
        """Тест форматирования количества комнат None."""
        formatter = ValueFormatter()
        result = formatter.format_rooms_count(None)
        assert result == "Количество комнат не указано"

    def test_format_contact_info_full(self):
        """Тест форматирования полной контактной информации."""
        formatter = ValueFormatter()
        result = formatter.format_contact_info("Иван", ["+380991234567"])
        assert "Имя: Иван" in result
        assert "Телефон: +380991234567" in result

    def test_format_contact_info_name_only(self):
        """Тест форматирования контактной информации только с именем."""
        formatter = ValueFormatter()
        result = formatter.format_contact_info("Иван", None)
        assert result == "Имя: Иван"

    def test_format_contact_info_phones_only(self):
        """Тест форматирования контактной информации только с телефонами."""
        formatter = ValueFormatter()
        result = formatter.format_contact_info(None, ["+380991234567", "+380991234568"])
        assert result == "Телефон: +380991234567, +380991234568"

    def test_format_contact_info_none(self):
        """Тест форматирования контактной информации None."""
        formatter = ValueFormatter()
        result = formatter.format_contact_info(None, None)
        assert result == "Информация не указана"

    def test_format_description_short(self):
        """Тест форматирования короткого описания."""
        formatter = ValueFormatter()
        result = formatter.format_description("Короткое описание")
        assert result == "Короткое описание"

    def test_format_description_long(self):
        """Тест форматирования длинного описания."""
        formatter = ValueFormatter()
        long_description = "А" * 400
        result = formatter.format_description(long_description, max_length=300)
        assert len(result) == 300  # 297 символов + "..."
        assert result.endswith("...")

    def test_format_description_none(self):
        """Тест форматирования описания None."""
        formatter = ValueFormatter()
        result = formatter.format_description(None)
        assert result == "Описание отсутствует"
