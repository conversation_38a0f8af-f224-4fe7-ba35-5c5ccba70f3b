"""
Тесты для модели ProcessedAd.
"""
from datetime import datetime, timedelta
from src.infrastructure.storage.models.processed_ad import ProcessedAd


class TestProcessedAd:
    """Тесты для модели ProcessedAd."""

    def test_create_with_ad_id_only(self):
        """Тест создания объекта только с ID объявления."""
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id)
        
        assert processed_ad.ad_id == ad_id
        assert isinstance(processed_ad.processed_at, datetime)
        assert (datetime.utcnow() - processed_ad.processed_at).total_seconds() < 1

    def test_create_with_processed_at(self):
        """Тест создания объекта с указанием времени обработки."""
        ad_id = "12345678"
        processed_at = datetime.utcnow() - timedelta(days=1)
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at=processed_at)
        
        assert processed_ad.ad_id == ad_id
        assert processed_ad.processed_at == processed_at

    def test_to_dict(self):
        """Тест преобразования объекта в словарь."""
        ad_id = "12345678"
        processed_at = datetime.utcnow()
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at=processed_at)
        
        result = processed_ad.to_dict()
        
        assert result["ad_id"] == ad_id
        assert result["processed_at"] == processed_at.isoformat()

    def test_from_dict_valid(self):
        """Тест создания объекта из валидного словаря."""
        ad_id = "12345678"
        processed_at = datetime.utcnow()
        data = {
            "ad_id": ad_id,
            "processed_at": processed_at.isoformat()
        }
        
        processed_ad = ProcessedAd.from_dict(data)
        
        assert processed_ad.ad_id == ad_id
        assert processed_ad.processed_at == processed_at

    def test_from_dict_missing_processed_at(self):
        """Тест создания объекта из словаря без времени обработки."""
        ad_id = "12345678"
        data = {
            "ad_id": ad_id
        }
        
        processed_ad = ProcessedAd.from_dict(data)
        
        assert processed_ad.ad_id == ad_id
        assert isinstance(processed_ad.processed_at, datetime)
        assert (datetime.utcnow() - processed_ad.processed_at).total_seconds() < 1

    def test_from_dict_invalid_processed_at(self):
        """Тест создания объекта из словаря с невалидным временем обработки."""
        ad_id = "12345678"
        data = {
            "ad_id": ad_id,
            "processed_at": "invalid-date"
        }
        
        processed_ad = ProcessedAd.from_dict(data)
        
        assert processed_ad.ad_id == ad_id
        assert isinstance(processed_ad.processed_at, datetime)
        assert (datetime.utcnow() - processed_ad.processed_at).total_seconds() < 1

    def test_from_dict_missing_ad_id(self):
        """Тест создания объекта из словаря без ID объявления."""
        data = {
            "processed_at": datetime.utcnow().isoformat()
        }
        
        processed_ad = ProcessedAd.from_dict(data)
        
        assert processed_ad is None

    def test_from_dict_empty(self):
        """Тест создания объекта из пустого словаря."""
        data = {}
        
        processed_ad = ProcessedAd.from_dict(data)
        
        assert processed_ad is None
