"""
Тесты для JsonStorage.
"""
import json
import os
import tempfile
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd


class TestJsonStorage:
    """Тесты для JsonStorage."""

    @pytest.fixture
    def temp_file(self):
        """Фикстура для создания временного файла."""
        fd, path = tempfile.mkstemp()
        os.close(fd)
        yield path
        os.unlink(path)

    @pytest.fixture
    def json_storage(self, temp_file):
        """Фикстура для создания JsonStorage с временным файлом."""
        logger = MagicMock()
        storage = JsonStorage(file_path=temp_file, logger=logger)
        return storage

    def test_init_creates_file(self, temp_file):
        """Тест создания файла при инициализации."""
        # Удаляем файл, если он существует
        if os.path.exists(temp_file):
            os.unlink(temp_file)
        
        # Создаем хранилище
        JsonStorage(file_path=temp_file)
        
        # Проверяем, что файл создан
        assert os.path.exists(temp_file)
        
        # Проверяем, что файл содержит пустой список
        with open(temp_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            assert data == []

    def test_add_new_item(self, json_storage):
        """Тест добавления нового элемента."""
        processed_ad = ProcessedAd(ad_id="12345678")
        
        result = json_storage.add(processed_ad)
        
        assert result is True
        
        # Проверяем, что элемент добавлен
        data = json_storage._read_data()
        assert len(data) == 1
        assert data[0]["ad_id"] == "12345678"

    def test_add_existing_item(self, json_storage):
        """Тест добавления существующего элемента."""
        processed_ad1 = ProcessedAd(ad_id="12345678")
        processed_ad2 = ProcessedAd(ad_id="12345678")
        
        # Добавляем первый элемент
        json_storage.add(processed_ad1)
        
        # Добавляем второй элемент с тем же ID
        result = json_storage.add(processed_ad2)
        
        assert result is True
        
        # Проверяем, что в хранилище только один элемент
        data = json_storage._read_data()
        assert len(data) == 1
        assert data[0]["ad_id"] == "12345678"

    def test_add_dict(self, json_storage):
        """Тест добавления словаря."""
        processed_ad_dict = {
            "ad_id": "12345678",
            "processed_at": datetime.utcnow().isoformat()
        }
        
        result = json_storage.add(processed_ad_dict)
        
        assert result is True
        
        # Проверяем, что элемент добавлен
        data = json_storage._read_data()
        assert len(data) == 1
        assert data[0]["ad_id"] == "12345678"

    def test_add_invalid_dict(self, json_storage):
        """Тест добавления невалидного словаря."""
        processed_ad_dict = {
            "processed_at": datetime.utcnow().isoformat()
        }
        
        result = json_storage.add(processed_ad_dict)
        
        assert result is False
        
        # Проверяем, что элемент не добавлен
        data = json_storage._read_data()
        assert len(data) == 0

    def test_exists_true(self, json_storage):
        """Тест проверки существования элемента (положительный)."""
        processed_ad = ProcessedAd(ad_id="12345678")
        json_storage.add(processed_ad)
        
        result = json_storage.exists("12345678")
        
        assert result is True

    def test_exists_false(self, json_storage):
        """Тест проверки существования элемента (отрицательный)."""
        result = json_storage.exists("12345678")
        
        assert result is False

    def test_get_all(self, json_storage):
        """Тест получения всех элементов."""
        processed_ad1 = ProcessedAd(ad_id="12345678")
        processed_ad2 = ProcessedAd(ad_id="87654321")
        
        json_storage.add(processed_ad1)
        json_storage.add(processed_ad2)
        
        result = json_storage.get_all()
        
        assert len(result) == 2
        assert result[0].ad_id == "12345678"
        assert result[1].ad_id == "87654321"

    def test_get_all_empty(self, json_storage):
        """Тест получения всех элементов из пустого хранилища."""
        result = json_storage.get_all()
        
        assert result == []

    def test_remove_existing(self, json_storage):
        """Тест удаления существующего элемента."""
        processed_ad = ProcessedAd(ad_id="12345678")
        json_storage.add(processed_ad)
        
        result = json_storage.remove("12345678")
        
        assert result is True
        
        # Проверяем, что элемент удален
        data = json_storage._read_data()
        assert len(data) == 0

    def test_remove_non_existing(self, json_storage):
        """Тест удаления несуществующего элемента."""
        result = json_storage.remove("12345678")
        
        assert result is False

    def test_clear(self, json_storage):
        """Тест очистки хранилища."""
        processed_ad1 = ProcessedAd(ad_id="12345678")
        processed_ad2 = ProcessedAd(ad_id="87654321")
        
        json_storage.add(processed_ad1)
        json_storage.add(processed_ad2)
        
        result = json_storage.clear()
        
        assert result is True
        
        # Проверяем, что хранилище пусто
        data = json_storage._read_data()
        assert len(data) == 0

    def test_count(self, json_storage):
        """Тест подсчета элементов."""
        processed_ad1 = ProcessedAd(ad_id="12345678")
        processed_ad2 = ProcessedAd(ad_id="87654321")
        
        json_storage.add(processed_ad1)
        json_storage.add(processed_ad2)
        
        result = json_storage.count()
        
        assert result == 2

    def test_count_empty(self, json_storage):
        """Тест подсчета элементов в пустом хранилище."""
        result = json_storage.count()
        
        assert result == 0

    def test_read_data_error(self, json_storage):
        """Тест чтения данных с ошибкой."""
        # Записываем невалидный JSON
        with open(json_storage.file_path, 'w', encoding='utf-8') as f:
            f.write("invalid json")
        
        result = json_storage._read_data()
        
        assert result == []

    def test_write_data_error(self, json_storage):
        """Тест записи данных с ошибкой."""
        # Патчим open, чтобы он вызывал исключение
        with patch('builtins.open', side_effect=Exception("Test error")):
            result = json_storage._write_data([{"ad_id": "12345678"}])
            
            assert result is False
