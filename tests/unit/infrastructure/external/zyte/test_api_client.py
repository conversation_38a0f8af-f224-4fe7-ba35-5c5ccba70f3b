"""
Тесты для ZyteApiClient.
"""
import json
from unittest.mock import MagicMock, patch

import pytest
import requests

from src.infrastructure.external.zyte import (
    ZyteApiClient, ZyteAPIError, ZyteAuthenticationError, ZyteClientError,
    ZyteRateLimitError, ZyteServerError
)


class TestZyteApiClient:
    """Тесты для ZyteApiClient."""

    @pytest.fixture
    def client(self):
        """Фикстура для создания клиента Zyte API."""
        return ZyteApiClient(api_key="test_key")

    @pytest.fixture
    def mock_response(self):
        """Фикстура для создания мок-ответа."""
        mock = MagicMock()
        mock.status_code = 200
        mock.content = b'{"test": "data"}'
        mock.json.return_value = {"test": "data"}
        return mock

    def test_singleton_pattern(self):
        """Тест паттерна Singleton."""
        client1 = ZyteApiClient(api_key="test_key")
        client2 = ZyteApiClient(api_key="test_key")
        assert client1 is client2

        client3 = ZyteApiClient(api_key="another_key")
        assert client1 is not client3

    def test_validate_url_valid(self, client):
        """Тест валидации URL с корректным URL."""
        # Не должно вызывать исключение
        client._validate_url("https://example.com")

    def test_validate_url_invalid(self, client):
        """Тест валидации URL с некорректным URL."""
        with pytest.raises(ZyteAPIError):
            client._validate_url(None)

        with pytest.raises(ZyteAPIError):
            client._validate_url("invalid-url")

    @patch("requests.post")
    def test_get_html_success(self, mock_post, client, mock_response):
        """Тест успешного получения HTML."""
        mock_response.json.return_value = {"browserHtml": "<html>Test</html>"}
        mock_post.return_value = mock_response

        result = client.get_html("https://example.com")
        assert result == "<html>Test</html>"
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_get_html_error(self, mock_post, client):
        """Тест получения HTML с ошибкой."""
        mock_post.side_effect = requests.RequestException("Test error")

        result = client.get_html("https://example.com")
        assert result is None
        # Проверяем, что метод был вызван хотя бы один раз
        assert mock_post.call_count >= 1

    # Метод get_screenshot был удален согласно требованиям пользователя
    # @patch("requests.post")
    # def test_get_screenshot_success(self, mock_post, client, mock_response):
    #     """Тест успешного получения скриншота."""
    #     mock_response.json.return_value = {"screenshot": "dGVzdA=="}  # "test" в base64
    #     mock_post.return_value = mock_response
    #
    #     result = client.get_screenshot("https://example.com")
    #     assert result == b"test"
    #     mock_post.assert_called_once()

    @patch("requests.post")
    def test_extract_data_success(self, mock_post, client, mock_response):
        """Тест успешного извлечения данных."""
        mock_response.json.return_value = {
            "article": {"title": "Test Article"},
            "product": {"name": "Test Product"}
        }
        mock_post.return_value = mock_response

        result = client.extract_data("https://example.com")
        assert result == {
            "article": {"title": "Test Article"},
            "product": {"name": "Test Product"}
        }
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_extract_property_data_success(self, mock_post, client, mock_response):
        """Тест успешного извлечения данных о недвижимости."""
        mock_response.json.return_value = {
            "product": {
                "name": "Test Property",
                "description": "Test Description",
                "price": 100000,
                "currency": "USD",
                "images": ["image1.jpg", "image2.jpg"],
                "attributes": [
                    {"name": "rooms", "value": "3"},
                    {"name": "area", "value": "100"}
                ]
            }
        }
        mock_post.return_value = mock_response

        result = client.extract_property_data("https://example.com")
        assert result["title"] == "Test Property"
        assert result["description"] == "Test Description"
        assert result["price"]["amount"] == 100000
        assert result["price"]["currency"] == "USD"
        assert len(result["images"]) == 2
        assert result["attributes"]["rooms"] == "3"
        assert result["attributes"]["area"] == "100"
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_extract_jsonld_success(self, mock_post, client, mock_response):
        """Тест успешного извлечения JSON-LD."""
        mock_response.json.return_value = {
            "jsonLd": [
                {
                    "@context": "https://schema.org",
                    "@type": "Product",
                    "name": "Test Product"
                }
            ]
        }
        mock_post.return_value = mock_response

        result = client.extract_jsonld("https://example.com")
        assert len(result) == 1
        assert result[0]["@type"] == "Product"
        assert result[0]["name"] == "Test Product"
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_get_olx_phones_success(self, mock_post, client, mock_response):
        """Тест успешного получения телефонов OLX."""
        mock_response.json.return_value = {
            "httpResponseBody": json.dumps({
                "data": {
                    "phones": [
                        {"number": "+380123456789"},
                        {"number": "+380987654321"}
                    ]
                }
            })
        }
        mock_post.return_value = mock_response

        result = client.get_olx_phones("12345678")
        assert len(result) == 2
        assert result[0] == "+380123456789"
        assert result[1] == "+380987654321"
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_execute_javascript_actions_success(self, mock_post, client, mock_response):
        """Тест успешного выполнения JavaScript-действий."""
        mock_response.json.return_value = {
            "browserHtml": "<html>Modified</html>"
        }
        mock_post.return_value = mock_response

        actions = [
            {
                "action": "click",
                "selector": "button.submit"
            },
            {
                "action": "fill",
                "selector": "input[name=search]",
                "value": "квартира"
            }
        ]

        result = client.execute_javascript_actions("https://example.com", actions)
        assert result["browserHtml"] == "<html>Modified</html>"
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_get_olx_phones_with_click_success(self, mock_post, client, mock_response):
        """Тест успешного получения телефонов OLX через клик по кнопке."""
        # Мокаем ответ с HTML, содержащим телефоны
        mock_response.json.return_value = {
            "browserHtml": "<html><div class='css-1k7tkn3'><a href='tel:+380123456789'>+380123456789</a></div></html>"
        }
        mock_post.return_value = mock_response

        result = client.get_olx_phones_with_click("https://example.com")
        assert len(result) == 1
        assert result[0] == "+380123456789"
        mock_post.assert_called_once()

        # Проверяем, что в запросе используется правильный формат действий
        call_args = mock_post.call_args[1]['json']
        assert 'actions' in call_args
        assert len(call_args['actions']) == 2
        assert call_args['actions'][0]['action'] == 'click'
        assert call_args['actions'][0]['selector'] == "button[data-cy='ad-contact-phone']"

    def test_handle_error_response(self, client):
        """Тест обработки ошибочных ответов."""
        # Тест ошибки аутентификации
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.json.return_value = {"error": "Unauthorized"}

        with pytest.raises(ZyteAuthenticationError):
            client._handle_error_response(mock_response, "https://example.com")

        # Тест ошибки превышения лимита запросов
        mock_response.status_code = 429
        with pytest.raises(ZyteRateLimitError):
            client._handle_error_response(mock_response, "https://example.com")

        # Тест серверной ошибки
        mock_response.status_code = 500
        with pytest.raises(ZyteServerError):
            client._handle_error_response(mock_response, "https://example.com")

        # Тест клиентской ошибки
        mock_response.status_code = 400
        with pytest.raises(ZyteClientError):
            client._handle_error_response(mock_response, "https://example.com")

        # Тест общей ошибки API
        mock_response.status_code = 0
        with pytest.raises(ZyteAPIError):
            client._handle_error_response(mock_response, "https://example.com")
