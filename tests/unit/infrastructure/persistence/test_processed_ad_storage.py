"""
Тесты для DbProcessedAdStorage.
"""
from unittest.mock import MagicMock

import pytest
from src.domain.value_objects.ad_id import AdId
from src.infrastructure.persistence.processed_ad_storage import DbProcessedAdStorage
from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd


class TestDbProcessedAdStorage:
    """Тесты для DbProcessedAdStorage."""

    @pytest.fixture
    def mock_json_storage(self):
        """Фикстура для мока JsonStorage."""
        storage = MagicMock(spec=JsonStorage)
        storage.add.return_value = True
        storage.exists.return_value = False
        storage.get_all.return_value = []
        return storage

    @pytest.fixture
    def processed_ad_storage(self, mock_json_storage):
        """Фикстура для создания DbProcessedAdStorage с моком JsonStorage."""
        logger = MagicMock()
        return DbProcessedAdStorage(json_storage=mock_json_storage, logger=logger)

    def test_add_success(self, processed_ad_storage, mock_json_storage):
        """Тест успешного добавления ID."""
        ad_id = AdId("12345678")
        
        processed_ad_storage.add(ad_id)
        
        # Проверяем, что метод add был вызван с правильными аргументами
        mock_json_storage.add.assert_called_once()
        called_with = mock_json_storage.add.call_args[0][0]
        assert isinstance(called_with, ProcessedAd)
        assert called_with.ad_id == "12345678"

    def test_add_failure(self, processed_ad_storage, mock_json_storage):
        """Тест добавления ID с ошибкой."""
        mock_json_storage.add.return_value = False
        ad_id = AdId("12345678")
        
        with pytest.raises(RuntimeError):
            processed_ad_storage.add(ad_id)

    def test_exists_true(self, processed_ad_storage, mock_json_storage):
        """Тест проверки существования ID (положительный)."""
        mock_json_storage.exists.return_value = True
        ad_id = AdId("12345678")
        
        result = processed_ad_storage.exists(ad_id)
        
        assert result is True
        mock_json_storage.exists.assert_called_once_with("12345678")

    def test_exists_false(self, processed_ad_storage, mock_json_storage):
        """Тест проверки существования ID (отрицательный)."""
        mock_json_storage.exists.return_value = False
        ad_id = AdId("12345678")
        
        result = processed_ad_storage.exists(ad_id)
        
        assert result is False
        mock_json_storage.exists.assert_called_once_with("12345678")

    def test_get_all_empty(self, processed_ad_storage, mock_json_storage):
        """Тест получения всех ID из пустого хранилища."""
        mock_json_storage.get_all.return_value = []
        
        result = processed_ad_storage.get_all()
        
        assert result == []
        mock_json_storage.get_all.assert_called_once()

    def test_get_all_with_items(self, processed_ad_storage, mock_json_storage):
        """Тест получения всех ID из хранилища с элементами."""
        processed_ad1 = ProcessedAd(ad_id="12345678")
        processed_ad2 = ProcessedAd(ad_id="87654321")
        mock_json_storage.get_all.return_value = [processed_ad1, processed_ad2]
        
        result = processed_ad_storage.get_all()
        
        assert len(result) == 2
        assert isinstance(result[0], AdId)
        assert isinstance(result[1], AdId)
        assert result[0].value == "12345678"
        assert result[1].value == "87654321"
        mock_json_storage.get_all.assert_called_once()
