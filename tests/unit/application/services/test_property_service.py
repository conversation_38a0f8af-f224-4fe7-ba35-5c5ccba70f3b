"""
Тесты для сервиса PropertyService.
"""
import logging
from unittest.mock import MagicMock, patch

import pytest
from src.application.interfaces.notification_service import INotificationService
from src.application.interfaces.parser import IParser
from src.application.interfaces.processed_ad_storage import IProcessedAdStorage
from src.application.services.property import PropertyService
from src.domain.entities.property import Property
from src.domain.services.property_validator import PropertyValidator
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.price import Price


class TestPropertyService:
    """Тесты для сервиса PropertyService."""

    @pytest.fixture
    def mock_parser(self):
        """Фикстура для мока парсера."""
        parser = MagicMock(spec=IParser)
        return parser

    @pytest.fixture
    def mock_storage(self):
        """Фикстура для мока хранилища обработанных ID."""
        storage = MagicMock(spec=IProcessedAdStorage)
        return storage

    @pytest.fixture
    def mock_notification_service(self):
        """Фикстура для мока сервиса уведомлений."""
        notification_service = MagicMock(spec=INotificationService)
        return notification_service

    @pytest.fixture
    def mock_property_validator(self):
        """Фикстура для мока валидатора объектов недвижимости."""
        validator = MagicMock(spec=PropertyValidator)
        validator.validate.return_value = (True, [])
        return validator

    @pytest.fixture
    def property_service(self, mock_parser, mock_storage, mock_notification_service, mock_property_validator):
        """Фикстура для создания сервиса PropertyService с моками."""
        logger = logging.getLogger("test_logger")
        return PropertyService(
            parser=mock_parser,
            processed_ad_storage=mock_storage,
            notification_service=mock_notification_service,
            property_validator=mock_property_validator,
            logger=logger
        )

    @pytest.fixture
    def sample_property(self):
        """Фикстура для создания тестового объекта недвижимости."""
        return Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        )

    def test_process_category_success(self, property_service, mock_parser, mock_storage,
                                     mock_notification_service, mock_property_validator, sample_property):
        """Тест успешной обработки категории."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_parser.get_ad_details.return_value = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        }
        mock_storage.exists.return_value = False
        mock_notification_service.notify_new_property.return_value = True

        # Патчим метод from_dict класса Property
        with patch('src.domain.entities.property.Property.from_dict', return_value=sample_property):
            # Вызываем метод
            new_count, processed_count, error_count = property_service.process_category(
                category="квартиры",
                chat_ids=["123456789"]
            )

        # Проверяем результаты
        assert new_count == 1
        assert processed_count == 1
        assert error_count == 0

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, None)
        mock_parser.get_ad_details.assert_called_once_with(ad_id)
        mock_storage.exists.assert_called_once_with(ad_id)
        mock_property_validator.validate.assert_called_once_with(sample_property)
        mock_notification_service.notify_new_property.assert_called_once_with(sample_property, ["123456789"], "квартиры")
        mock_storage.add.assert_called_once_with(ad_id)

    def test_process_category_already_processed(self, property_service, mock_parser, mock_storage,
                                              mock_notification_service):
        """Тест обработки категории с уже обработанным объявлением."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_storage.exists.return_value = True  # ID уже обработан

        # Вызываем метод
        new_count, processed_count, error_count = property_service.process_category(
            category="квартиры",
            chat_ids=["123456789"]
        )

        # Проверяем результаты
        assert new_count == 0
        assert processed_count == 1
        assert error_count == 0

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, None)
        mock_storage.exists.assert_called_once_with(ad_id)

        # Проверяем, что эти методы не вызывались
        mock_parser.get_ad_details.assert_not_called()
        mock_notification_service.notify_new_property.assert_not_called()
        mock_storage.add.assert_not_called()

    def test_process_category_parser_error(self, property_service, mock_parser, mock_notification_service):
        """Тест обработки категории с ошибкой парсера."""
        # Настраиваем моки
        mock_parser.get_ad_ids.side_effect = Exception("Ошибка парсера")

        # Вызываем метод
        new_count, processed_count, error_count = property_service.process_category(
            category="квартиры",
            chat_ids=["123456789"]
        )

        # Проверяем результаты
        assert new_count == 0
        assert processed_count == 0
        assert error_count == 1

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, None)
        mock_notification_service.notify_error.assert_called_once()
        assert "Ошибка парсера" in mock_notification_service.notify_error.call_args[0][0]

    def test_process_category_ad_details_error(self, property_service, mock_parser, mock_storage,
                                             mock_notification_service):
        """Тест обработки категории с ошибкой получения деталей объявления."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_storage.exists.return_value = False
        mock_parser.get_ad_details.side_effect = Exception("Ошибка получения деталей")

        # Вызываем метод
        new_count, processed_count, error_count = property_service.process_category(
            category="квартиры",
            chat_ids=["123456789"]
        )

        # Проверяем результаты
        assert new_count == 0
        assert processed_count == 1
        assert error_count == 1

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, None)
        mock_storage.exists.assert_called_once_with(ad_id)
        mock_parser.get_ad_details.assert_called_once_with(ad_id)
        mock_notification_service.notify_new_property.assert_not_called()
        mock_storage.add.assert_not_called()

    def test_process_category_notification_error(self, property_service, mock_parser, mock_storage,
                                               mock_notification_service, mock_property_validator, sample_property):
        """Тест обработки категории с ошибкой отправки уведомления."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_parser.get_ad_details.return_value = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        }
        mock_storage.exists.return_value = False
        mock_notification_service.notify_new_property.return_value = False  # Ошибка отправки уведомления

        # Патчим метод from_dict класса Property
        with patch('src.domain.entities.property.Property.from_dict', return_value=sample_property):
            # Вызываем метод
            new_count, processed_count, error_count = property_service.process_category(
                category="квартиры",
                chat_ids=["123456789"]
            )

        # Проверяем результаты
        assert new_count == 0
        assert processed_count == 1
        assert error_count == 1

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, None)
        mock_parser.get_ad_details.assert_called_once_with(ad_id)
        mock_storage.exists.assert_called_once_with(ad_id)
        mock_property_validator.validate.assert_called_once_with(sample_property)
        mock_notification_service.notify_new_property.assert_called_once_with(sample_property, ["123456789"], "квартиры")
        mock_storage.add.assert_called_once_with(ad_id)  # ID все равно добавляется в хранилище

    def test_process_category_with_limit(self, property_service, mock_parser, mock_storage,
                                        mock_notification_service, mock_property_validator, sample_property):
        """Тест обработки категории с ограничением количества объявлений."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_parser.get_ad_details.return_value = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        }
        mock_storage.exists.return_value = False
        mock_notification_service.notify_new_property.return_value = True
        mock_property_validator.validate.return_value = (True, [])

        # Патчим метод from_dict класса Property
        with patch('src.domain.entities.property.Property.from_dict', return_value=sample_property):
            # Вызываем метод с ограничением количества объявлений
            new_count, processed_count, error_count = property_service.process_category(
                category="квартиры",
                chat_ids=["123456789"],
                limit=10
            )

            # Проверяем результаты
            assert new_count == 1
            assert processed_count == 1
            assert error_count == 0

            # Проверяем, что методы моков были вызваны с правильными аргументами
            mock_parser.get_ad_ids.assert_called_once_with("квартиры", 10, None)
            mock_parser.get_ad_details.assert_called_once_with(ad_id)
            mock_storage.exists.assert_called_once_with(ad_id)
            mock_property_validator.validate.assert_called_once_with(sample_property)
            mock_notification_service.notify_new_property.assert_called_once_with(sample_property, ["123456789"], "квартиры")
            mock_storage.add.assert_called_once_with(ad_id)

    def test_process_category_with_chunk_size(self, property_service, mock_parser, mock_storage,
                                           mock_notification_service, mock_property_validator, sample_property):
        """Тест обработки категории с указанием размера чанка."""
        # Настраиваем моки
        ad_id = AdId("12345678")
        mock_parser.get_ad_ids.return_value = [ad_id]
        mock_parser.get_ad_details.return_value = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        }
        mock_storage.exists.return_value = False
        mock_notification_service.notify_new_property.return_value = True
        mock_property_validator.validate.return_value = (True, [])

        # Патчим метод from_dict класса Property
        with patch('src.domain.entities.property.Property.from_dict', return_value=sample_property):
            # Вызываем метод с указанием размера чанка
            new_count, processed_count, error_count = property_service.process_category(
                category="квартиры",
                chat_ids=["123456789"],
                chunk_size=5
            )

            # Проверяем результаты
            assert new_count == 1
            assert processed_count == 1
            assert error_count == 0

            # Проверяем, что методы моков были вызваны с правильными аргументами
            mock_parser.get_ad_ids.assert_called_once_with("квартиры", None, 5)
            mock_parser.get_ad_details.assert_called_once_with(ad_id)
            mock_storage.exists.assert_called_once_with(ad_id)
            mock_property_validator.validate.assert_called_once_with(sample_property)
            mock_notification_service.notify_new_property.assert_called_once_with(sample_property, ["123456789"], "квартиры")
            mock_storage.add.assert_called_once_with(ad_id)

    def test_get_property_by_id_success(self, property_service, mock_parser, sample_property):
        """Тест успешного получения объекта недвижимости по ID."""
        # Настраиваем моки
        ad_id = "12345678"
        mock_parser.get_ad_details.return_value = {
            "id": "12345678",
            "title": "Тестовая квартира",
            "description": "Описание тестовой квартиры",
            "price": {"amount": 50000, "currency": "USD"},
            "address": {"city": "Киев", "district": "Печерский"},
            "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        }

        # Патчим метод from_dict класса Property
        with patch('src.domain.entities.property.Property.from_dict', return_value=sample_property):
            # Вызываем метод
            result = property_service.get_property_by_id(ad_id)

        # Проверяем результаты
        assert result == sample_property

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_details.assert_called_once()
        assert mock_parser.get_ad_details.call_args[0][0].value == ad_id

    def test_get_property_by_id_not_found(self, property_service, mock_parser):
        """Тест получения объекта недвижимости по ID, когда объявление не найдено."""
        # Настраиваем моки
        ad_id = "12345678"
        mock_parser.get_ad_details.return_value = None

        # Вызываем метод
        result = property_service.get_property_by_id(ad_id)

        # Проверяем результаты
        assert result is None

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_details.assert_called_once()
        assert mock_parser.get_ad_details.call_args[0][0].value == ad_id

    def test_get_property_by_id_error(self, property_service, mock_parser):
        """Тест получения объекта недвижимости по ID с ошибкой."""
        # Настраиваем моки
        ad_id = "12345678"
        mock_parser.get_ad_details.side_effect = Exception("Ошибка получения деталей")

        # Вызываем метод
        result = property_service.get_property_by_id(ad_id)

        # Проверяем результаты
        assert result is None

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_parser.get_ad_details.assert_called_once()
        assert mock_parser.get_ad_details.call_args[0][0].value == ad_id
