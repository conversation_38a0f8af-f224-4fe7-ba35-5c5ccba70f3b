"""
Тесты для сервиса NotificationService.
"""
from unittest.mock import MagicMock

import pytest
import asyncio
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

from src.application.interfaces.notification_sender import INotificationSender
from src.application.services.notification import NotificationService
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price


class TestNotificationService:
    """Тесты для сервиса NotificationService."""

    @pytest.fixture
    def mock_notification_sender(self):
        """Фикстура для мока отправителя уведомлений."""
        sender = MagicMock(spec=INotificationSender)
        sender.send.return_value = True
        sender.send_with_image.return_value = True
        sender.send_with_images.return_value = True
        return sender

    @pytest.fixture
    def notification_service(self, mock_notification_sender):
        """Фикстура для создания сервиса NotificationService с моком."""
        return NotificationService(notification_sender=mock_notification_sender)

    @pytest.fixture
    def sample_property(self):
        """Фикстура для создания тестового объекта недвижимости."""
        return Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=60.5, living=40.0, kitchen=10.0),
            rooms=2,
            floor=5,
            total_floors=9,
            property_type="квартира",
            contact=Contact(name="Иван", phones=["+380991234567"]),
            images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        )

    @pytest.mark.asyncio
    async def test_notify_new_property_with_images(self, notification_service, mock_notification_sender, sample_property):
        """Тест отправки уведомления о новом объекте недвижимости с изображениями."""
        # Вызываем метод
        result = await notification_service.notify_new_property(sample_property, ["123456789"], "apartments_sale")

        # Проверяем результаты
        assert result is True

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_notification_sender.send_with_images.assert_called_once()
        assert mock_notification_sender.send_with_images.call_args[1]["chat_id"] == "123456789"
        assert mock_notification_sender.send_with_images.call_args[1]["image_urls"] == sample_property.images[:5]
        assert mock_notification_sender.send_with_images.call_args[1]["parse_mode"] == "HTML"

        # Проверяем, что кнопка "Открыть объявление" присутствует
        reply_markup = mock_notification_sender.send_with_images.call_args[1]["reply_markup"]
        assert isinstance(reply_markup, InlineKeyboardMarkup)
        assert len(reply_markup.inline_keyboard) == 1
        assert len(reply_markup.inline_keyboard[0]) == 1
        assert reply_markup.inline_keyboard[0][0].text == "Открыть объявление"
        assert reply_markup.inline_keyboard[0][0].url == sample_property.url

        # Проверяем, что сообщение содержит нужную информацию
        message = mock_notification_sender.send_with_images.call_args[1]["message"]
        assert sample_property.title in message
        assert sample_property.price.currency in message
        assert str(sample_property.area.total) in message
        assert str(sample_property.rooms) in message
        assert str(sample_property.floor) in message
        assert str(sample_property.total_floors) in message
        assert sample_property.url in message

    @pytest.mark.asyncio
    async def test_notify_new_property_without_images(self, notification_service, mock_notification_sender, sample_property):
        """Тест отправки уведомления о новом объекте недвижимости без изображений."""
        # Убираем изображения
        sample_property.images = None

        # Вызываем метод
        result = await notification_service.notify_new_property(sample_property, ["123456789"], "apartments_sale")

        # Проверяем результаты
        assert result is True

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_notification_sender.send.assert_called_once()
        assert mock_notification_sender.send.call_args[1]["chat_id"] == "123456789"
        assert mock_notification_sender.send.call_args[1]["parse_mode"] == "HTML"
        assert mock_notification_sender.send.call_args[1]["disable_web_page_preview"] is False

        # Проверяем, что кнопка "Открыть объявление" присутствует
        reply_markup = mock_notification_sender.send.call_args[1]["reply_markup"]
        assert isinstance(reply_markup, InlineKeyboardMarkup)
        assert len(reply_markup.inline_keyboard) == 1
        assert len(reply_markup.inline_keyboard[0]) == 1
        assert reply_markup.inline_keyboard[0][0].text == "Открыть объявление"
        assert reply_markup.inline_keyboard[0][0].url == sample_property.url

        # Проверяем, что сообщение содержит нужную информацию
        message = mock_notification_sender.send.call_args[1]["message"]
        assert sample_property.title in message
        assert sample_property.price.currency in message
        assert str(sample_property.area.total) in message
        assert str(sample_property.rooms) in message
        assert str(sample_property.floor) in message
        assert str(sample_property.total_floors) in message
        assert sample_property.url in message

    @pytest.mark.asyncio
    async def test_notify_new_property_multiple_chats(self, notification_service, mock_notification_sender, sample_property):
        """Тест отправки уведомления о новом объекте недвижимости в несколько чатов."""
        # Вызываем метод
        result = await notification_service.notify_new_property(sample_property, ["123456789", "987654321"], "apartments_sale")

        # Проверяем результаты
        assert result is True

        # Проверяем, что методы моков были вызваны с правильными аргументами
        assert mock_notification_sender.send_with_images.call_count == 2

        # Проверяем первый вызов
        assert mock_notification_sender.send_with_images.call_args_list[0][1]["chat_id"] == "123456789"

        # Проверяем второй вызов
        assert mock_notification_sender.send_with_images.call_args_list[1][1]["chat_id"] == "987654321"

    @pytest.mark.asyncio
    async def test_notify_new_property_failure(self, notification_service, mock_notification_sender, sample_property):
        """Тест отправки уведомления о новом объекте недвижимости с ошибкой."""
        # Настраиваем мок на возврат ошибки
        mock_notification_sender.send_with_images.return_value = False

        # Вызываем метод
        result = await notification_service.notify_new_property(sample_property, ["123456789"], "apartments_sale")

        # Проверяем результаты
        assert result is False

    @pytest.mark.asyncio
    async def test_notify_error(self, notification_service, mock_notification_sender):
        """Тест отправки уведомления об ошибке."""
        # Вызываем метод
        result = await notification_service.notify_error("Тестовая ошибка", ["123456789"])

        # Проверяем результаты
        assert result is True

        # Проверяем, что методы моков были вызваны с правильными аргументами
        mock_notification_sender.send.assert_called_once()
        assert mock_notification_sender.send.call_args[1]["chat_id"] == "123456789"
        assert mock_notification_sender.send.call_args[1]["parse_mode"] == "HTML"

        # Проверяем, что сообщение содержит нужную информацию
        message = mock_notification_sender.send.call_args[1]["message"]
        assert "Ошибка" in message
        assert "Тестовая ошибка" in message

    @pytest.mark.asyncio
    async def test_notify_error_multiple_chats(self, notification_service, mock_notification_sender):
        """Тест отправки уведомления об ошибке в несколько чатов."""
        # Вызываем метод
        result = await notification_service.notify_error("Тестовая ошибка", ["123456789", "987654321"])

        # Проверяем результаты
        assert result is True

        # Проверяем, что методы моков были вызваны с правильными аргументами
        assert mock_notification_sender.send.call_count == 2

        # Проверяем первый вызов
        assert mock_notification_sender.send.call_args_list[0][1]["chat_id"] == "123456789"

        # Проверяем второй вызов
        assert mock_notification_sender.send.call_args_list[1][1]["chat_id"] == "987654321"

    @pytest.mark.asyncio
    async def test_notify_error_failure(self, notification_service, mock_notification_sender):
        """Тест отправки уведомления об ошибке с ошибкой."""
        # Настраиваем мок на возврат ошибки
        mock_notification_sender.send.return_value = False

        # Вызываем метод
        result = await notification_service.notify_error("Тестовая ошибка", ["123456789"])

        # Проверяем результаты
        assert result is False
