"""
Тестовый скрипт для проверки работы локализованных уведомлений.
"""
import os
import sys
import asyncio
import logging
from datetime import datetime

# Добавляем корневую директорию проекта в путь для импорта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price
from src.infrastructure.external.telegram.notification_sender import TelegramNotificationSender
from src.presentation.telegram.localization.factory import create_localization_services
from src.presentation.telegram.localization.notification_factory import create_localized_notification_service


async def test_localized_notifications():
    """
    Тестирует работу локализованных уведомлений.
    """
    # Настраиваем логгер
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Получаем токен Telegram бота из переменной окружения
    telegram_token = os.environ.get('TELEGRAM_BOT_TOKEN')
    if not telegram_token:
        logger.error("Не указан токен Telegram бота в переменной окружения TELEGRAM_BOT_TOKEN")
        return

    # Получаем ID чата для тестирования из переменной окружения
    test_chat_id = os.environ.get('TEST_CHAT_ID')
    if not test_chat_id:
        logger.error("Не указан ID чата для тестирования в переменной окружения TEST_CHAT_ID")
        return

    try:
        test_chat_id = int(test_chat_id)
    except ValueError:
        logger.error(f"Некорректный ID чата для тестирования: {test_chat_id}")
        return

    # Создаем сервисы локализации
    localization_service, user_language_service = create_localization_services()

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=telegram_token, logger=logger)

    # Создаем локализованный сервис уведомлений
    notification_service = create_localized_notification_service(
        notification_sender=notification_sender,
        localization_service=localization_service,
        user_language_service=user_language_service,
        logger=logger
    )

    # Создаем тестовый объект недвижимости
    property_obj = Property(
        ad_id=AdId(value="12345"),
        title="Тестовое объявление",
        description="Описание тестового объявления",
        url="https://www.olx.ua/d/obyavlenie/test-IDxyz.html",
        price=Price(amount=50000, currency="USD"),
        address=Address(
            city="Киев",
            district="Печерский",
            street="ул. Тестовая",
            house_number="1"
        ),
        area=Area(
            total=100,
            living=80,
            kitchen=15,
            land=None
        ),
        rooms=3,
        floor=5,
        total_floors=9,
        building_type="Кирпичный",
        contact=Contact(
            name="Тестовый Пользователь",
            phones=["+380123456789"]
        ),
        images=[
            "https://via.placeholder.com/800x600.png?text=Test+Image+1",
            "https://via.placeholder.com/800x600.png?text=Test+Image+2"
        ],
        published_at=datetime.now()
    )

    # Устанавливаем язык пользователя
    user_language_service.set_language(test_chat_id, 'ru')

    # Отправляем уведомление о новом объявлении на русском языке
    logger.info("Отправка уведомления о новом объявлении на русском языке")
    await notification_service.notify_new_property(property_obj, [test_chat_id], "apartments_sale")

    # Ждем 2 секунды
    await asyncio.sleep(2)



    # Отправляем уведомление о статусе парсинга на русском языке
    logger.info("Отправка уведомления о статусе парсинга на русском языке")
    await notification_service.notify_parsing_status(
        category="apartments_sale",
        new_count=10,
        processed_count=100,
        error_count=2,
        execution_time="00:05:30",
        chat_ids=[test_chat_id]
    )

    # Ждем 2 секунды
    await asyncio.sleep(2)

    # Устанавливаем язык пользователя на украинский
    user_language_service.set_language(test_chat_id, 'ua')

    # Отправляем уведомление о новом объявлении на украинском языке
    logger.info("Отправка уведомления о новом объявлении на украинском языке")
    await notification_service.notify_new_property(property_obj, [test_chat_id], "apartments_sale")

    # Ждем 2 секунды
    await asyncio.sleep(2)



    # Отправляем уведомление о статусе парсинга на украинском языке
    logger.info("Отправка уведомления о статусе парсинга на украинском языке")
    await notification_service.notify_parsing_status(
        category="apartments_sale",
        new_count=10,
        processed_count=100,
        error_count=2,
        execution_time="00:05:30",
        chat_ids=[test_chat_id]
    )

    # Возвращаем язык пользователя на русский
    user_language_service.set_language(test_chat_id, 'ru')


if __name__ == "__main__":
    asyncio.run(test_localized_notifications())
