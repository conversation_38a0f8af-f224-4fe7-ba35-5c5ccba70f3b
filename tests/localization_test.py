"""
Тестовый скрипт для проверки работы локализации.
"""
import os
import sys

# Добавляем корневую директорию проекта в путь для импорта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.presentation.telegram.localization.factory import create_localization_services


def test_localization():
    """
    Тестирует работу локализации.
    """
    # Создаем сервисы локализации
    localization_service, user_language_service = create_localization_services()
    
    # Получаем список доступных языков
    languages = user_language_service.get_available_languages()
    print(f"Доступные языки: {', '.join(str(lang) for lang in languages)}")
    
    # Тестируем получение текстов на разных языках
    test_keys = [
        'main_menu_status',
        'main_menu_notifications',
        'main_menu_categories',
        'main_menu_help',
        'help_menu_title',
        'categories_menu_title',
        'language_menu_title',
        'status_running',
        'notifications_stopped'
    ]
    
    for language in ['ru', 'ua']:
        print(f"\nТексты на языке {language}:")
        for key in test_keys:
            text = localization_service.get_text(key, language)
            print(f"  {key}: {text}")
    
    # Тестируем получение шаблонов на разных языках
    test_templates = [
        'command_start',
        'command_help',
        'command_status',
        'command_category_added',
        'command_category_removed'
    ]
    
    for language in ['ru', 'ua']:
        print(f"\nШаблоны на языке {language}:")
        for key in test_templates:
            template = localization_service.get_template(key, language)
            print(f"  {key}: {template[:50]}...")
    
    # Тестируем форматирование шаблонов
    print("\nФорматирование шаблонов:")
    
    # Шаблон command_start
    template = localization_service.get_template('command_start', 'ru')
    formatted = localization_service.format_template(template, {'name': 'Иван'})
    print(f"command_start (ru):\n{formatted}")
    
    template = localization_service.get_template('command_start', 'ua')
    formatted = localization_service.format_template(template, {'name': 'Іван'})
    print(f"command_start (ua):\n{formatted}")
    
    # Шаблон command_status
    template = localization_service.get_template('command_status', 'ru')
    formatted = localization_service.format_template(template, {
        'status': localization_service.get_text('status_running', 'ru'),
        'categories_info': "Активные категории:\n- 🏢 Продажа квартир\n- 🏠 Аренда домов"
    })
    print(f"command_status (ru):\n{formatted}")
    
    template = localization_service.get_template('command_status', 'ua')
    formatted = localization_service.format_template(template, {
        'status': localization_service.get_text('status_running', 'ua'),
        'categories_info': "Активні категорії:\n- 🏢 Продаж квартир\n- 🏠 Оренда будинків"
    })
    print(f"command_status (ua):\n{formatted}")
    
    # Тестируем управление языком пользователя
    print("\nУправление языком пользователя:")
    
    # Создаем тестового пользователя
    user_id = 123456789
    
    # Получаем язык пользователя по умолчанию
    language = user_language_service.get_language(user_id)
    print(f"Язык пользователя по умолчанию: {language}")
    
    # Устанавливаем язык пользователя
    user_language_service.set_language(user_id, 'ua')
    language = user_language_service.get_language(user_id)
    print(f"Язык пользователя после установки: {language}")
    
    # Пробуем установить несуществующий язык
    result = user_language_service.set_language(user_id, 'en')
    print(f"Результат установки несуществующего языка: {result}")
    language = user_language_service.get_language(user_id)
    print(f"Язык пользователя после попытки установки несуществующего языка: {language}")
    
    # Возвращаем язык пользователя на русский
    user_language_service.set_language(user_id, 'ru')
    language = user_language_service.get_language(user_id)
    print(f"Язык пользователя после возврата на русский: {language}")


if __name__ == "__main__":
    test_localization()
