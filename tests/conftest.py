"""
Общие фикстуры для тестов.
"""
import os
import sys
import pytest
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.insert(0, os.path.abspath('.'))

# Путь к директории с фикстурами
FIXTURES_DIR = Path(__file__).parent / "fixtures"
HTML_FIXTURES_DIR = FIXTURES_DIR / "html"

@pytest.fixture
def html_fixtures_dir():
    """Возвращает путь к директории с HTML фикстурами."""
    return HTML_FIXTURES_DIR

@pytest.fixture
def sample_property_dict():
    """Возвращает словарь с данными объекта недвижимости для тестов."""
    return {
        "id": "12345678",
        "title": "Тестовая квартира",
        "description": "Описание тестовой квартиры",
        "price": {
            "amount": 50000,
            "currency": "USD"
        },
        "location": "Киев, Печерский район",
        "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        "total_area": 60.5,
        "kitchen_area": 12.0,
        "rooms_count": 2,
        "floor": 5,
        "floors_count": 9,
        "building_type": "Кирпичный",
        "contact": {
            "name": "Иван",
            "phones": ["+380991234567"]
        },
        "published_at": "2023-05-15 10:30:00",
        "images": [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        ]
    }
