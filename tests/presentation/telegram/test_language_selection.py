"""
Тесты для проверки выбора языка при первом запуске бота.
"""
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

from telegram import Update, User, Message

from src.presentation.telegram.bot import PropertyBot


class TestLanguageSelection(unittest.TestCase):
    """
    Тесты для проверки выбора языка при первом запуске бота.
    """

    def setUp(self):
        """
        Настройка тестов.
        """
        # Создаем моки для сервисов
        self.property_service = MagicMock()
        self.localization_service = MagicMock()
        self.user_language_service = MagicMock()
        
        # Настраиваем мок для user_language_service
        self.user_language_service.get_language.return_value = 'ru'
        self.user_language_service._default_language = 'ru'
        self.user_language_service._user_language_storage = MagicMock()
        self.user_language_service._user_language_storage._languages = {}
        
        # Настраиваем мок для localization_service
        self.localization_service.get_text.return_value = 'Тестовый текст'
        self.localization_service.get_template.return_value = 'Привет, {name}!'
        self.localization_service.format_template.return_value = 'Привет, Тест!'
        
        # Создаем бота
        self.bot = PropertyBot(
            token='test_token',
            property_service=self.property_service,
            allowed_users=[123456],
            admin_users=[123456],
            localization_service=self.localization_service,
            user_language_service=self.user_language_service
        )
        
        # Патчим методы бота
        self.bot._is_user_allowed = AsyncMock(return_value=True)
        PropertyBot.safe_reply_text = AsyncMock()
        PropertyBot.safe_send_message = AsyncMock()
        PropertyBot.safe_answer = AsyncMock()
        PropertyBot.safe_edit_message_text_from_query = AsyncMock()
        
        # Создаем объект Update
        self.update = MagicMock(spec=Update)
        self.update.message = MagicMock(spec=Message)
        self.update.message.from_user = MagicMock(spec=User)
        self.update.message.from_user.id = 123456
        self.update.message.from_user.first_name = 'Тест'
        self.update.message.from_user.language_code = 'ru'
        
        # Создаем контекст
        self.context = MagicMock()

    @patch('src.presentation.telegram.localization.menu.get_localized_language_menu')
    async def test_first_launch_language_selection(self, mock_get_localized_language_menu):
        """
        Тест выбора языка при первом запуске бота.
        """
        # Настраиваем мок для get_localized_language_menu
        mock_get_localized_language_menu.return_value = 'language_menu'
        
        # Настраиваем мок для localization_service
        self.localization_service.get_text.side_effect = lambda key, lang: {
            'welcome_message': 'Привет! Добро пожаловать в бот!' if lang == 'ru' else 'Привіт! Вітаємо у боті!'
        }.get(key, 'Тестовый текст')
        
        # Вызываем метод _handle_start
        await self.bot._handle_start(self.update, self.context)
        
        # Проверяем, что был вызван метод safe_reply_text с правильными параметрами
        PropertyBot.safe_reply_text.assert_called_once()
        call_args = PropertyBot.safe_reply_text.call_args[0]
        self.assertEqual(call_args[0], self.update.message)
        self.assertIn('Привет! Добро пожаловать в бот!', call_args[1])
        self.assertIn('Привіт! Вітаємо у боті!', call_args[1])
        self.assertEqual(call_args[2], 'language_menu')
        
        # Проверяем, что был вызван метод get_localized_language_menu с правильными параметрами
        mock_get_localized_language_menu.assert_called_once_with(
            self.localization_service, 
            self.user_language_service, 
            self.update.message.from_user.id
        )

    @patch('src.presentation.telegram.localization.menu.get_localized_main_menu')
    async def test_language_callback_first_launch(self, mock_get_localized_main_menu):
        """
        Тест обработки callback-запроса для выбора языка при первом запуске.
        """
        # Настраиваем мок для get_localized_main_menu
        mock_get_localized_main_menu.return_value = 'main_menu'
        
        # Создаем объект Update с callback_query
        update = MagicMock(spec=Update)
        update.callback_query = MagicMock()
        update.callback_query.data = 'language_ua'
        update.callback_query.from_user = MagicMock(spec=User)
        update.callback_query.from_user.id = 123456
        update.callback_query.from_user.first_name = 'Тест'
        update.callback_query.message = MagicMock()
        update.callback_query.message.chat_id = 123456
        
        # Настраиваем мок для user_language_service
        self.user_language_service.set_language.return_value = True
        self.user_language_service.get_available_languages.return_value = [
            MagicMock(code='ru', name='Русский'),
            MagicMock(code='ua', name='Українська')
        ]
        
        # Настраиваем мок для localization_service
        self.localization_service.get_text.side_effect = lambda key, lang: {
            'language_changed': 'Мову змінено на {language}' if lang == 'ua' else 'Язык изменен на {language}',
            'main_menu_title': 'Головне меню:' if lang == 'ua' else 'Главное меню:'
        }.get(key, 'Тестовый текст')
        
        self.localization_service.get_template.side_effect = lambda key, lang: {
            'command_start': 'Привіт, {name}!' if lang == 'ua' else 'Привет, {name}!'
        }.get(key, 'Тестовый шаблон')
        
        self.localization_service.format_template.side_effect = lambda template, params: template.replace('{name}', params['name'])
        
        # Вызываем метод _handle_callback
        await self.bot._handle_callback(update, self.context)
        
        # Проверяем, что был вызван метод set_language с правильными параметрами
        self.user_language_service.set_language.assert_called_once_with(123456, 'ua')
        
        # Проверяем, что был вызван метод safe_answer с правильными параметрами
        PropertyBot.safe_answer.assert_called_once()
        
        # Проверяем, что был вызван метод safe_edit_message_text_from_query с правильными параметрами
        PropertyBot.safe_edit_message_text_from_query.assert_called_once()
        
        # Проверяем, что был вызван метод safe_send_message с правильными параметрами
        PropertyBot.safe_send_message.assert_called_once()
        call_args = PropertyBot.safe_send_message.call_args[0]
        self.assertEqual(call_args[1], 123456)
        self.assertIn('Привіт, Тест!', call_args[2])
        self.assertEqual(call_args[3], 'main_menu')


if __name__ == '__main__':
    unittest.main()
