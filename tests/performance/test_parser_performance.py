"""
Тесты производительности для парсера OLX.
"""
from unittest.mock import MagicMock, patch

import pytest

from src.infrastructure.parsers.olx.olx_parser import OlxParser


class TestParserPerformance:
    """Тесты производительности для парсера OLX."""

    @pytest.fixture
    def config(self):
        """Фикстура для создания конфигурации парсера."""
        return {
            'base_url': 'https://www.olx.ua',
            'requests': {
                'timeout': 30,
                'retries': 3,
                'delay': 2,
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            'zyte': {
                'api_key': 'test_key',
                'api_url': 'https://api.zyte.com/v1/extract',
                'timeout': 60
            }
        }

    @pytest.fixture
    def parser(self, config):
        """Фикстура для создания парсера."""
        # Патчим конструктор, чтобы избежать проверки Zyte API ключа
        with patch.object(OlxParser, '__init__', return_value=None):
            parser = OlxParser.__new__(OlxParser)
            parser._base_url = 'https://www.olx.ua'
            parser._timeout = 30
            parser._retries = 3
            parser._delay = 2
            parser._user_agent = 'Mozilla/5.0'
            parser._zyte_api_key = 'test_key'
            parser._zyte_api_url = 'https://api.zyte.com/v1/extract'
            parser._zyte_timeout = 60
            parser._max_ads = 10
            parser._max_ads_per_category = 5
            parser._max_pages = 1
            parser._chunk_size = 3
            parser._max_ad_age_hours = 72
            parser._extract_phones = False
            parser._extract_images = True
            parser._max_images = 5
            parser._config = config
            parser._logger = MagicMock()

            # Создаем мок для хранилища обработанных объявлений
            parser._processed_ad_storage = MagicMock()
            parser._processed_ad_storage.exists.return_value = False
            parser._processed_ad_storage.add.return_value = True

            return parser

    @pytest.fixture
    def sample_html(self):
        """Фикстура для создания тестового HTML."""
        return """
        <html>
            <head>
                <meta property="og:title" content="Тестовая квартира">
                <meta property="og:description" content="Описание тестовой квартиры">
            </head>
            <body>
                <h1 class="css-1soizd2">Тестовая квартира</h1>
                <div data-cy="ad_description">
                    <div>Описание тестовой квартиры</div>
                </div>
                <div class="css-1epmoz1">
                    <p>Цена: 50 000 USD</p>
                    <p>Город: Киев</p>
                    <p>Район: Печерский</p>
                </div>
                <a href="/nedvizhimost/kvartiry/prodazha/">
                    <p>Киев, Печерский</p>
                </a>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "id": "12345678",
                            "map": {
                                "lat": 50.4501,
                                "lon": 30.5234
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """

    def test_get_ad_details_performance(self, benchmark, parser, sample_html):
        """Тест производительности получения деталей объявления."""
        # Arrange
        from src.domain.value_objects.ad_id import AdId
        ad_id = AdId("12345678")
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"

        # Патчим метод _get_html, чтобы он возвращал наш тестовый HTML
        with patch.object(parser, '_get_html', return_value=sample_html):
            # Патчим метод _extract_ad_details, чтобы он возвращал тестовые данные
            with patch.object(parser, '_extract_ad_details', return_value={
                "id": "12345678",
                "title": "Тестовая квартира",
                "description": "Описание тестовой квартиры",
                "price": {"amount": 50000, "currency": "USD"},
                "address": {"city": "Киев", "district": "Печерский"},
                "url": url
            }):
                # Act & Assert
                result = benchmark(parser.get_ad_details, ad_id)

                assert result is not None
                assert result["id"] == "12345678"
                assert result["title"] == "Тестовая квартира"

    def test_parse_html_performance(self, benchmark, sample_html):
        """Тест производительности парсинга HTML."""
        # Arrange
        from bs4 import BeautifulSoup

        # Act & Assert
        result = benchmark(BeautifulSoup, sample_html, 'html.parser')

        assert result is not None

    def test_extract_property_info_performance(self, benchmark, sample_html):
        """Тест производительности извлечения информации о недвижимости."""
        # Arrange
        from bs4 import BeautifulSoup
        from src.infrastructure.parsers.olx.utils.html_utils import get_title

        soup = BeautifulSoup(sample_html, 'html.parser')

        # Act & Assert
        result = benchmark(get_title, soup)

        assert result is not None
