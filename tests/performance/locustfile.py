"""
Сценарий нагрузочного тестирования с использованием Locust.
"""
import json
import os
import sys

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from locust import HttpUser, task, between
from locust.exception import StopUser

from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.price import Price


class OlxUser(HttpUser):
    """Пользователь для нагрузочного тестирования OLX."""

    wait_time = between(1, 3)

    def on_start(self):
        """Метод, вызываемый при старте пользователя."""
        # Проверяем, что API доступен
        try:
            response = self.client.get("/health")
            if response.status_code != 200:
                print(f"API недоступен: {response.status_code}")
                raise StopUser()
        except Exception as e:
            print(f"Ошибка при проверке доступности API: {e}")
            raise StopUser()

    @task(1)
    def parse_ad(self):
        """Задача для парсинга объявления."""
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        response = self.client.get(f"/parse?url={url}")
        if response.status_code != 200:
            print(f"Ошибка при парсинге объявления: {response.status_code}")

    @task(2)
    def check_processed(self):
        """Задача для проверки, был ли объект обработан."""
        ad_id = "12345678"
        response = self.client.get(f"/processed?ad_id={ad_id}")
        if response.status_code != 200:
            print(f"Ошибка при проверке обработанного объекта: {response.status_code}")

    @task(1)
    def send_notification(self):
        """Задача для отправки уведомления."""
        property_obj = Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
        )
        response = self.client.post(
            "/notify",
            json={
                "ad_id": "12345678",
                "title": "Тестовая квартира",
                "description": "Описание тестовой квартиры",
                "price": {"amount": 50000, "currency": "USD"},
                "address": {"city": "Киев", "district": "Печерский"},
                "url": "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            },
        )
        if response.status_code != 200:
            print(f"Ошибка при отправке уведомления: {response.status_code}")


class FastUser(HttpUser):
    """Пользователь для нагрузочного тестирования с высокой частотой запросов."""

    wait_time = between(0.1, 0.5)

    @task
    def parse_ad(self):
        """Задача для парсинга объявления."""
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        response = self.client.get(f"/parse?url={url}")
        if response.status_code != 200:
            print(f"Ошибка при парсинге объявления: {response.status_code}")


class SlowUser(HttpUser):
    """Пользователь для нагрузочного тестирования с низкой частотой запросов."""

    wait_time = between(5, 10)

    @task
    def parse_ad(self):
        """Задача для парсинга объявления."""
        url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"
        response = self.client.get(f"/parse?url={url}")
        if response.status_code != 200:
            print(f"Ошибка при парсинге объявления: {response.status_code}")
