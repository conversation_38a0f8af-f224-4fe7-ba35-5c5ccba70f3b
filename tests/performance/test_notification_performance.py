"""
Тесты производительности для сервиса уведомлений.
"""
from unittest.mock import MagicMock

import pytest

from src.application.services.notification import NotificationService
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price


class TestNotificationPerformance:
    """Тесты производительности для сервиса уведомлений."""

    @pytest.fixture
    def notification_sender(self):
        """Фикстура для создания мока отправителя уведомлений."""
        mock = MagicMock()
        return mock

    @pytest.fixture
    def notification_service(self, notification_sender):
        """Фикстура для создания сервиса уведомлений."""
        return NotificationService(notification_sender)

    @pytest.fixture
    def property_obj(self):
        """Фикстура для создания объекта недвижимости."""
        return Property(
            ad_id=AdId("12345678"),
            title="Тестовая квартира",
            description="Описание тестовой квартиры",
            price=Price(50000, "USD"),
            address=Address("Киев", "Печерский"),
            url="https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html",
            area=Area(total=60.5, living=40.0, kitchen=10.0),
            rooms=2,
            floor=5,
            total_floors=9,
            property_type="квартира",
            contact=Contact(name="Иван", phones=["+380991234567"]),
            images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
        )

    def test_send_property_notification_performance(self, benchmark, notification_service, property_obj):
        """Тест производительности отправки уведомления о недвижимости."""
        # Act & Assert
        benchmark(notification_service.notify_new_property, property_obj, ["123456789"], "apartments_sale")

    def test_format_property_notification_performance(self, benchmark, property_obj):
        """Тест производительности форматирования уведомления о недвижимости."""
        # Импортируем функцию форматирования
        from src.presentation.telegram.messages.formatters import format_notification_new_property

        # Act & Assert
        result = benchmark(format_notification_new_property, property_obj, "apartments_sale")

        assert result is not None
        assert "Тестовая квартира" in result
