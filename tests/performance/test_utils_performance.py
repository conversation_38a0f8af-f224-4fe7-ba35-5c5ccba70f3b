"""
Тесты производительности для утилит парсера OLX.
"""
import re

import pytest
from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import get_title, get_description
from src.infrastructure.parsers.olx.utils.price_utils import PriceUtils
from src.infrastructure.parsers.olx.utils.location_utils import LocationUtils


class TestUtilsPerformance:
    """Тесты производительности для утилит парсера OLX."""

    @pytest.fixture
    def sample_html(self):
        """Фикстура для создания тестового HTML."""
        return """
        <html>
            <head>
                <meta property="og:title" content="Тестовая квартира">
                <meta property="og:description" content="Описание тестовой квартиры">
            </head>
            <body>
                <h1 class="css-1soizd2">Тестовая квартира</h1>
                <div data-cy="ad_description">
                    <div>Описание тестовой квартиры</div>
                </div>
                <div class="css-1epmoz1">
                    <p>Цена: 50 000 USD</p>
                    <p>Город: Киев</p>
                    <p>Район: Печерский</p>
                </div>
                <a href="/nedvizhimost/kvartiry/prodazha/">
                    <p>Киев, Печерский</p>
                </a>
                <script>
                    window.__PRERENDERED_STATE__ = {
                        "ad": {
                            "id": "12345678",
                            "map": {
                                "lat": 50.4501,
                                "lon": 30.5234
                            }
                        }
                    };
                </script>
            </body>
        </html>
        """

    def test_parse_html_performance(self, benchmark, sample_html):
        """Тест производительности парсинга HTML."""
        # Act & Assert
        result = benchmark(BeautifulSoup, sample_html, 'html.parser')

        assert result is not None

    def test_get_title_performance(self, benchmark, sample_html):
        """Тест производительности извлечения заголовка."""
        # Arrange
        soup = BeautifulSoup(sample_html, 'html.parser')

        # Act & Assert
        result = benchmark(get_title, soup)

        assert result is not None

    def test_get_description_performance(self, benchmark, sample_html):
        """Тест производительности извлечения описания."""
        # Arrange
        soup = BeautifulSoup(sample_html, 'html.parser')

        # Act & Assert
        result = benchmark(get_description, soup)

        assert result is not None

    def test_extract_price_performance(self, benchmark, sample_html):
        """Тест производительности извлечения цены."""
        # Arrange
        soup = BeautifulSoup(sample_html, 'html.parser')

        # Act & Assert
        result = benchmark(PriceUtils.extract_price_from_soup, soup)

        assert result is not None

    def test_extract_location_performance(self, benchmark, sample_html):
        """Тест производительности извлечения местоположения."""
        # Arrange
        soup = BeautifulSoup(sample_html, 'html.parser')

        # Act & Assert
        result = benchmark(LocationUtils.extract_location_from_soup, soup)

        assert result is not None

    def test_regex_performance(self, benchmark):
        """Тест производительности регулярных выражений."""
        # Arrange
        text = "Цена: 50 000 USD"
        pattern = r'(\d[\d\s,.]*\d|\d)\s*([A-Za-z\u0410-\u042f\u0430-\u044f$€£₴]+)'

        # Act & Assert
        result = benchmark(re.search, pattern, text)

        assert result is not None
