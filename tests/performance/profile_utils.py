"""
Скрипт для профилирования утилит парсера OLX.
"""
import cProfile
import os
import pstats
import sys

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import get_title, get_description
from src.infrastructure.parsers.olx.utils.price_utils import PriceUtils
from src.infrastructure.parsers.olx.utils.location_utils import LocationUtils


def profile_function(func, *args, **kwargs):
    """Профилирование функции."""
    profiler = cProfile.Profile()
    profiler.enable()
    result = func(*args, **kwargs)
    profiler.disable()
    stats = pstats.Stats(profiler).sort_stats('cumtime')
    stats.print_stats(20)
    return result


def main():
    """Основная функция."""
    # Создаем тестовый HTML
    sample_html = """
    <html>
        <head>
            <meta property="og:title" content="Тестовая квартира">
            <meta property="og:description" content="Описание тестовой квартиры">
        </head>
        <body>
            <h1 class="css-1soizd2">Тестовая квартира</h1>
            <div data-cy="ad_description">
                <div>Описание тестовой квартиры</div>
            </div>
            <div class="css-1epmoz1">
                <p>Цена: 50 000 USD</p>
                <p>Город: Киев</p>
                <p>Район: Печерский</p>
            </div>
            <a href="/nedvizhimost/kvartiry/prodazha/">
                <p>Киев, Печерский</p>
            </a>
            <script>
                window.__PRERENDERED_STATE__ = {
                    "ad": {
                        "id": "12345678",
                        "map": {
                            "lat": 50.4501,
                            "lon": 30.5234
                        }
                    }
                };
            </script>
        </body>
    </html>
    """

    # Парсим HTML
    soup = BeautifulSoup(sample_html, 'html.parser')

    # Профилируем функции
    print("Профилирование функции get_title:")
    title = profile_function(get_title, soup)
    print(f"Результат: {title}")

    print("\nПрофилирование функции get_description:")
    description = profile_function(get_description, soup)
    print(f"Результат: {description}")

    print("\nПрофилирование функции extract_price_from_soup:")
    price = profile_function(PriceUtils.extract_price_from_soup, soup)
    print(f"Результат: {price}")

    print("\nПрофилирование функции extract_location_from_soup:")
    location = profile_function(LocationUtils.extract_location_from_soup, soup)
    print(f"Результат: {location}")


if __name__ == "__main__":
    main()
