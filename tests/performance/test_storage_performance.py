"""
Тесты производительности для хранилища.
"""
import os
from datetime import datetime, timezone

import pytest

from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd


class TestStoragePerformance:
    """Тесты производительности для хранилища."""

    @pytest.fixture
    def storage_path(self):
        """Фикстура для создания пути к тестовому хранилищу."""
        return "tests/data/test_storage.json"

    @pytest.fixture
    def storage(self, storage_path):
        """Фикстура для создания тестового хранилища."""
        storage = JsonStorage(storage_path)
        yield storage
        # Cleanup
        if os.path.exists(storage_path):
            os.remove(storage_path)

    def test_add_performance(self, benchmark, storage):
        """Тест производительности добавления данных."""
        # Arrange
        processed_ad = ProcessedAd(ad_id="12345678", processed_at=datetime.now(timezone.utc))

        # Act & Assert
        benchmark(storage.add, processed_ad)

    def test_get_all_performance(self, benchmark, storage):
        """Тест производительности получения всех данных."""
        # Arrange
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at=datetime.now(timezone.utc))
        storage.add(processed_ad)

        # Act & Assert
        result = benchmark(storage.get_all)

        assert result is not None
        assert len(result) > 0

    def test_exists_performance(self, benchmark, storage):
        """Тест производительности проверки, был ли объект обработан."""
        # Arrange
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at=datetime.now(timezone.utc))
        storage.add(processed_ad)

        # Act & Assert
        result = benchmark(storage.exists, ad_id)

        assert result is True

    def test_remove_performance(self, benchmark, storage):
        """Тест производительности удаления объекта."""
        # Arrange
        ad_id = "12345678"
        processed_ad = ProcessedAd(ad_id=ad_id, processed_at=datetime.now(timezone.utc))
        storage.add(processed_ad)

        # Act & Assert
        benchmark(storage.remove, ad_id)
