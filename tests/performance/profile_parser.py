"""
Скрипт для профилирования парсера OLX.
"""
import cProfile
import os
import pstats
import sys
from unittest.mock import MagicMock, patch

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.infrastructure.parsers.olx.olx_parser import OlxParser


def profile_function(func, *args, **kwargs):
    """Профилирование функции."""
    profiler = cProfile.Profile()
    profiler.enable()
    result = func(*args, **kwargs)
    profiler.disable()
    stats = pstats.Stats(profiler).sort_stats('cumtime')
    stats.print_stats(20)
    return result


def main():
    """Основная функция."""
    parser = OlxParser()
    url = "https://www.olx.ua/d/uk/obyavlenie/testovaya-kvartira-ID12345678.html"

    # Создаем тестовый HTML
    sample_html = """
    <html>
        <head>
            <meta property="og:title" content="Тестовая квартира">
            <meta property="og:description" content="Описание тестовой квартиры">
        </head>
        <body>
            <h1 class="css-1soizd2">Тестовая квартира</h1>
            <div data-cy="ad_description">
                <div>Описание тестовой квартиры</div>
            </div>
            <div class="css-1epmoz1">
                <p>Цена: 50 000 USD</p>
                <p>Город: Киев</p>
                <p>Район: Печерский</p>
            </div>
            <a href="/nedvizhimost/kvartiry/prodazha/">
                <p>Киев, Печерский</p>
            </a>
            <script>
                window.__PRERENDERED_STATE__ = {
                    "ad": {
                        "id": "12345678",
                        "map": {
                            "lat": 50.4501,
                            "lon": 30.5234
                        }
                    }
                };
            </script>
        </body>
    </html>
    """

    with patch('requests.get') as mock_get:
        mock_response = MagicMock()
        mock_response.text = sample_html
        mock_get.return_value = mock_response

        print("Профилирование метода parse:")
        property_obj = profile_function(parser.parse, url)
        print(f"Результат: {property_obj}")

        print("\nПрофилирование метода _parse_html:")
        property_obj = profile_function(parser._parse_html, sample_html, url)
        print(f"Результат: {property_obj}")


if __name__ == "__main__":
    main()
