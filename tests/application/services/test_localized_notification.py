import os
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.price import Price

from src.application.services.localized_notification import LocalizedNotificationService
from src.domain.entities.property import Property


@pytest.fixture
def property_obj():
    """Создает тестовый объект недвижимости."""
    return Property(
        ad_id="12345",
        title="Тестовое объявление",
        description="Описание тестового объявления",
        price="50 000 USD",
        location="Киев, Печерский, ул. Тестовая, 1",
        url="https://www.olx.ua/d/obyavlenie/test-12345",
        images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
        phone="+380123456789",
        date="30.04.2025 20:10",
        category="apartments_sale",
        contact_name="Тестовый Пользователь",
        characteristics={
            "Общая": "100 м²",
            "Кухня": "15 м²",
            "Этаж": "5 / 9",
            "Комнат": "3",
            "Тип дома": "Кирпичный"
        }
    )


@pytest.mark.asyncio
async def test_notify_new_property_with_different_languages():
    """Тестирует отправку уведомлений с разными языками."""
    # Создаем моки для зависимостей
    notification_sender = AsyncMock()
    notification_templates = MagicMock()
    webhook_sender = AsyncMock()
    user_language_service = MagicMock()
    localization_service = MagicMock()

    # Настраиваем моки
    notification_templates.format_new_property_notification.return_value = "Тестовое сообщение"
    user_language_service.get_language.side_effect = lambda chat_id: "ru" if chat_id == 123 else "ua"
    localization_service.get_text.side_effect = lambda key, lang: "Открыть объявление" if lang == "ru" else "Відкрити оголошення"
    notification_sender.send_with_images.return_value = True

    # Создаем сервис
    service = LocalizedNotificationService(
        notification_sender=notification_sender,
        notification_templates=notification_templates,
        webhook_sender=webhook_sender,
        user_language_service=user_language_service,
        localization_service=localization_service
    )

    # Создаем моки для обязательных полей
    ad_id = MagicMock(spec=AdId)
    ad_id.__str__.return_value = "12345"

    address = MagicMock(spec=Address)
    address.__str__.return_value = "Киев, Печерский, ул. Тестовая, 1"

    price = MagicMock(spec=Price)
    price.__str__.return_value = "50 000 USD"

    # Создаем объект Property с моками
    property_obj = Property(
        ad_id=ad_id,
        title="Тестовое объявление",
        description="Описание тестового объявления",
        price=price,
        address=address,
        url="https://www.olx.ua/d/obyavlenie/test-12345",
        images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
    )

    # Вызываем метод
    result = await service.notify_new_property(property_obj, [123, 456], "apartments_sale")

    # Проверяем результат
    assert result is True

    # Проверяем, что были вызваны правильные методы
    # В нашем случае, метод format_new_property_notification должен быть вызван для каждого чата и для вебхука
    assert notification_templates.format_new_property_notification.call_count == 3

    # Проверяем, что метод get_language был вызван для каждого чата
    assert user_language_service.get_language.call_count >= 2

    # Проверяем, что метод get_text был вызван для каждого чата и для каждого языка
    assert localization_service.get_text.call_count >= 2


@pytest.mark.asyncio
async def test_notify_new_property_without_language_services():
    """Тестирует отправку уведомлений без сервисов языка."""
    # Создаем моки для зависимостей
    notification_sender = AsyncMock()
    notification_templates = MagicMock()

    # Настраиваем моки
    notification_templates.format_new_property_notification.return_value = "Тестовое сообщение"
    notification_sender.send_with_images.return_value = True

    # Создаем сервис без сервисов языка
    service = LocalizedNotificationService(
        notification_sender=notification_sender,
        notification_templates=notification_templates
    )

    # Создаем моки для обязательных полей
    ad_id = MagicMock(spec=AdId)
    ad_id.__str__.return_value = "12345"

    address = MagicMock(spec=Address)
    address.__str__.return_value = "Киев, Печерский, ул. Тестовая, 1"

    price = MagicMock(spec=Price)
    price.__str__.return_value = "50 000 USD"

    # Создаем объект Property с моками
    property_obj = Property(
        ad_id=ad_id,
        title="Тестовое объявление",
        description="Описание тестового объявления",
        price=price,
        address=address,
        url="https://www.olx.ua/d/obyavlenie/test-12345",
        images=["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
    )

    # Вызываем метод
    result = await service.notify_new_property(property_obj, [123, 456], "apartments_sale")

    # Проверяем результат
    assert result is True

    # Проверяем, что были вызваны правильные методы
    # В нашем случае, метод format_new_property_notification должен быть вызван для каждого чата и для вебхука
    assert notification_templates.format_new_property_notification.call_count == 3
