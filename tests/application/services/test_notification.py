"""
Тесты для сервиса уведомлений.
"""
import unittest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from src.application.services.notification import NotificationService
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.price import Price


class TestNotificationService(unittest.TestCase):
    """
    Тесты для сервиса уведомлений.
    """

    def setUp(self):
        """
        Настройка тестов.
        """
        self.notification_sender = AsyncMock()
        self.notification_sender._logger = MagicMock()

        # Патчим setup_logger, чтобы не создавать реальный файл логов
        with patch('src.application.services.notification.setup_logger') as mock_setup_logger:
            mock_setup_logger.return_value = MagicMock()
            self.notification_service = NotificationService(self.notification_sender)

    def test_notify_new_property_success(self):
        """
        Тест успешной отправки уведомления о новом объекте недвижимости.
        """
        # Создаем тестовые данные
        ad_id = AdId("test-id")
        price = Price(100000, "UAH")
        address = Address("Test Location", "Test City", "Test Region", "Test Country")

        property_obj = Property(
            ad_id=ad_id,
            url="https://example.com/test",
            title="Test Property",
            price=price,
            address=address,
            description="Test Description",
            images=["https://example.com/image1.jpg"],
        )
        chat_ids = [123456789]
        category = "apartments_sale"

        # Настраиваем мок для успешной отправки
        self.notification_sender.send_with_images.return_value = True

        # Вызываем тестируемый метод
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.notification_service.notify_new_property(property_obj, chat_ids, category))

        # Проверяем результат
        self.assertTrue(result)
        self.notification_sender.send_with_images.assert_called_once()

    def test_notify_new_property_empty_chat_ids(self):
        """
        Тест отправки уведомления с пустым списком чатов.
        """
        # Создаем тестовые данные
        ad_id = AdId("test-id")
        price = Price(100000, "UAH")
        address = Address("Test Location", "Test City", "Test Region", "Test Country")

        property_obj = Property(
            ad_id=ad_id,
            url="https://example.com/test",
            title="Test Property",
            price=price,
            address=address,
            description="Test Description",
            images=["https://example.com/image1.jpg"],
        )
        chat_ids = []
        category = "apartments_sale"

        # Вызываем тестируемый метод
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.notification_service.notify_new_property(property_obj, chat_ids, category))

        # Проверяем результат
        self.assertFalse(result)
        self.notification_sender.send_with_images.assert_not_called()

    def test_notify_new_property_no_images(self):
        """
        Тест отправки уведомления без изображений.
        """
        # Создаем тестовые данные
        ad_id = AdId("test-id")
        price = Price(100000, "UAH")
        address = Address("Test Location", "Test City", "Test Region", "Test Country")

        property_obj = Property(
            ad_id=ad_id,
            url="https://example.com/test",
            title="Test Property",
            price=price,
            address=address,
            description="Test Description",
            images=[],
        )
        chat_ids = [123456789]
        category = "apartments_sale"

        # Настраиваем мок для успешной отправки
        self.notification_sender.send.return_value = True

        # Вызываем тестируемый метод
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.notification_service.notify_new_property(property_obj, chat_ids, category))

        # Проверяем результат
        self.assertTrue(result)
        self.notification_sender.send.assert_called_once()
        self.notification_sender.send_with_images.assert_not_called()

    def test_notify_error_success(self):
        """
        Тест успешной отправки уведомления об ошибке.
        """
        # Создаем тестовые данные
        error_message = "Test Error"
        chat_ids = [123456789]

        # Настраиваем мок для успешной отправки
        self.notification_sender.send.return_value = True

        # Вызываем тестируемый метод
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.notification_service.notify_error(error_message, chat_ids))

        # Проверяем результат
        self.assertTrue(result)
        self.notification_sender.send.assert_called_once()

    def test_notify_error_empty_chat_ids(self):
        """
        Тест отправки уведомления об ошибке с пустым списком чатов.
        """
        # Создаем тестовые данные
        error_message = "Test Error"
        chat_ids = []

        # Вызываем тестируемый метод
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.notification_service.notify_error(error_message, chat_ids))

        # Проверяем результат
        self.assertFalse(result)
        self.notification_sender.send.assert_not_called()


if __name__ == "__main__":
    unittest.main()
