"""
Интеграционные тесты для парсера OLX.
"""
import os
from typing import Dict, List, Optional

import pytest
from bs4 import BeautifulSoup

from src.domain.value_objects.ad_id import AdId
from src.infrastructure.parsers.olx.olx_parser import OlxParser
from src.infrastructure.parsers.olx.utils.id_extractor import IdExtractor


class TestOlxParser:
    """Интеграционные тесты для парсера OLX."""

    @pytest.fixture
    def html_fixtures_dir(self):
        """Возвращает путь к директории с HTML фикстурами."""
        return os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'fixtures', 'html')

    @pytest.fixture
    def listing_page_html(self, html_fixtures_dir):
        """Возвращает HTML страницы листинга объявлений."""
        with open(os.path.join(html_fixtures_dir, 'listing_page.html'), 'r', encoding='utf-8') as f:
            return f.read()

    @pytest.fixture
    def detail_page_html(self, html_fixtures_dir):
        """Возвращает HTML страницы с деталями объявления."""
        with open(os.path.join(html_fixtures_dir, 'detail_page.html'), 'r', encoding='utf-8') as f:
            return f.read()

    @pytest.fixture
    def mock_olx_parser(self):
        """Создает мок парсера OLX, который использует сохраненные HTML страницы."""
        class MockOlxParser(OlxParser):
            def __init__(self, listing_html, detail_html):
                # Создаем логгер
                import logging
                from unittest.mock import MagicMock
                self._logger = logging.getLogger(__name__)

                # Создаем мок для хранилища обработанных объявлений
                self._processed_ad_storage = MagicMock()
                self._processed_ad_storage.exists.return_value = False
                self._processed_ad_storage.add.return_value = True

                self.listing_html = listing_html
                self.detail_html = detail_html
                self._config = {
                    'base_url': 'https://www.olx.ua',
                    'categories': {
                        'test_category': {
                            'url': '/test_category',
                            'active': True
                        }
                    },
                    'zyte': {
                        'api_key': 'test_key'
                    }
                }
                self._max_ads = 10
                self._max_pages = 1
                self._max_ads_per_category = 5
                self._chunk_size = 3
                self._max_ad_age_hours = 72
                self._delay = 0
                self._retries = 1
                self._extract_phones = False
                self._extract_images = True
                self._max_images = 5
                self._base_url = 'https://www.olx.ua'
                self._categories = {
                    'test_category': {
                        'url': '/test_category',
                        'active': True
                    }
                }

            def _get_html(self, url: str) -> Optional[str]:
                """Переопределяем метод для использования сохраненных HTML страниц."""
                if 'obyavlenie' in url:
                    return self.detail_html
                else:
                    return self.listing_html

            async def _extract_phones_from_ad(self, ad_id):
                """Переопределяем метод для тестирования."""
                return ["+380991234567"]

            def _extract_ad_details(self, html, ad_id, ad_url):
                """Переопределяем метод для тестирования."""
                return {
                    "id": ad_id,
                    "url": ad_url,
                    "title": "Тестовая квартира",
                    "description": "Описание тестовой квартиры",
                    "price": {"amount": 50000.0, "currency": "USD"},
                    "address": {"city": "Киев", "district": "Печерский"},
                    "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
                    "contact": {"phones": ["+380991234567"]}
                }

        return MockOlxParser

    def test_extract_ad_ids(self, mock_olx_parser, listing_page_html):
        """Тест извлечения ID объявлений из HTML страницы листинга."""
        parser = mock_olx_parser(listing_page_html, None)

        # Вызываем приватный метод для тестирования
        ad_ids = parser._extract_ad_ids(listing_page_html)

        # Проверяем результаты
        assert isinstance(ad_ids, list)
        assert len(ad_ids) > 0

        # Проверяем, что все ID валидные
        for ad_id in ad_ids:
            assert IdExtractor.is_valid_id(ad_id)

    def test_get_ad_ids(self, mock_olx_parser, listing_page_html):
        """Тест получения списка ID объявлений для указанной категории."""
        parser = mock_olx_parser(listing_page_html, None)

        # Вызываем метод
        ad_ids = parser.get_ad_ids('test_category', limit=5)

        # Проверяем результаты
        assert isinstance(ad_ids, list)
        assert len(ad_ids) > 0
        assert len(ad_ids) <= 5

        # Проверяем, что все элементы - экземпляры AdId
        for ad_id in ad_ids:
            assert isinstance(ad_id, AdId)

    def test_extract_ad_details(self, mock_olx_parser, detail_page_html):
        """Тест извлечения данных объявления из HTML."""
        parser = mock_olx_parser(None, detail_page_html)

        # Извлекаем ID объявления из HTML
        soup = BeautifulSoup(detail_page_html, 'html.parser')
        meta_id = soup.find('meta', property='og:url')
        ad_url = meta_id.get('content') if meta_id else None
        ad_id = IdExtractor.extract_id_from_url(ad_url) if ad_url else "test_id"

        # Вызываем приватный метод для тестирования
        ad_details = parser._extract_ad_details(detail_page_html, ad_id, ad_url or "https://www.olx.ua/test")

        # Проверяем результаты
        assert isinstance(ad_details, dict)
        assert 'id' in ad_details
        assert 'url' in ad_details

        # Проверяем наличие основных полей
        # В зависимости от HTML страницы, некоторые поля могут отсутствовать
        assert 'description' in ad_details
        assert isinstance(ad_details['description'], str)

        # Проверяем цену, если она есть
        if 'price' in ad_details:
            assert isinstance(ad_details['price'], dict)
            assert 'amount' in ad_details['price']
            assert 'currency' in ad_details['price']

        # Проверяем описание, если оно есть
        if 'description' in ad_details:
            assert isinstance(ad_details['description'], str)

        # Проверяем изображения, если они есть
        if 'images' in ad_details:
            assert isinstance(ad_details['images'], list)
            for image in ad_details['images']:
                assert isinstance(image, str)
                assert image.startswith('http')

    def test_get_ad_details(self, mock_olx_parser, detail_page_html):
        """Тест получения деталей объявления по его ID."""
        parser = mock_olx_parser(None, detail_page_html)

        # Создаем тестовый ID
        ad_id = AdId("test_id")

        # Вызываем метод
        ad_details = parser.get_ad_details(ad_id)

        # Проверяем результаты
        assert isinstance(ad_details, dict)
        assert 'id' in ad_details
        assert 'url' in ad_details

        # Проверяем наличие основных полей
        # В зависимости от HTML страницы, некоторые поля могут отсутствовать
        assert 'description' in ad_details
        assert isinstance(ad_details['description'], str)

        # Проверяем цену, если она есть
        if 'price' in ad_details:
            assert isinstance(ad_details['price'], dict)
            assert 'amount' in ad_details['price']
            assert 'currency' in ad_details['price']

        # Проверяем описание, если оно есть
        if 'description' in ad_details:
            assert isinstance(ad_details['description'], str)

        # Проверяем изображения, если они есть
        if 'images' in ad_details:
            assert isinstance(ad_details['images'], list)
            for image in ad_details['images']:
                assert isinstance(image, str)
                assert image.startswith('http')
