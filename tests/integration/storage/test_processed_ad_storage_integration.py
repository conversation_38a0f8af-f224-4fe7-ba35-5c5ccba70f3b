"""
Интеграционные тесты для хранилища обработанных ID объявлений.
"""
import os
import tempfile

import pytest
from src.domain.value_objects.ad_id import AdId
from src.infrastructure.persistence.processed_ad_storage import DbProcessedAdStorage
from src.infrastructure.storage.json_storage import JsonStorage


class TestProcessedAdStorage:
    """Интеграционные тесты для хранилища обработанных ID объявлений."""

    @pytest.fixture
    def temp_file(self):
        """Фикстура для создания временного файла."""
        fd, path = tempfile.mkstemp()
        os.close(fd)
        yield path
        os.unlink(path)

    @pytest.fixture
    def storage(self, temp_file):
        """Фикстура для создания хранилища с временным файлом."""
        json_storage = JsonStorage(file_path=temp_file)
        return DbProcessedAdStorage(json_storage=json_storage)

    def test_add_and_exists(self, storage):
        """Тест добавления ID и проверки его существования."""
        # Создаем тестовый ID
        ad_id = AdId("12345678")
        
        # Проверяем, что ID не существует
        assert not storage.exists(ad_id)
        
        # Добавляем ID
        storage.add(ad_id)
        
        # Проверяем, что ID существует
        assert storage.exists(ad_id)

    def test_add_multiple_and_get_all(self, storage):
        """Тест добавления нескольких ID и получения всех ID."""
        # Создаем тестовые ID
        ad_ids = [AdId("12345678"), AdId("87654321"), AdId("11223344")]
        
        # Добавляем ID
        for ad_id in ad_ids:
            storage.add(ad_id)
        
        # Получаем все ID
        all_ids = storage.get_all()
        
        # Проверяем, что все ID получены
        assert len(all_ids) == len(ad_ids)
        
        # Проверяем, что все добавленные ID присутствуют в результате
        for ad_id in ad_ids:
            assert ad_id in all_ids

    def test_add_duplicate(self, storage):
        """Тест добавления дубликата ID."""
        # Создаем тестовый ID
        ad_id = AdId("12345678")
        
        # Добавляем ID
        storage.add(ad_id)
        
        # Добавляем тот же ID еще раз
        storage.add(ad_id)
        
        # Получаем все ID
        all_ids = storage.get_all()
        
        # Проверяем, что в хранилище только один экземпляр ID
        assert len(all_ids) == 1
        assert all_ids[0] == ad_id

    def test_persistence(self, temp_file):
        """Тест сохранения данных между экземплярами хранилища."""
        # Создаем первый экземпляр хранилища
        json_storage1 = JsonStorage(file_path=temp_file)
        storage1 = DbProcessedAdStorage(json_storage=json_storage1)
        
        # Добавляем ID
        ad_id = AdId("12345678")
        storage1.add(ad_id)
        
        # Создаем второй экземпляр хранилища с тем же файлом
        json_storage2 = JsonStorage(file_path=temp_file)
        storage2 = DbProcessedAdStorage(json_storage=json_storage2)
        
        # Проверяем, что ID существует во втором экземпляре
        assert storage2.exists(ad_id)
        
        # Получаем все ID из второго экземпляра
        all_ids = storage2.get_all()
        
        # Проверяем, что ID присутствует в результате
        assert len(all_ids) == 1
        assert all_ids[0] == ad_id
