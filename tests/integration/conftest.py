"""
Общие фикстуры для интеграционных тестов.
"""
import os
import sys
import pytest
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.insert(0, os.path.abspath('.'))

# Путь к директории с фикстурами
FIXTURES_DIR = Path(__file__).parent.parent / "fixtures"
HTML_FIXTURES_DIR = FIXTURES_DIR / "html"

@pytest.fixture
def html_fixtures_dir():
    """Возвращает путь к директории с HTML фикстурами."""
    return HTML_FIXTURES_DIR
