#!/usr/bin/env python3
"""
Скрипт для тестирования новой системы настроек.
"""
import sys
import os

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.insert(0, os.path.abspath('.'))

from src.config import settings

def main():
    """
    Основная функция для тестирования настроек.
    """
    print("Тестирование новой системы настроек:")
    print("-" * 50)
    
    # Тестирование настроек хранилища
    print(f"Путь к файлу хранилища: {settings.get_storage_file_path()}")
    
    # Тестирование настроек логирования
    print(f"Уровень логирования: {settings.get_log_level()}")
    print(f"Путь к файлу логов: {settings.get_log_file_path()}")
    
    # Тестирование настроек Telegram
    print(f"Токен Telegram: {settings.get_telegram_token()}")
    print(f"ID чатов Telegram: {settings.get_telegram_chat_ids()}")
    
    # Тестирование настроек OLX
    olx_config = settings.olx
    print(f"Настройки OLX: {len(olx_config)} параметров")
    
    # Тестирование настроек планировщика
    print(f"Интервал планировщика: {settings.get_scheduler_interval()} секунд")
    print(f"Планировщик включен: {settings.get_scheduler_enabled()}")
    print(f"Категории планировщика: {settings.get_scheduler_categories()}")
    
    print("-" * 50)
    print("Тестирование завершено успешно!")

if __name__ == "__main__":
    main()
