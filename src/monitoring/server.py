"""
Сервер метрик для проекта Parser OLX.
"""
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler

# Проверяем доступность модуля Prometheus
# Мы импортируем модуль только для проверки его наличия, а конкретные функции импортируем внутри методов
try:
    import prometheus_client  # noqa: F401
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False


class MetricsHandler(BaseHTTPRequestHandler):
    """Обработчик запросов для метрик."""

    def do_GET(self):
        """Обработка GET-запроса."""
        if not PROMETHEUS_AVAILABLE:
            self.send_response(404)
            self.end_headers()
            return

        # Импортируем модули Prometheus внутри метода, чтобы избежать ошибок типизации
        from prometheus_client import generate_latest, CONTENT_TYPE_LATEST

        if self.path == '/metrics':
            self.send_response(200)
            self.send_header('Content-Type', CONTENT_TYPE_LATEST)
            self.end_headers()
            self.wfile.write(generate_latest())
        else:
            self.send_response(404)
            self.end_headers()


def start_metrics_server(port=8000):
    """
    Запуск сервера метрик.

    Args:
        port: Порт для сервера метрик.
    """
    if not PROMETHEUS_AVAILABLE:
        return

    server = HTTPServer(('', port), MetricsHandler)

    def run_server():
        """Запуск сервера в отдельном потоке."""
        server.serve_forever()

    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()

    return server


if __name__ == '__main__':
    # Запуск сервера метрик
    server = start_metrics_server()

    # Бесконечный цикл для поддержания работы сервера
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Проверяем, что сервер был успешно запущен
        if server is not None:
            server.shutdown()
