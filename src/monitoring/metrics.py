"""
Метрики для мониторинга проекта Parser OLX.
"""
try:
    from prometheus_client import Counter, Gauge, Histogram, Summary
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    Counter = Gauge = Histogram = Summary = lambda *args, **kwargs: None


if PROMETHEUS_AVAILABLE:
    # Счетчики
    PARSED_URLS = Counter('parsed_urls_total', 'Total number of parsed URLs')
    FAILED_URLS = Counter('failed_urls_total', 'Total number of failed URLs')
    PROCESSED_PROPERTIES = Counter('processed_properties_total', 'Total number of processed properties')
    SENT_NOTIFICATIONS = Counter('sent_notifications_total', 'Total number of sent notifications')

    # Гистограммы
    PARSING_TIME = Histogram('parsing_time_seconds', 'Time spent parsing URLs')
    NOTIFICATION_TIME = Histogram('notification_time_seconds', 'Time spent sending notifications')

    # Измерители
    ACTIVE_PARSERS = Gauge('active_parsers', 'Number of active parsers')
    QUEUE_SIZE = Gauge('queue_size', 'Size of the parsing queue')

    # Сводки
    PROPERTY_PRICE = Summary('property_price_dollars', 'Property price in dollars')
else:
    # Заглушки для метрик
    class DummyMetric:
        """Заглушка для метрик."""
        def inc(self, *args, **kwargs):
            pass
        
        def dec(self, *args, **kwargs):
            pass
        
        def observe(self, *args, **kwargs):
            pass
        
        def time(self, *args, **kwargs):
            class DummyTimer:
                def __enter__(self):
                    pass
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    pass
            
            return DummyTimer()
    
    PARSED_URLS = FAILED_URLS = PROCESSED_PROPERTIES = SENT_NOTIFICATIONS = DummyMetric()
    PARSING_TIME = NOTIFICATION_TIME = DummyMetric()
    ACTIVE_PARSERS = QUEUE_SIZE = DummyMetric()
    PROPERTY_PRICE = DummyMetric()
