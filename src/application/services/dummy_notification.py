"""
Реализация заглушки для сервиса уведомлений.
"""
import logging
from typing import Optional, Union

from src.application.interfaces.notification_service import INotificationService
from src.domain.entities.property import Property


class DummyNotificationService(INotificationService):
    """
    Заглушка для сервиса уведомлений.
    Не отправляет никаких уведомлений, но реализует интерфейс INotificationService.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Инициализирует заглушку для сервиса уведомлений.

        Args:
            logger: Логгер (опционально)
        """
        self._logger = logger or logging.getLogger(__name__)

    async def notify_new_property(self, property_obj: Property, chat_ids: Union[list[str], list[int]], category: Optional[str] = None) -> bool:
        """
        Имитирует отправку уведомления о новом объекте недвижимости.

        Args:
            property_obj: Объект недвижимости
            chat_ids: Список ID чатов для отправки
            category: Категория недвижимости (опционально)

        Returns:
            bool: Всегда возвращает True
        """
        self._logger.info(f"[DUMMY] Имитация отправки уведомления о новом объекте недвижимости {property_obj.ad_id} в чаты {chat_ids}, категория: {category or 'не указана'}")
        return True

    async def notify_error(self, error_message: str, chat_ids: Union[list[str], list[int]]) -> bool:
        """
        Имитирует отправку уведомления об ошибке.

        Args:
            error_message: Сообщение об ошибке
            chat_ids: Список ID чатов для отправки

        Returns:
            bool: Всегда возвращает True
        """
        self._logger.info(f"[DUMMY] Имитация отправки уведомления об ошибке: {error_message} в чаты {chat_ids}")
        return True

    async def notify_parser_error(self, error_message: str, error_chat_id: Union[str, int]) -> bool:
        """
        Имитирует отправку уведомления об ошибке парсера в специальный чат.

        Args:
            error_message: Сообщение об ошибке
            error_chat_id: ID чата для отправки уведомлений об ошибках

        Returns:
            bool: Всегда возвращает True
        """
        self._logger.info(f"[DUMMY] Имитация отправки уведомления об ошибке парсера: {error_message} в чат {error_chat_id}")
        return True
