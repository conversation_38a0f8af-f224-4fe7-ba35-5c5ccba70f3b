"""
Сервис для работы с объектами недвижимости.
"""
import logging
from typing import List, Optional, Tuple, Union

from src.application.interfaces.notification_service import INotificationService
from src.application.interfaces.parser import IParser
from src.application.interfaces.processed_ad_storage import IProcessedAdStorage
from src.domain.entities.property import Property
from src.domain.services.property_validator import PropertyValidator
from src.domain.value_objects.ad_id import AdId
from src.config.app_settings import Settings

# Импортируем метрики из модуля monitoring.metrics
from src.monitoring.metrics import (
    ACTIVE_PARSERS as _ACTIVE_PARSERS,
    FAILED_URLS as _FAILED_URLS,
    NOTIFICATION_TIME as _NOTIFICATION_TIME,
    PARSED_URLS as _PARSED_URLS,
    PARSING_TIME as _PARSING_TIME,
    PROCESSED_PROPERTIES as _PROCESSED_PROPERTIES,
    PROPERTY_PRICE as _PROPERTY_PRICE,
    SENT_NOTIFICATIONS as _SENT_NOTIFICATIONS
)

# Создаем локальные переменные для метрик
ACTIVE_PARSERS = _ACTIVE_PARSERS
FAILED_URLS = _FAILED_URLS
NOTIFICATION_TIME = _NOTIFICATION_TIME
PARSED_URLS = _PARSED_URLS
PARSING_TIME = _PARSING_TIME
PROCESSED_PROPERTIES = _PROCESSED_PROPERTIES
PROPERTY_PRICE = _PROPERTY_PRICE
SENT_NOTIFICATIONS = _SENT_NOTIFICATIONS


class PropertyService:
    """
    Сервис для работы с объектами недвижимости.
    Оркестрирует основной сценарий использования (use case).
    """

    def __init__(self, parser: IParser, processed_ad_storage: IProcessedAdStorage,
                 notification_service: INotificationService, property_validator: PropertyValidator,
                 logger: Optional[logging.Logger] = None):
        """
        Инициализирует сервис работы с объектами недвижимости.

        Args:
            parser: Реализация интерфейса IParser
            processed_ad_storage: Реализация интерфейса IProcessedAdStorage
            notification_service: Реализация интерфейса INotificationService
            property_validator: Валидатор объектов недвижимости
            logger: Логгер (опционально)
        """
        self._parser = parser
        self._processed_ad_storage = processed_ad_storage
        self._notification_service = notification_service
        self._property_validator = property_validator
        self._logger = logger or logging.getLogger(__name__)

        # Удаляем старые записи при инициализации
        self._cleanup_old_ads()

    def _cleanup_old_ads(self) -> None:
        """
        Удаляет старые записи об обработанных объявлениях.
        """
        try:
            # Получаем настройки хранилища
            settings = Settings()
            max_storage_days = settings.get_storage_max_days()

            # Удаляем старые записи
            if hasattr(self._processed_ad_storage, 'remove_old_ads'):
                removed_count = self._processed_ad_storage.remove_old_ads(max_storage_days)
                self._logger.info(f"Удалено {removed_count} старых записей (старше {max_storage_days} дней)")
            else:
                self._logger.warning("Хранилище не поддерживает удаление старых записей")
        except Exception as e:
            self._logger.error(f"Ошибка при удалении старых записей: {e}", exc_info=True)

    async def process_category(self, category: str, chat_ids: Union[List[str], List[int]],
                         limit: Optional[int] = None, chunk_size: Optional[int] = None,
                         error_chat_id: Optional[int] = None) -> Tuple[int, int, int]:
        """
        Обрабатывает категорию объявлений с учетом настроек парсера.

        Метод использует следующие настройки парсера:
        - max_ads_per_category: максимальное количество объявлений в одной категории
        - max_ads: общее максимальное количество объявлений за запуск
        - max_pages: максимальное количество страниц для парсинга
        - chunk_size: размер чанка для групповой обработки объявлений
        - max_ad_age_hours: максимальный возраст объявления в часах

        Args:
            category: Категория объявлений
            chat_ids: Список ID чатов для отправки уведомлений
            limit: Максимальное количество объявлений для обработки (переопределяет max_ads_per_category из конфигурации)
            chunk_size: Размер чанка для групповой обработки объявлений (переопределяет chunk_size из конфигурации)
            error_chat_id: ID чата для отправки уведомлений об ошибках (опционально)

        Returns:
            Tuple[int, int, int]: (количество новых объявлений, количество обработанных объявлений, количество ошибок)
        """
        self._logger.info(f"Начало обработки категории: {category}")
        ACTIVE_PARSERS.inc()

        # Получаем список ID объявлений
        # Парсер уже фильтрует объявления по дате и проверяет, были ли они уже обработаны
        try:
            with PARSING_TIME.time():
                ad_ids = self._parser.get_ad_ids(category, limit, chunk_size)
            self._logger.info(f"Получено {len(ad_ids)} новых ID объявлений")
            PARSED_URLS.inc()
        except Exception as e:
            self._logger.error(f"Ошибка при получении ID объявлений: {e}", exc_info=True)
            FAILED_URLS.inc()

            # Получаем ID чата для отправки уведомлений об ошибках, если он не был передан
            if not error_chat_id:
                settings = Settings()
                error_chat_id = settings.get_telegram_error_chat_id()
                self._logger.info(f"DEBUG: Получен ID чата для отправки уведомлений об ошибках из настроек: {error_chat_id}")
            else:
                self._logger.info(f"DEBUG: Используется переданный ID чата для отправки уведомлений об ошибках: {error_chat_id}")

            # Отправляем уведомление в специальный чат для ошибок, если он указан
            if error_chat_id:
                await self._notification_service.notify_parser_error(
                    f"Ошибка при получении ID объявлений для категории {category}: {str(e)}",
                    error_chat_id
                )

            # Преобразуем все ID чатов в целые числа
            normalized_chat_ids = []
            for chat_id in chat_ids:
                try:
                    if isinstance(chat_id, str):
                        normalized_chat_ids.append(int(chat_id))
                    else:
                        normalized_chat_ids.append(chat_id)
                except (ValueError, TypeError) as conversion_error:
                    self._logger.error(f"Ошибка при преобразовании ID чата {chat_id} в число: {conversion_error}")

            # Отправляем уведомление в обычные чаты
            await self._notification_service.notify_error(
                f"Ошибка при получении ID объявлений для категории {category}: {str(e)}",
                normalized_chat_ids
            )
            ACTIVE_PARSERS.dec()
            return 0, 0, 1

        # Счетчики
        new_count = 0
        processed_count = 0
        error_count = 0

        # Разбиваем список ID объявлений на чанки для обработки
        # Используем переданный размер чанка или значение из конфигурации парсера
        chunk_size = chunk_size or getattr(self._parser, '_chunk_size', 5)
        # Проверяем, что chunk_size не None и больше 0
        if chunk_size is None or chunk_size <= 0:
            chunk_size = 5  # Используем значение по умолчанию, если не задано или некорректно

        # Разбиваем список ID на чанки
        total_ads = len(ad_ids)
        # Теперь chunk_size гарантированно не None и больше 0
        total_chunks = (total_ads + chunk_size - 1) // chunk_size  # Округление вверх
        self._logger.info(f"Разбиваем {total_ads} ID объявлений на {total_chunks} чанков по {chunk_size} штук")

        # Обрабатываем каждый чанк
        for chunk_index in range(total_chunks):
            start_index = chunk_index * chunk_size
            end_index = min(start_index + chunk_size, total_ads)
            current_chunk = ad_ids[start_index:end_index]

            self._logger.info(f"Обработка чанка {chunk_index + 1}/{total_chunks} ({len(current_chunk)} объявлений)")

            # Обрабатываем каждый ID в текущем чанке
            # Парсер уже проверил, что эти ID не были обработаны ранее
            for ad_id in current_chunk:
                processed_count += 1

                # Получаем детали объявления
                try:
                    with PARSING_TIME.time():
                        ad_details = self._parser.get_ad_details(ad_id)
                    if not ad_details:
                        self._logger.warning(f"Не удалось получить детали для ID {ad_id}")
                        FAILED_URLS.inc()
                        error_count += 1
                        continue
                    PARSED_URLS.inc()
                except Exception as e:
                    self._logger.error(f"Ошибка при получении деталей объявления {ad_id}: {e}", exc_info=True)
                    FAILED_URLS.inc()

                    # Получаем ID чата для отправки уведомлений об ошибках, если он не был передан
                    if not error_chat_id:
                        settings = Settings()
                        error_chat_id = settings.get_telegram_error_chat_id()
                        self._logger.info(f"DEBUG: Получен ID чата для отправки уведомлений об ошибках из настроек: {error_chat_id}")
                    else:
                        self._logger.info(f"DEBUG: Используется переданный ID чата для отправки уведомлений об ошибках: {error_chat_id}")

                    # Отправляем уведомление в специальный чат для ошибок, если он указан
                    if error_chat_id:
                        await self._notification_service.notify_parser_error(
                            f"Ошибка при получении деталей объявления {ad_id} для категории {category}: {str(e)}",
                            error_chat_id
                        )

                    error_count += 1
                    continue

                # Логируем полученные данные для отладки
                import json
                self._logger.debug(f"Полученные детали объявления {ad_id}: {json.dumps(ad_details, ensure_ascii=False, indent=2)}")

                # Если нужно получить телефоны асинхронно
                if hasattr(self._parser, '_extract_phones_from_ad') and getattr(self._parser, '_extract_phones', False):
                    try:
                        # Получаем телефоны асинхронно
                        extract_phones_method = getattr(self._parser, '_extract_phones_from_ad')
                        phones = await extract_phones_method(ad_id)
                        if phones:
                            if 'contact' not in ad_details:
                                ad_details['contact'] = {}
                            ad_details['contact']['phones'] = phones
                    except Exception as e:
                        self._logger.error(f"Ошибка при получении телефонов для ID {ad_id}: {e}", exc_info=True)
                        # Продолжаем работу даже если не удалось получить телефоны

                # Создаем объект Property
                try:
                    property_obj = Property.from_dict(ad_details)
                    if not property_obj:
                        self._logger.warning(f"Не удалось создать объект Property из данных для ID {ad_id}")
                        FAILED_URLS.inc()
                        error_count += 1
                        continue

                    # Добавляем цену в метрики
                    if property_obj.price and property_obj.price.amount:
                        PROPERTY_PRICE.observe(property_obj.price.amount)
                except Exception as e:
                    self._logger.error(f"Ошибка при создании объекта Property для ID {ad_id}: {e}", exc_info=True)
                    FAILED_URLS.inc()
                    error_count += 1
                    continue

                # Валидируем объект Property
                is_valid, validation_errors = self._property_validator.validate(property_obj)
                if not is_valid:
                    self._logger.warning(f"Объект Property для ID {ad_id} не прошел валидацию: {validation_errors}")
                    # Можно решить, нужно ли отправлять уведомление о невалидном объекте
                    # В данном случае, мы все равно отправляем уведомление, но логируем ошибки

                # Отправляем уведомление
                try:
                    self._logger.info(f"DEBUG: Попытка отправки уведомления о новом объявлении {ad_id} в чаты: {chat_ids}")
                    self._logger.info(f"DEBUG: Список чатов: {chat_ids}, тип: {type(chat_ids)}")
                    self._logger.info(f"DEBUG: Категория: {category}")
                    self._logger.info(f"DEBUG: Объект Property: {property_obj.ad_id}, URL: {property_obj.url}")

                    # Преобразуем все ID чатов в целые числа
                    normalized_chat_ids = []
                    for chat_id in chat_ids:
                        try:
                            if isinstance(chat_id, str):
                                normalized_chat_ids.append(int(chat_id))
                            else:
                                normalized_chat_ids.append(chat_id)
                        except (ValueError, TypeError) as conversion_error:
                            self._logger.error(f"Ошибка при преобразовании ID чата {chat_id} в число: {conversion_error}")

                    self._logger.info(f"Начало отправки уведомления о новом объявлении {ad_id} в чаты: {normalized_chat_ids}")

                    with NOTIFICATION_TIME.time():
                        notification_sent = await self._notification_service.notify_new_property(property_obj, normalized_chat_ids, category)

                    self._logger.info(f"DEBUG: Результат отправки уведомления: {notification_sent}")

                    if notification_sent:
                        self._logger.info(f"Успешно отправлено уведомление о новом объявлении {ad_id} в чаты: {chat_ids}")
                        SENT_NOTIFICATIONS.inc()
                        new_count += 1
                    else:
                        self._logger.warning(f"Не удалось отправить уведомление о новом объявлении {ad_id} в чаты: {chat_ids}")
                        FAILED_URLS.inc()
                        error_count += 1
                except Exception as e:
                    self._logger.error(f"Ошибка при отправке уведомления о новом объявлении {ad_id} в чаты: {chat_ids}: {e}", exc_info=True)
                    self._logger.error(f"DEBUG: Детали ошибки: {str(e)}")
                    FAILED_URLS.inc()
                    error_count += 1
                    continue

                # ID уже добавлен в хранилище обработанных в методе get_ad_details парсера
                # Увеличиваем счетчик обработанных объявлений
                PROCESSED_PROPERTIES.inc()

            # Логируем информацию о завершении обработки чанка
            self._logger.info(f"Завершена обработка чанка {chunk_index + 1}/{total_chunks}. "
                             f"Новых: {new_count}, Обработано: {processed_count}, Ошибок: {error_count}")

        self._logger.info(f"Завершена обработка категории {category}. "
                         f"Новых: {new_count}, Обработано: {processed_count}, Ошибок: {error_count}")

        # Отправляем уведомление о статусе парсинга
        try:
            # Вычисляем приблизительное время выполнения (заглушка)
            # В реальности нужно измерять время выполнения
            # Переменная execution_time не используется, так как отправка уведомлений отключена
            # execution_time = f"{processed_count / 10:.2f} сек" if processed_count > 0 else "0.00 сек"

            # Проверяем, есть ли у сервиса уведомлений метод notify_parsing_status
            if hasattr(self._notification_service, 'notify_parsing_status'):
                # Преобразуем все ID чатов в целые числа
                normalized_chat_ids = []
                for chat_id in chat_ids:
                    try:
                        if isinstance(chat_id, str):
                            normalized_chat_ids.append(int(chat_id))
                        else:
                            normalized_chat_ids.append(chat_id)
                    except (ValueError, TypeError) as conversion_error:
                        self._logger.error(f"Ошибка при преобразовании ID чата {chat_id} в число: {conversion_error}")

                # Отключаем отправку уведомления о статусе парсинга
                # await self._notification_service.notify_parsing_status(
                #     category=category,
                #     new_count=new_count,
                #     processed_count=processed_count,
                #     error_count=error_count,
                #     execution_time=execution_time,
                #     chat_ids=normalized_chat_ids
                # )
                self._logger.info(f"Отправка уведомления о статусе парсинга категории {category} отключена")
        except Exception as e:
            self._logger.error(f"Ошибка при отправке уведомления о статусе парсинга: {e}", exc_info=True)

        # Закрываем браузер Playwright, если он был использован
        # Проверяем, является ли парсер экземпляром OlxParser
        if hasattr(self._parser, '__class__') and self._parser.__class__.__name__ == 'OlxParser':
            # Проверяем, есть ли у парсера атрибут _playwright_client
            if hasattr(self._parser, '_playwright_client'):
                try:
                    import asyncio
                    # Получаем текущий event loop
                    try:
                        loop = asyncio.get_event_loop()
                    except RuntimeError:
                        # Если цикл событий не найден, создаем новый
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    # Закрываем браузер
                    # Используем getattr для безопасного доступа к атрибуту
                    playwright_client = getattr(self._parser, '_playwright_client', None)
                    if playwright_client:
                        loop.run_until_complete(playwright_client.close())
                    self._logger.info("Браузер Playwright успешно закрыт")
                except Exception as e:
                    self._logger.error(f"Ошибка при закрытии браузера Playwright: {e}")

        ACTIVE_PARSERS.dec()
        return new_count, processed_count, error_count

    async def get_property_by_id(self, ad_id: Union[str, AdId]) -> Optional[Property]:
        """
        Получает объект недвижимости по ID.

        Args:
            ad_id: ID объявления

        Returns:
            Optional[Property]: Объект недвижимости или None, если объявление не найдено
        """
        self._logger.info(f"Начало получения объекта недвижимости по ID: {ad_id}")
        ACTIVE_PARSERS.inc()

        try:
            # Преобразуем строку в AdId, если необходимо
            if isinstance(ad_id, str):
                ad_id = AdId(ad_id)

            # Получаем детали объявления
            with PARSING_TIME.time():
                ad_details = self._parser.get_ad_details(ad_id)
            if not ad_details:
                self._logger.warning(f"Не удалось получить детали для ID {ad_id}")
                FAILED_URLS.inc()
                ACTIVE_PARSERS.dec()
                return None
            PARSED_URLS.inc()

            # Если нужно получить телефоны асинхронно
            if hasattr(self._parser, '_extract_phones_from_ad') and getattr(self._parser, '_extract_phones', False):
                try:
                    # Получаем телефоны асинхронно
                    extract_phones_method = getattr(self._parser, '_extract_phones_from_ad')
                    phones = await extract_phones_method(ad_id)
                    if phones:
                        if 'contact' not in ad_details:
                            ad_details['contact'] = {}
                        ad_details['contact']['phones'] = phones
                except Exception as e:
                    self._logger.error(f"Ошибка при получении телефонов для ID {ad_id}: {e}", exc_info=True)
                    # Продолжаем работу даже если не удалось получить телефоны

            # Создаем объект Property
            property_obj = Property.from_dict(ad_details)
            if not property_obj:
                self._logger.warning(f"Не удалось создать объект Property из данных для ID {ad_id}")
                FAILED_URLS.inc()
                ACTIVE_PARSERS.dec()
                return None

            # Добавляем цену в метрики
            if property_obj.price and property_obj.price.amount:
                PROPERTY_PRICE.observe(property_obj.price.amount)

            self._logger.info(f"Успешно получен объект недвижимости по ID: {ad_id}")
            ACTIVE_PARSERS.dec()
            return property_obj
        except Exception as e:
            self._logger.error(f"Ошибка при получении объекта недвижимости по ID {ad_id}: {e}", exc_info=True)
            FAILED_URLS.inc()
            ACTIVE_PARSERS.dec()
            return None
