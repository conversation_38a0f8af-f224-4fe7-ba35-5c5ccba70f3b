"""
Сервис локализации.
"""
import logging
from typing import Dict, Any, Optional

from src.domain.interfaces.localization_service import ILocalizationService


class LocalizationService:
    """
    Сервис локализации.
    """

    def __init__(self, localization_service: ILocalizationService, default_language: str = 'ru'):
        """
        Инициализирует сервис локализации.

        Args:
            localization_service: Реализация интерфейса ILocalizationService
            default_language: Язык по умолчанию
        """
        self._localization_service = localization_service
        self._default_language = default_language
        self._logger = logging.getLogger(__name__)

    def get_text(self, key: str, language: Optional[str] = None) -> str:
        """
        Получает локализованный текст по ключу.

        Args:
            key: Ключ текста
            language: Код языка (опционально)

        Returns:
            str: Локализованный текст
        """
        lang = language or self._default_language
        self._logger.info(f"DEBUG: LocalizationService.get_text - Запрос текста для ключа '{key}', язык '{lang}'")
        result = self._localization_service.get_text(key, lang, key)
        self._logger.info(f"DEBUG: LocalizationService.get_text - Получен результат: '{result}'")
        return result

    def get_template(self, key: str, language: Optional[str] = None) -> str:
        """
        Получает локализованный шаблон по ключу.

        Args:
            key: Ключ шаблона
            language: Код языка (опционально)

        Returns:
            str: Локализованный шаблон
        """
        lang = language or self._default_language
        return self._localization_service.get_template(key, lang, key)

    def format_template(self, template: str, params: Dict[str, Any]) -> str:
        """
        Форматирует шаблон с параметрами.

        Args:
            template: Шаблон
            params: Параметры для форматирования

        Returns:
            str: Отформатированный текст
        """
        return self._localization_service.format_template(template, params)
