"""
Сервис для управления языком пользователя.
"""
import logging
from typing import Optional, List

from src.domain.value_objects.localization import Language


class UserLanguageService:
    """
    Сервис для управления языком пользователя.
    """

    def __init__(self, user_language_storage, available_languages: List[Language], default_language: str = 'ru'):
        """
        Инициализирует сервис управления языком пользователя.

        Args:
            user_language_storage: Хранилище языковых настроек пользователей
            available_languages: Список доступных языков
            default_language: Язык по умолчанию
        """
        self._user_language_storage = user_language_storage
        self._available_languages = available_languages
        self._default_language = default_language
        self._logger = logging.getLogger(__name__)

    def get_language(self, user_id: int) -> str:
        """
        Получает язык пользователя.

        Args:
            user_id: ID пользователя

        Returns:
            str: Код языка
        """
        self._logger.info(f"Запрос языка для пользователя {user_id}")

        # Принудительно перезагружаем настройки из файла
        if hasattr(self._user_language_storage, '_load'):
            try:
                self._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены при запросе языка")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_storage.get_language(user_id) or self._default_language
        self._logger.info(f"Язык пользователя {user_id}: {language}")

        return language

    def set_language(self, user_id: int, language: str) -> bool:
        """
        Устанавливает язык пользователя.

        Args:
            user_id: ID пользователя
            language: Код языка

        Returns:
            bool: True, если язык успешно установлен
        """
        self._logger.info(f"Попытка установить язык {language} для пользователя {user_id}")

        # Проверяем, что язык доступен
        if language not in [lang.code for lang in self._available_languages]:
            self._logger.warning(f"Язык {language} не поддерживается")
            return False

        # Получаем текущий язык пользователя
        current_language = self._user_language_storage.get_language(user_id)

        # Если язык не изменился, просто возвращаем True
        if current_language == language:
            self._logger.info(f"Язык пользователя {user_id} не изменился: {language}")
            return True

        # Устанавливаем язык
        try:
            self._user_language_storage.set_language(user_id, language)
            self._logger.info(f"Язык пользователя {user_id} успешно изменен с {current_language} на {language}")

            # Принудительно перезагружаем настройки из файла
            if hasattr(self._user_language_storage, '_load'):
                try:
                    self._user_language_storage._load()
                    self._logger.info(f"Языковые настройки пользователей успешно перезагружены после установки языка")
                except Exception as e:
                    self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

            return True
        except Exception as e:
            self._logger.error(f"Ошибка при установке языка {language} для пользователя {user_id}: {e}")
            return False

    def get_available_languages(self) -> List[Language]:
        """
        Получает список доступных языков.

        Returns:
            List[Language]: Список доступных языков
        """
        return self._available_languages

    def reload_language_settings(self) -> bool:
        """
        Перезагружает языковые настройки пользователей из хранилища.

        Returns:
            bool: True, если настройки успешно перезагружены, иначе False
        """
        self._logger.info("Попытка перезагрузки языковых настроек пользователей")

        # Проверяем, что хранилище поддерживает перезагрузку
        if hasattr(self._user_language_storage, '_load'):
            try:
                self._user_language_storage._load()
                self._logger.info("Языковые настройки пользователей успешно перезагружены")
                return True
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")
                return False
        else:
            self._logger.warning("Хранилище не поддерживает перезагрузку языковых настроек")
            return False

    def detect_language(self, telegram_language_code: Optional[str]) -> str:
        """
        Определяет язык пользователя на основе языка Telegram.

        Args:
            telegram_language_code: Код языка Telegram

        Returns:
            str: Код языка
        """
        if not telegram_language_code:
            return self._default_language

        # Преобразуем код языка Telegram в код языка приложения
        language_mapping = {
            'ru': 'ru',  # Русский
            'uk': 'ua',  # Украинский (в Telegram используется код 'uk')
            'ua': 'ua',  # Украинский (на случай, если в Telegram будет использоваться код 'ua')
            'en': 'ru',  # Английский (по умолчанию используем русский)
            'be': 'ru',  # Белорусский (по умолчанию используем русский)
            'kk': 'ru',  # Казахский (по умолчанию используем русский)
            'uz': 'ru',  # Узбекский (по умолчанию используем русский)
            'az': 'ru',  # Азербайджанский (по умолчанию используем русский)
            'hy': 'ru',  # Армянский (по умолчанию используем русский)
            'ka': 'ru',  # Грузинский (по умолчанию используем русский)
            'tg': 'ru',  # Таджикский (по умолчанию используем русский)
            'tk': 'ru',  # Туркменский (по умолчанию используем русский)
            'ky': 'ru',  # Киргизский (по умолчанию используем русский)
            'mo': 'ru',  # Молдавский (по умолчанию используем русский)
        }

        # Если язык Telegram не найден в маппинге, используем язык по умолчанию
        return language_mapping.get(telegram_language_code.lower(), self._default_language)
