"""
Интерфейс для парсера объявлений.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

from src.domain.value_objects.ad_id import AdId


class IParser(ABC):
    """
    Интерфейс для парсера объявлений.
    Определяет методы для получения списка ID и деталей объявления.
    """

    @abstractmethod
    def get_ad_ids(self, category: str, limit: Optional[int] = None, chunk_size: Optional[int] = None) -> List[AdId]:
        """
        Получает список ID объявлений для указанной категории.

        Args:
            category: Категория объявлений
            limit: Максимальное количество ID для получения (опционально)
            chunk_size: Размер чанка для групповой обработки (опционально)

        Returns:
            List[AdId]: Список ID объявлений
        """
        pass

    @abstractmethod
    def get_ad_details(self, ad_id: AdId) -> Optional[Dict]:
        """
        Получает детали объявления по его ID.

        Args:
            ad_id: ID объявления

        Returns:
            Optional[Dict]: Словарь с данными объявления или None, если объявление не найдено
        """
        pass
