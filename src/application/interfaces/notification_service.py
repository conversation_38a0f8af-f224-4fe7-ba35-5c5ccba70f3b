"""
Интерфейс для сервиса уведомлений.
"""
from abc import ABC, abstractmethod
from typing import Optional, Union

from src.domain.entities.property import Property


class INotificationService(ABC):
    """
    Интерфейс для сервиса уведомлений.
    Определяет методы для отправки уведомлений о новых объявлениях.
    """

    @abstractmethod
    async def notify_new_property(self, property_obj: Property, chat_ids: Union[list[str], list[int]], category: Optional[str] = None) -> bool:
        """
        Отправляет уведомление о новом объекте недвижимости.

        Args:
            property_obj: Объект недвижимости
            chat_ids: Список ID чатов для отправки
            category: Категория недвижимости (опционально)

        Returns:
            bool: True, если уведомление успешно отправлено, иначе False
        """
        pass

    @abstractmethod
    async def notify_error(self, error_message: str, chat_ids: Union[list[str], list[int]]) -> bool:
        """
        Отправляет уведомление об ошибке.

        Args:
            error_message: Сообщение об ошибке
            chat_ids: Список ID чатов для отправки

        Returns:
            bool: True, если уведомление успешно отправлено, иначе False
        """
        pass

    @abstractmethod
    async def notify_parser_error(self, error_message: str, error_chat_id: Union[str, int]) -> bool:
        """
        Отправляет уведомление об ошибке парсера в специальный чат.

        Args:
            error_message: Сообщение об ошибке
            error_chat_id: ID чата для отправки уведомлений об ошибках

        Returns:
            bool: True, если уведомление успешно отправлено, иначе False
        """
        pass
