"""
Интерфейс для хранилища обработанных ID объявлений.
"""
from abc import ABC, abstractmethod
from typing import List

from src.domain.value_objects.ad_id import AdId


class IProcessedAdStorage(ABC):
    """
    Интерфейс для хранилища обработанных ID объявлений.
    Определяет методы для добавления и проверки существования ID.
    """

    @abstractmethod
    def add(self, ad_id: AdId) -> None:
        """
        Добавляет ID объявления в хранилище обработанных.

        Args:
            ad_id: ID объявления для добавления
        """
        pass

    @abstractmethod
    def exists(self, ad_id: AdId) -> bool:
        """
        Проверяет, существует ли ID объявления в хранилище обработанных.

        Args:
            ad_id: ID объявления для проверки

        Returns:
            bool: True, если ID уже обработан, иначе False
        """
        pass

    @abstractmethod
    def get_all(self) -> List[AdId]:
        """
        Возвращает список всех обработанных ID объявлений.

        Returns:
            List[AdId]: Список обработанных ID
        """
        pass

    @abstractmethod
    def remove_old_ads(self, max_storage_days: int = 30) -> int:
        """
        Удаляет старые записи об обработанных объявлениях.

        Args:
            max_storage_days: Максимальное время хранения записей в днях (по умолчанию 30)

        Returns:
            int: Количество удаленных записей
        """
        pass
