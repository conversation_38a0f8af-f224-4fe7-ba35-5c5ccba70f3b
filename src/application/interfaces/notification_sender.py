"""
Интерфейс для отправки уведомлений.
"""
from abc import ABC, abstractmethod
from typing import Optional, Union
from typing_extensions import TypedDict


class InlineKeyboardButton(TypedDict, total=False):
    """Представляет кнопку встроенной клавиатуры Telegram."""
    text: str
    url: Optional[str]
    callback_data: Optional[str]


class InlineKeyboardMarkup(TypedDict):
    """Представляет встроенную клавиатуру Telegram."""
    inline_keyboard: list[list[InlineKeyboardButton]]


class INotificationSender(ABC):
    """
    Интерфейс для отправки уведомлений.
    Определяет методы для отправки уведомлений.
    """

    @abstractmethod
    async def send(self, message: str, chat_id: Union[str, int], parse_mode: Optional[str] = None,
             disable_web_page_preview: bool = False, reply_markup: Optional[Union[InlineKeyboardMarkup, dict[str, list[list[dict[str, str]]]]]] = None) -> bool:
        """
        Отправляет текстовое сообщение.

        Args:
            message: Текст сообщения
            chat_id: ID чата для отправки
            parse_mode: Режим форматирования текста (опционально)
            disable_web_page_preview: Отключить предпросмотр ссылок (опционально)
            reply_markup: Разметка для интерактивных кнопок (опционально)

        Returns:
            bool: True, если сообщение успешно отправлено, иначе False
        """
        pass

    @abstractmethod
    async def send_with_image(self, message: str, image_url: str, chat_id: Union[str, int],
                        parse_mode: Optional[str] = None, reply_markup: Optional[Union[InlineKeyboardMarkup, dict[str, list[list[dict[str, str]]]]]] = None) -> bool:
        """
        Отправляет сообщение с изображением.

        Args:
            message: Текст сообщения
            image_url: URL изображения
            chat_id: ID чата для отправки
            parse_mode: Режим форматирования текста (опционально)
            reply_markup: Разметка для интерактивных кнопок (опционально)

        Returns:
            bool: True, если сообщение успешно отправлено, иначе False
        """
        pass

    @abstractmethod
    async def send_with_images(self, message: str, image_urls: list[str], chat_id: Union[str, int],
                         parse_mode: Optional[str] = None, reply_markup: Optional[Union[InlineKeyboardMarkup, dict[str, list[list[dict[str, str]]]]]] = None) -> bool:
        """
        Отправляет сообщение с несколькими изображениями.

        Args:
            message: Текст сообщения
            image_urls: Список URL изображений
            chat_id: ID чата для отправки
            parse_mode: Режим форматирования текста (опционально)
            reply_markup: Разметка для интерактивных кнопок (опционально)

        Returns:
            bool: True, если сообщение успешно отправлено, иначе False
        """
        pass

    @abstractmethod
    async def send_error(self, message: str, error_chat_id: Union[str, int], parse_mode: Optional[str] = None,
                  disable_web_page_preview: bool = False, reply_markup: Optional[Union[InlineKeyboardMarkup, dict[str, list[list[dict[str, str]]]]]] = None) -> bool:
        """
        Отправляет уведомление об ошибке в специальный чат.

        Args:
            message: Текст сообщения об ошибке
            error_chat_id: ID чата для отправки уведомлений об ошибках
            parse_mode: Режим форматирования текста (опционально)
            disable_web_page_preview: Отключить предпросмотр ссылок (опционально)
            reply_markup: Разметка для интерактивных кнопок (опционально)

        Returns:
            bool: True, если сообщение успешно отправлено, иначе False
        """
        pass
