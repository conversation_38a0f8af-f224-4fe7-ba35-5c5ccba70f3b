"""
Пример использования локализации в обработчиках команд.
"""
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup

from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.presentation.telegram.menu import create_reply_keyboard, create_keyboard_button, create_inline_keyboard, create_inline_button


class ExampleHandler:
    """
    Пример обработчика команд с использованием локализации.
    """

    def __init__(self, localization_service: LocalizationService, user_language_service: UserLanguageService, bot=None):
        """
        Инициализирует обработчик команд.

        Args:
            localization_service: Сервис локализации
            user_language_service: Сервис управления языком пользователя
            bot: Объект бота (опционально)
        """
        self._localization_service = localization_service
        self._user_language_service = user_language_service
        self._bot = bot

    def get_main_menu(self, user_id: int) -> ReplyKeyboardMarkup:
        """
        Создает главное меню с локализованными текстами.

        Args:
            user_id: ID пользователя

        Returns:
            ReplyKeyboardMarkup: Клавиатура с кнопками главного меню
        """
        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Создаем клавиатуру с локализованными кнопками
        keyboard = [
            [
                create_keyboard_button(self._localization_service.get_text('main_menu_status', language)),
                create_keyboard_button(self._localization_service.get_text('main_menu_notifications', language))
            ],
            [
                create_keyboard_button(self._localization_service.get_text('main_menu_categories', language)),
                create_keyboard_button(self._localization_service.get_text('main_menu_help', language))
            ]
        ]

        return create_reply_keyboard(keyboard)

    def get_help_menu(self, user_id: int) -> InlineKeyboardMarkup:
        """
        Создает меню помощи с локализованными текстами.

        Args:
            user_id: ID пользователя

        Returns:
            InlineKeyboardMarkup: Клавиатура с кнопками помощи
        """
        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Создаем клавиатуру с локализованными кнопками
        keyboard = [
            [
                create_inline_button(self._localization_service.get_text('help_commands', language), "help_commands"),
                create_inline_button(self._localization_service.get_text('help_categories', language), "help_categories")
            ],
            [
                create_inline_button(self._localization_service.get_text('help_about', language), "help_about")
            ]
        ]

        return create_inline_keyboard(keyboard)

    def get_categories_menu(self, user_id: int) -> InlineKeyboardMarkup:
        """
        Создает меню категорий с локализованными текстами.

        Args:
            user_id: ID пользователя

        Returns:
            InlineKeyboardMarkup: Клавиатура с кнопками категорий
        """
        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Создаем клавиатуру с локализованными кнопками
        keyboard = [
            [
                create_inline_button(self._localization_service.get_text('category_apartments', language), "category_apartments"),
                create_inline_button(self._localization_service.get_text('category_houses', language), "category_houses")
            ],
            [
                create_inline_button(self._localization_service.get_text('category_rooms', language), "category_rooms"),
                create_inline_button(self._localization_service.get_text('category_garages', language), "category_garages")
            ],
            [
                create_inline_button(self._localization_service.get_text('category_dacha', language), "category_dacha"),
                create_inline_button(self._localization_service.get_text('category_land', language), "category_land")
            ],
            [
                create_inline_button(self._localization_service.get_text('category_commercial', language), "category_commercial")
            ]
        ]

        return create_inline_keyboard(keyboard)

    async def handle_start_command(self, update: Update, context=None) -> None:
        """
        Обрабатывает команду /start.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await message.reply_text("Не удалось определить пользователя.")
            return

        user_id = message.from_user.id
        user_name = message.from_user.first_name

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем локализованный шаблон
        template = self._localization_service.get_template('command_start', language)

        # Форматируем сообщение
        text = self._localization_service.format_template(template, {'name': user_name})

        # Отправляем сообщение с локализованным меню
        await message.reply_text(
            text,
            reply_markup=self.get_main_menu(user_id)
        )

    async def handle_help_command(self, update: Update, context=None) -> None:
        """
        Обрабатывает команду /help.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await message.reply_text("Не удалось определить пользователя.")
            return

        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем локализованный текст
        text = self._localization_service.get_text('help_menu_title', language)

        # Отправляем сообщение с локализованным меню
        await message.reply_text(
            text,
            reply_markup=self.get_help_menu(user_id)
        )

    async def handle_categories_command(self, update: Update, context=None) -> None:
        """
        Обрабатывает команду /categories.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await message.reply_text("Не удалось определить пользователя.")
            return

        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем локализованный текст
        text = self._localization_service.get_text('categories_menu_title', language)

        # Отправляем сообщение с локализованным меню
        await message.reply_text(
            text,
            reply_markup=self.get_categories_menu(user_id)
        )

    async def handle_status_command(self, update: Update, context=None) -> None:
        """
        Обрабатывает команду /status.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await message.reply_text("Не удалось определить пользователя.")
            return

        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем категории пользователя
        categories = []
        if self._bot:
            categories = self._bot.get_categories(user_id)

        # Формируем информацию о категориях
        if categories:
            categories_info = self._localization_service.get_text('categories_active', language)

            for category in categories:
                # Получаем локализованное описание категории
                category_parts = category.split('_')
                if len(category_parts) == 2:
                    category_type, operation = category_parts
                    category_name = self._localization_service.get_text(f'category_{category_type}', language)
                    operation_name = self._localization_service.get_text(f'category_{operation}', language)
                    description = f"{operation_name} {category_name}"
                else:
                    description = category

                categories_info += f"\n- {description}"
        else:
            categories_info = self._localization_service.get_text('categories_empty', language)

        # Получаем локализованный шаблон
        template = self._localization_service.get_template('command_status', language)

        # Форматируем сообщение
        text = self._localization_service.format_template(template, {
            'status': self._localization_service.get_text('status_running', language),
            'categories_info': categories_info
        })

        # Отправляем сообщение с локализованным меню
        await message.reply_text(
            text,
            reply_markup=self.get_main_menu(user_id)
        )
