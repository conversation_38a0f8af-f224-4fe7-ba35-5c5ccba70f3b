# Локализация Telegram бота

Модуль локализации предоставляет возможность перевода интерфейса Telegram бота на разные языки в соответствии с принципами чистой архитектуры.

## Структура модуля

### Доменный слой (Domain Layer)
- `src/domain/interfaces/localization_service.py` - Интерфейс сервиса локализации
- `src/domain/value_objects/localization.py` - Модели для языков и локализованных строк

### Слой приложения (Application Layer)
- `src/application/services/localization.py` - Сервис локализации
- `src/application/services/user_language.py` - Сервис для управления языком пользователя

### Слой инфраструктуры (Infrastructure Layer)
- `src/infrastructure/localization/yaml_localization_service.py` - Реализация сервиса локализации на основе YAML-файлов
- `src/infrastructure/persistence/user_language_storage.py` - Хранилище для языковых настроек пользователей
- `config/localization/ru.yaml` - Файл с локализованными строками на русском языке
- `config/localization/ua.yaml` - Файл с локализованными строками на украинском языке

### Слой представления (Presentation Layer)
- `src/presentation/telegram/localization/factory.py` - Фабрика для создания сервисов локализации
- `src/presentation/telegram/localization/language_handler.py` - Обработчик команды для выбора языка
- `src/presentation/telegram/localization/register.py` - Регистрация обработчиков команд для локализации
- `src/presentation/telegram/localization/example.py` - Пример использования локализации в обработчиках команд

## Использование

### Инициализация сервисов локализации

```python
from src.presentation.telegram.localization.factory import create_localization_services

def main():
    # Создание сервисов локализации
    localization_service, user_language_service = create_localization_services()

    # Создание приложения
    application = Application.builder().token(token).build()

    # Регистрация обработчиков локализации
    from src.presentation.telegram.localization.register import register_language_handlers
    register_language_handlers(application, localization_service, user_language_service)

    # Запуск бота
    application.run_polling()
```

### Использование сервисов локализации в обработчиках команд

```python
from telegram import Update
from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService

class CommandHandler:
    def __init__(self, localization_service: LocalizationService, user_language_service: UserLanguageService):
        self._localization_service = localization_service
        self._user_language_service = user_language_service

    async def handle_start_command(self, update: Update, context=None):
        # Получаем ID пользователя
        user_id = update.message.from_user.id
        user_name = update.message.from_user.first_name

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем локализованный шаблон
        template = self._localization_service.get_template('command_start', language)

        # Форматируем сообщение
        text = self._localization_service.format_template(template, {'name': user_name})

        # Отправляем сообщение
        await update.message.reply_text(text)
```

## Добавление новых языков

Для добавления нового языка необходимо:

1. Создать файл с локализованными строками в директории `config/localization/` (например, `en.yaml`)
2. Добавить язык в список доступных языков в файле `src/presentation/telegram/localization/factory.py`:

```python
# Создаем список доступных языков
available_languages = [
    Language(code='ru', name='Русский', flag='🇷🇺'),
    Language(code='ua', name='Українська', flag='🇺🇦'),
    Language(code='en', name='English', flag='🇬🇧')  # Добавляем новый язык
]
```

## Добавление новых текстов и шаблонов

Для добавления новых текстов и шаблонов необходимо добавить их в соответствующие разделы файлов локализации:

```yaml
texts:
  # Добавляем новый текст
  new_text_key: "Новый текст"

templates:
  # Добавляем новый шаблон
  new_template_key: |
    Шаблон с {placeholder}
```

## Преимущества данной реализации

1. **Соответствие принципам чистой архитектуры**:
   - Разделение на слои (доменный, приложения, инфраструктуры, представления)
   - Использование интерфейсов и абстракций
   - Инверсия зависимостей

2. **Гибкость и расширяемость**:
   - Легко добавлять новые языки
   - Легко заменять источник локализованных строк (YAML, JSON, база данных и т.д.)
   - Легко заменять хранилище языковых настроек пользователей

3. **Тестируемость**:
   - Возможность тестировать каждый компонент независимо
   - Возможность создавать моки для тестирования

4. **Удобство использования**:
   - Простой API для получения локализованных текстов и шаблонов
   - Автоматическое форматирование шаблонов
   - Поддержка выбора языка пользователем
