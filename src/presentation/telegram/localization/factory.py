"""
Фабрика для создания сервисов локализации.
"""
import json
import logging
import os
from typing import Dict, List, Tuple

from src.domain.value_objects.localization import Language
from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.infrastructure.localization.yaml_localization_service import YamlLocalizationService
from src.infrastructure.persistence.user_language_storage import UserLanguageStorage
from src.presentation.telegram.localization.languages import AVAILABLE_LANGUAGES, LANGUAGE_NAMES
from src.presentation.telegram.localization.notification_templates import NotificationTemplates


def create_localization_services(config_dir: str = 'config') -> Tuple[LocalizationService, UserLanguageService]:
    """
    Создает сервисы локализации.

    Args:
        config_dir: Путь к директории с конфигурацией

    Returns:
        Tu<PERSON>[LocalizationService, UserLanguageService]: Кортеж из сервиса локализации и сервиса управления языком пользователя
    """
    # Определяем пути к файлам
    localization_dir = os.path.join(config_dir, 'localization')

    # Используем путь для файла с языковыми настройками пользователей из переменной окружения
    # или используем путь по умолчанию
    default_docker_path = '/app/data/user_languages.json'
    default_local_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'data', 'user_languages.json')

    # Сначала пробуем использовать переменную окружения
    user_language_file = os.environ.get('USER_LANGUAGE_FILE')

    # Если переменная окружения не задана, пробуем использовать Docker-путь
    if not user_language_file:
        # Проверяем, работаем ли мы в Docker
        in_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_CONTAINER') == 'true'

        if in_docker:
            user_language_file = default_docker_path
        else:
            user_language_file = default_local_path

    # Создаем директорию для файла, если она не существует
    try:
        os.makedirs(os.path.dirname(user_language_file), exist_ok=True)

        # Проверяем, можем ли мы записать в файл
        try:
            # Пробуем открыть файл для записи
            with open(user_language_file, 'a'):
                pass
        except (IOError, PermissionError):
            # Если не можем записать в файл, используем временный файл
            import logging
            logger = logging.getLogger("telegram_bot")
            logger.error(f"Нет прав на запись в файл с языковыми настройками: {user_language_file}")
            logger.info(f"Используем временный файл для языковых настроек")
            import tempfile
            user_language_file = os.path.join(tempfile.gettempdir(), 'user_languages.json')

    except Exception as e:
        # Если не удалось создать директорию, используем временный файл
        import logging
        logger = logging.getLogger("telegram_bot")
        logger.error(f"Не удалось создать директорию для файла с языковыми настройками: {e}")
        logger.info(f"Используем временный файл для языковых настроек")
        import tempfile
        user_language_file = os.path.join(tempfile.gettempdir(), 'user_languages.json')

    # Создаем список доступных языков
    available_languages = [
        Language(code='ru', name='Русский', flag='🇷🇺'),
        Language(code='ua', name='Українська', flag='🇺🇦')
    ]

    # Создаем сервисы
    localization_service = YamlLocalizationService(localization_dir)
    user_language_storage = UserLanguageStorage(user_language_file)

    # Создаем сервисы приложения
    localization_app_service = LocalizationService(localization_service)
    user_language_app_service = UserLanguageService(
        user_language_storage,
        available_languages
    )

    return localization_app_service, user_language_app_service


def create_notification_templates(localization_service: LocalizationService, user_language_service: UserLanguageService) -> NotificationTemplates:
    """
    Создает экземпляр класса для работы с локализованными шаблонами уведомлений.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя

    Returns:
        NotificationTemplates: Экземпляр класса для работы с локализованными шаблонами уведомлений
    """
    return NotificationTemplates(localization_service, user_language_service)
