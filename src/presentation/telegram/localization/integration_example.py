"""
Пример интеграции локализации в бота.
"""
from telegram.ext import Application, CommandHandler

from src.presentation.telegram.localization.factory import create_localization_services
from src.presentation.telegram.localization.register import register_language_handlers
from src.presentation.telegram.localization.example import ExampleHandler


def main():
    """
    Пример интеграции локализации в бота.
    """
    # Создание сервисов локализации
    localization_service, user_language_service = create_localization_services()
    
    # Создание обработчика команд
    example_handler = ExampleHandler(localization_service, user_language_service)
    
    # Создание приложения
    application = Application.builder().token("YOUR_BOT_TOKEN").build()
    
    # Регистрация обработчиков локализации
    register_language_handlers(application, localization_service, user_language_service)
    
    # Регистрация обработчиков команд
    application.add_handler(CommandHandler("start", example_handler.handle_start_command))
    application.add_handler(CommandHandler("help", example_handler.handle_help_command))
    application.add_handler(CommandHandler("categories", example_handler.handle_categories_command))
    application.add_handler(CommandHandler("status", example_handler.handle_status_command))
    
    # Запуск бота
    application.run_polling()


if __name__ == "__main__":
    main()
