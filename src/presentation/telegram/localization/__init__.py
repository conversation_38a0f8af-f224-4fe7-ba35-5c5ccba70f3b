"""
Пакет для локализации текстов и шаблонов Telegram бота.
"""
from src.presentation.telegram.localization.factory import create_localization_services, create_notification_templates
from src.presentation.telegram.localization.languages import AVAILABLE_LANGUAGES, LANGUAGE_NAMES
from src.presentation.telegram.localization.menu import (
    get_localized_main_menu, get_localized_notifications_menu, get_localized_categories_menu,
    get_localized_category_type_menu, get_localized_category_action_menu, get_localized_help_menu,
    get_localized_admin_menu, get_localized_broadcast_menu, get_localized_users_menu,
    get_localized_requests_menu, get_localized_notification_settings_menu,
    get_localized_language_menu
)
from src.presentation.telegram.localization.notification_factory import create_localized_notification_service
from src.presentation.telegram.localization.notification_templates import NotificationTemplates
from src.presentation.telegram.localization.register import register_language_handlers

__all__ = [
    'create_localization_services',
    'create_notification_templates',
    'create_localized_notification_service',
    'register_language_handlers',
    'NotificationTemplates',
    'AVAILABLE_LANGUAGES',
    'LANGUAGE_NAMES',
    'get_localized_main_menu',
    'get_localized_notifications_menu',
    'get_localized_categories_menu',
    'get_localized_category_type_menu',
    'get_localized_category_action_menu',
    'get_localized_help_menu',
    'get_localized_admin_menu',
    'get_localized_broadcast_menu',
    'get_localized_users_menu',
    'get_localized_requests_menu',
    'get_localized_notification_settings_menu',
    'get_localized_language_menu'
]
