"""
Модуль для создания локализованных меню и кнопок Telegram бота.
"""
import logging
from typing import List, Optional

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton

from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.presentation.telegram.menu import (
    create_keyboard_button, create_inline_button,
    create_reply_keyboard, create_inline_keyboard
)


def get_localized_main_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> ReplyKeyboardMarkup:
    """
    Создает локализованное главное меню бота.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        ReplyKeyboardMarkup: Клавиатура с локализованными кнопками главного меню
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    status_text = localization_service.get_text('main_menu_status', language) or "📊 Статус"
    notifications_text = localization_service.get_text('main_menu_notifications', language) or "🔔 Уведомления"
    categories_text = localization_service.get_text('main_menu_categories', language) or "🏠 Категории"
    help_text = localization_service.get_text('main_menu_help', language) or "❓ Помощь"

    # Получаем текст для кнопки выбора языка
    language_text = localization_service.get_text('main_menu_language', language) or "🌐 Язык"

    # Добавляем индикацию текущего языка
    if language == 'ru':
        language_text += " 🇷🇺"
    elif language == 'ua':
        language_text += " 🇺🇦"

    # Создаем клавиатуру
    keyboard = [
        [create_keyboard_button(status_text), create_keyboard_button(notifications_text)],
        [create_keyboard_button(categories_text), create_keyboard_button(help_text)],
        [create_keyboard_button(language_text)]
    ]

    return create_reply_keyboard(keyboard)


def get_localized_notifications_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> ReplyKeyboardMarkup:
    """
    Создает локализованное меню управления уведомлениями.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        ReplyKeyboardMarkup: Клавиатура с локализованными кнопками меню уведомлений
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    enable_text = localization_service.get_text('notifications_menu_enable', language) or "🔔 Включить уведомления"
    disable_text = localization_service.get_text('notifications_menu_disable', language) or "🔕 Отключить уведомления"
    back_text = localization_service.get_text('notifications_menu_back', language) or "◀️ Назад в главное меню"

    # Создаем клавиатуру
    keyboard = [
        [create_keyboard_button(enable_text), create_keyboard_button(disable_text)],
        [create_keyboard_button(back_text)]
    ]

    return create_reply_keyboard(keyboard)


def get_localized_categories_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню выбора категорий недвижимости.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками категорий
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    apartments_text = localization_service.get_text('category_apartments', language) or "🏢 Квартиры"
    houses_text = localization_service.get_text('category_houses', language) or "🏠 Дома"
    rooms_text = localization_service.get_text('category_rooms', language) or "🚪 Комнаты"
    garages_text = localization_service.get_text('category_garages', language) or "🚗 Гаражи"
    dacha_text = localization_service.get_text('category_dacha', language) or "🏡 Дачи"
    land_text = localization_service.get_text('category_land', language) or "🌳 Земля"
    commercial_text = localization_service.get_text('category_commercial', language) or "🏭 Коммерческая"
    all_text = localization_service.get_text('category_all', language) or "✅ Все категории"
    clear_text = localization_service.get_text('category_clear', language) or "❌ Очистить все"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(apartments_text, "category_apartments"),
            create_inline_button(houses_text, "category_houses")
        ],
        [
            create_inline_button(rooms_text, "category_rooms"),
            create_inline_button(garages_text, "category_garages")
        ],
        [
            create_inline_button(dacha_text, "category_dacha"),
            create_inline_button(land_text, "category_land")
        ],
        [
            create_inline_button(commercial_text, "category_commercial")
        ],
        [
            create_inline_button(all_text, "category_all"),
            create_inline_button(clear_text, "category_clear")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_category_type_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int,
    category_type: str
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню выбора типа операции для категории.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя
        category_type: Тип категории (apartments, houses, rooms, etc.)

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками типов операций
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    sale_text = localization_service.get_text('category_sale', language) or "🛒 Продажа"
    rent_text = localization_service.get_text('category_rent', language) or "💰 Аренда"
    back_text = localization_service.get_text('category_back', language) or "◀️ Назад к категориям"

    # Для категории "land" (Земельные участки) показываем только кнопку "Продажа"
    if category_type == "land":
        keyboard = [
            [
                create_inline_button(sale_text, f"{category_type}_sale")
            ],
            [
                create_inline_button(back_text, "back_to_categories")
            ]
        ]
    else:
        keyboard = [
            [
                create_inline_button(sale_text, f"{category_type}_sale"),
                create_inline_button(rent_text, f"{category_type}_rent")
            ],
            [
                create_inline_button(back_text, "back_to_categories")
            ]
        ]

    return create_inline_keyboard(keyboard)


def get_localized_category_action_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int,
    category_id: str
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню действий для категории.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя
        category_id: ID категории (apartments_sale, houses_rent, etc.)

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками действий
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    subscribe_text = localization_service.get_text('category_subscribe', language) or "➕ Подписаться"
    unsubscribe_text = localization_service.get_text('category_unsubscribe', language) or "➖ Отписаться"
    back_text = localization_service.get_text('category_back_to_types', language) or "◀️ Назад к типам"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(subscribe_text, f"add_{category_id}"),
            create_inline_button(unsubscribe_text, f"remove_{category_id}")
        ],
        [
            create_inline_button(back_text, f"back_to_{category_id.split('_')[0]}")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_help_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню помощи.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками помощи
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    commands_text = localization_service.get_text('help_commands', language) or "📚 Команды"
    categories_text = localization_service.get_text('help_categories', language) or "🏠 Категории"
    about_text = localization_service.get_text('help_about', language) or "❓ О боте"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(commands_text, "help_commands"),
            create_inline_button(categories_text, "help_categories")
        ],
        [
            create_inline_button(about_text, "help_about")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_admin_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> ReplyKeyboardMarkup:
    """
    Создает локализованное меню администратора.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        ReplyKeyboardMarkup: Клавиатура с локализованными кнопками меню администратора
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    users_text = localization_service.get_text('admin_menu_users', language) or "👥 Пользователи"
    requests_text = localization_service.get_text('admin_menu_requests', language) or "📝 Запросы"
    stats_text = localization_service.get_text('admin_menu_stats', language) or "📊 Статистика"
    broadcast_text = localization_service.get_text('admin_menu_broadcast', language) or "📢 Рассылка"
    notification_settings_text = localization_service.get_text('admin_menu_notification_settings', language) or "🔔 Настройки уведомлений"
    back_text = localization_service.get_text('notifications_menu_back', language) or "◀️ Назад в главное меню"

    # Создаем клавиатуру
    keyboard = [
        [create_keyboard_button(users_text), create_keyboard_button(requests_text)],
        [create_keyboard_button(stats_text), create_keyboard_button(broadcast_text)],
        [create_keyboard_button(notification_settings_text), create_keyboard_button(back_text)]
    ]

    return create_reply_keyboard(keyboard)


def get_localized_broadcast_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню выбора группы для рассылки.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками выбора группы для рассылки
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    all_text = localization_service.get_text('broadcast_all', language) or "👥 Всем пользователям"
    users_text = localization_service.get_text('broadcast_users', language) or "👤 Только пользователям"
    admins_text = localization_service.get_text('broadcast_admins', language) or "👑 Только администраторам"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(all_text, "broadcast_all"),
        ],
        [
            create_inline_button(users_text, "broadcast_users"),
            create_inline_button(admins_text, "broadcast_admins")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_users_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню управления пользователями.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками управления пользователями
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    list_text = localization_service.get_text('users_menu_list', language) or "📋 Список пользователей"
    blocked_text = localization_service.get_text('users_menu_blocked', language) or "🔒 Заблокированные"
    add_text = localization_service.get_text('users_menu_add', language) or "➕ Добавить пользователя"
    remove_text = localization_service.get_text('users_menu_remove', language) or "➖ Удалить пользователя"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(list_text, "admin_users_list"),
            create_inline_button(blocked_text, "admin_users_blocked")
        ],
        [
            create_inline_button(add_text, "admin_users_add"),
            create_inline_button(remove_text, "admin_users_remove")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_requests_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню управления запросами на доступ.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками управления запросами
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    pending_text = localization_service.get_text('requests_menu_pending', language) or "📋 Ожидающие запросы"
    approved_text = localization_service.get_text('requests_menu_approved', language) or "✅ Одобренные запросы"
    rejected_text = localization_service.get_text('requests_menu_rejected', language) or "❌ Отклоненные запросы"
    refresh_text = localization_service.get_text('requests_menu_refresh', language) or "🔄 Обновить"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(pending_text, "admin_requests_pending"),
            create_inline_button(approved_text, "admin_requests_approved")
        ],
        [
            create_inline_button(rejected_text, "admin_requests_rejected"),
            create_inline_button(refresh_text, "admin_requests_refresh")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_notification_settings_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню настроек уведомлений.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками подписки/отписки от уведомлений
    """
    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    subscribe_text = localization_service.get_text('notification_subscribe', language) or "🔔 Подписаться на запросы"
    unsubscribe_text = localization_service.get_text('notification_unsubscribe', language) or "🔕 Отписаться от запросов"

    # Создаем клавиатуру
    keyboard = [
        [
            create_inline_button(subscribe_text, "admin_subscribe_requests"),
            create_inline_button(unsubscribe_text, "admin_unsubscribe_requests")
        ]
    ]

    return create_inline_keyboard(keyboard)


def get_localized_language_menu(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> InlineKeyboardMarkup:
    """
    Создает локализованное меню выбора языка.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup: Клавиатура с локализованными кнопками выбора языка
    """
    # Получаем логгер
    logger = logging.getLogger(__name__)

    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)
    logger.info(f"DEBUG: get_localized_language_menu - Язык пользователя {user_id}: {language}")

    # Получаем локализованные тексты
    ru_text = localization_service.get_text('language_ru', language) or "🇷🇺 Русский"
    ua_text = localization_service.get_text('language_ua', language) or "🇺🇦 Українська"

    logger.info(f"DEBUG: get_localized_language_menu - Локализованные тексты: ru_text='{ru_text}', ua_text='{ua_text}'")



    # Добавляем отметку к текущему языку
    if language == 'ru':
        ru_text += " ✓"
    elif language == 'ua':
        ua_text += " ✓"

    logger.info(f"DEBUG: get_localized_language_menu - Тексты с отметками: ru_text='{ru_text}', ua_text='{ua_text}'")

    # Создаем клавиатуру с кнопками выбора языка
    keyboard = [
        [
            create_inline_button(ru_text, "language_ru"),
            create_inline_button(ua_text, "language_ua")
        ]
    ]

    logger.info(f"DEBUG: get_localized_language_menu - Создана клавиатура: {keyboard}")

    return create_inline_keyboard(keyboard)


def get_localized_language_keyboard(
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    user_id: int
) -> ReplyKeyboardMarkup:
    """
    Создает локализованное меню выбора языка с обычными кнопками.

    Args:
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        user_id: ID пользователя

    Returns:
        ReplyKeyboardMarkup: Клавиатура с локализованными кнопками выбора языка
    """
    # Получаем логгер
    logger = logging.getLogger(__name__)

    # Получаем язык пользователя
    language = user_language_service.get_language(user_id)
    logger.info(f"DEBUG: get_localized_language_keyboard - Язык пользователя {user_id}: {language}")

    # Получаем локализованные тексты
    ru_text = localization_service.get_text('language_ru', language) or "🇷🇺 Русский"
    ua_text = localization_service.get_text('language_ua', language) or "🇺🇦 Українська"
    back_text = localization_service.get_text('notifications_menu_back', language) or "◀️ Назад в главное меню"

    logger.info(f"DEBUG: get_localized_language_keyboard - Локализованные тексты: ru_text='{ru_text}', ua_text='{ua_text}'")

    # Добавляем отметку к текущему языку
    if language == 'ru':
        ru_text += " ✓"
    elif language == 'ua':
        ua_text += " ✓"

    logger.info(f"DEBUG: get_localized_language_keyboard - Тексты с отметками: ru_text='{ru_text}', ua_text='{ua_text}'")

    # Создаем клавиатуру с кнопками выбора языка
    keyboard = [
        [create_keyboard_button(ru_text), create_keyboard_button(ua_text)],
        [create_keyboard_button(back_text)]
    ]

    logger.info(f"DEBUG: get_localized_language_keyboard - Создана клавиатура: {keyboard}")

    return create_reply_keyboard(keyboard)