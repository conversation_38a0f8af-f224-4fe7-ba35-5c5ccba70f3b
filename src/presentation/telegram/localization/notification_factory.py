"""
Фабрика для создания локализованного сервиса уведомлений.
"""
import logging
from typing import Optional

from src.application.interfaces.notification_sender import INotificationSender
from src.application.services.localization import LocalizationService
from src.application.services.localized_notification import LocalizedNotificationService
from src.application.services.user_language import UserLanguageService
from src.presentation.telegram.localization.factory import create_notification_templates


def create_localized_notification_service(
    notification_sender: INotificationSender,
    localization_service: LocalizationService,
    user_language_service: UserLanguageService,
    webhook_sender: Optional[INotificationSender] = None,
    logger: Optional[logging.Logger] = None
) -> LocalizedNotificationService:
    """
    Создает локализованный сервис уведомлений.

    Args:
        notification_sender: Отправитель уведомлений
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
        webhook_sender: Отправитель уведомлений через вебхук (опционально)
        logger: Логгер (опционально)

    Returns:
        LocalizedNotificationService: Локализованный сервис уведомлений
    """
    # Создаем шаблоны уведомлений
    notification_templates = create_notification_templates(localization_service, user_language_service)

    # Создаем локализованный сервис уведомлений
    return LocalizedNotificationService(
        notification_sender=notification_sender,
        notification_templates=notification_templates,
        webhook_sender=webhook_sender,
        logger=logger,
        user_language_service=user_language_service,
        localization_service=localization_service
    )
