"""
Локализованные шаблоны уведомлений.
"""
import logging
from typing import Optional

from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.domain.entities.property import Property


class NotificationTemplates:
    """
    Класс для работы с локализованными шаблонами уведомлений.
    """

    def __init__(self, localization_service: LocalizationService, user_language_service: UserLanguageService):
        """
        Инициализирует класс для работы с локализованными шаблонами уведомлений.

        Args:
            localization_service: Сервис локализации
            user_language_service: Сервис управления языком пользователя
        """
        self._localization_service = localization_service
        self._user_language_service = user_language_service
        self._logger = logging.getLogger(__name__)

    def get_new_property_template(self, user_id: int) -> str:
        """
        Возвращает шаблон уведомления о новом объявлении.

        Args:
            user_id: ID пользователя

        Returns:
            str: Шаблон уведомления
        """
        # Принудительно перезагружаем языковые настройки пользователей
        if hasattr(self._user_language_service, '_user_language_storage') and hasattr(self._user_language_service._user_language_storage, '_load'):
            try:
                self._user_language_service._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены в get_new_property_template для пользователя {user_id}")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Проверяем, что язык получен корректно
        if not language:
            self._logger.warning(f"Язык пользователя {user_id} не определен, используем язык по умолчанию (ru)")
            language = 'ru'

        self._logger.info(f"Получение шаблона уведомления для пользователя {user_id} на языке: {language}")

        # Получаем шаблон для указанного языка
        template = self._localization_service.get_template('notification_new_property', language)

        # Если шаблон не найден, используем стандартный шаблон
        if not template:
            self._logger.warning(f"Шаблон уведомления для языка {language} не найден, используем стандартный шаблон")
            return "{property_info}"

        self._logger.info(f"Получен шаблон уведомления для языка {language}")
        return template



    def get_status_template(self, user_id: int) -> str:
        """
        Возвращает шаблон уведомления о статусе парсинга.

        Args:
            user_id: ID пользователя

        Returns:
            str: Шаблон уведомления
        """
        # Принудительно перезагружаем языковые настройки пользователей
        if hasattr(self._user_language_service, '_user_language_storage') and hasattr(self._user_language_service._user_language_storage, '_load'):
            try:
                self._user_language_service._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены в get_status_template для пользователя {user_id}")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Проверяем, что язык получен корректно
        if not language:
            self._logger.warning(f"Язык пользователя {user_id} не определен, используем язык по умолчанию (ru)")
            language = 'ru'

        self._logger.info(f"Получение шаблона уведомления о статусе для пользователя {user_id} на языке: {language}")

        # Получаем шаблон для указанного языка
        template = self._localization_service.get_template('notification_status', language)

        # Если шаблон не найден, используем стандартный шаблон
        if not template:
            self._logger.warning(f"Шаблон уведомления о статусе для языка {language} не найден, используем стандартный шаблон")
            # Используем стандартный шаблон в зависимости от языка
            if language == 'ua':
                return """
<b>📊 Статус парсингу</b>

Категорія: <b>{category}</b>
Нових оголошень: {new_count}
Оброблено: {processed_count}
Помилок: {error_count}

Час виконання: {execution_time}
"""
            else:
                return """
<b>📊 Статус парсинга</b>

Категория: <b>{category}</b>
Новых объявлений: {new_count}
Обработано: {processed_count}
Ошибок: {error_count}

Время выполнения: {execution_time}
"""

        self._logger.info(f"Получен шаблон уведомления о статусе для языка {language}")
        return template

    def format_new_property_notification(self, property_obj: Property, user_id: int, category: Optional[str] = None) -> str:
        """
        Форматирует уведомление о новом объявлении.

        Args:
            property_obj: Объект недвижимости
            user_id: ID пользователя
            category: Категория недвижимости (опционально)

        Returns:
            str: Отформатированное уведомление
        """
        from src.presentation.telegram.messages.formatters import format_property_by_category, format_notification_new_property

        # Принудительно перезагружаем языковые настройки пользователей
        if hasattr(self._user_language_service, '_user_language_storage') and hasattr(self._user_language_service._user_language_storage, '_load'):
            try:
                self._user_language_service._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены в NotificationTemplates для пользователя {user_id}")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Проверяем, что язык получен корректно
        if not language:
            self._logger.warning(f"Язык пользователя {user_id} не определен, используем язык по умолчанию (ru)")
            language = 'ru'

        self._logger.info(f"Форматирование уведомления для пользователя {user_id} на языке: {language}")

        # Получаем шаблон уведомления с учетом языка пользователя
        template = self.get_new_property_template(user_id)
        self._logger.info(f"Получен шаблон уведомления для языка {language}")

        # Если категория не указана, используем apartments_sale по умолчанию
        if not category:
            category = 'apartments_sale'
            self._logger.info(f"Категория не указана, используем категорию по умолчанию: {category}")

        # Форматируем информацию о недвижимости с учетом языка пользователя
        try:
            property_info = format_property_by_category(property_obj, category, short=True, language=language)
            self._logger.info(f"Успешно отформатирована информация о недвижимости для категории {category} на языке {language}")
        except Exception as e:
            self._logger.error(f"Ошибка при форматировании информации о недвижимости: {e}")
            # В случае ошибки пробуем использовать русский язык
            try:
                property_info = format_property_by_category(property_obj, category, short=True, language='ru')
                self._logger.info(f"Использован русский язык для форматирования после ошибки")
            except Exception as e2:
                self._logger.error(f"Критическая ошибка при форматировании информации о недвижимости: {e2}")
                # В случае критической ошибки возвращаем базовую информацию
                return f"<b>Новое объявление:</b>\n\n{property_obj.title}\n\nЦена: {property_obj.price}\n\n<a href=\"{property_obj.url}\">Подробнее на OLX</a>"

        # Форматируем уведомление
        try:
            result = template.format(property_info=property_info)
            self._logger.info(f"Успешно отформатировано уведомление")
        except Exception as e:
            self._logger.error(f"Ошибка при форматировании уведомления: {e}")
            # В случае ошибки возвращаем просто информацию о недвижимости
            return property_info

        return result



    def format_status_notification(self, category: str, new_count: int, processed_count: int, error_count: int, execution_time: str, user_id: int) -> str:
        """
        Форматирует уведомление о статусе парсинга.

        Args:
            category: Категория
            new_count: Количество новых объявлений
            processed_count: Количество обработанных объявлений
            error_count: Количество ошибок
            execution_time: Время выполнения
            user_id: ID пользователя

        Returns:
            str: Отформатированное уведомление
        """
        from src.presentation.telegram.messages.formatters import format_notification_status

        # Принудительно перезагружаем языковые настройки пользователей
        if hasattr(self._user_language_service, '_user_language_storage') and hasattr(self._user_language_service._user_language_storage, '_load'):
            try:
                self._user_language_service._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены в format_status_notification для пользователя {user_id}")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Проверяем, что язык получен корректно
        if not language:
            self._logger.warning(f"Язык пользователя {user_id} не определен, используем язык по умолчанию (ru)")
            language = 'ru'

        self._logger.info(f"Форматирование уведомления о статусе парсинга для пользователя {user_id} на языке: {language}")

        # Получаем шаблон уведомления
        template = self.get_status_template(user_id)
        self._logger.info(f"Получен шаблон уведомления о статусе для языка {language}")

        # Форматируем уведомление с учетом языка пользователя
        try:
            result = format_notification_status(
                category=category,
                new_count=new_count,
                processed_count=processed_count,
                error_count=error_count,
                execution_time=execution_time,
                language=language
            )
            self._logger.info(f"Успешно отформатировано уведомление о статусе парсинга")
            return result
        except Exception as e:
            self._logger.error(f"Ошибка при форматировании уведомления о статусе парсинга: {e}")
            # В случае ошибки используем шаблон напрямую
            try:
                return template.format(
                    category=category,
                    new_count=new_count,
                    processed_count=processed_count,
                    error_count=error_count,
                    execution_time=execution_time
                )
            except Exception as e2:
                self._logger.error(f"Критическая ошибка при форматировании уведомления о статусе парсинга: {e2}")
                # В случае критической ошибки возвращаем базовую информацию
                return f"""
<b>📊 Статус парсинга</b>

Категория: <b>{category}</b>
Новых объявлений: {new_count}
Обработано: {processed_count}
Ошибок: {error_count}

Время выполнения: {execution_time}
"""

    def get_open_ad_button_text(self, user_id: int) -> str:
        """
        Возвращает локализованный текст для кнопки "Открыть объявление".

        Args:
            user_id: ID пользователя

        Returns:
            str: Локализованный текст кнопки
        """
        # Принудительно перезагружаем языковые настройки пользователей
        if hasattr(self._user_language_service, '_user_language_storage') and hasattr(self._user_language_service._user_language_storage, '_load'):
            try:
                self._user_language_service._user_language_storage._load()
                self._logger.info(f"Языковые настройки пользователей успешно перезагружены в get_open_ad_button_text для пользователя {user_id}")
            except Exception as e:
                self._logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

        # Получаем язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Проверяем, что язык получен корректно
        if not language:
            self._logger.warning(f"Язык пользователя {user_id} не определен, используем язык по умолчанию (ru)")
            language = 'ru'

        self._logger.info(f"Получение текста кнопки 'Открыть объявление' для пользователя {user_id} на языке: {language}")

        # Пробуем получить локализованный текст
        button_text = self._localization_service.get_text('open_ad_button', language)

        # Если локализованный текст не найден, используем резервный текст
        if not button_text:
            button_text = self._localization_service.get_text('open_ad_button_fallback', language)
            self._logger.info(f"Используем резервный текст для кнопки: {button_text}")

        # Если и резервный текст не найден, используем текст по умолчанию в зависимости от языка
        if not button_text:
            if language == 'ua':
                button_text = "Відкрити оголошення"
            elif language == 'en':
                button_text = "Open listing"
            else:
                button_text = "Открыть объявление"
            self._logger.info(f"Используем текст по умолчанию для языка {language}: {button_text}")

        self._logger.info(f"Итоговый текст кнопки для пользователя {user_id} на языке {language}: {button_text}")
        return button_text
