"""
Регистрация обработчиков команд для локализации.
"""
from telegram.ext import CommandHandler, CallbackQueryHandler

from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.presentation.telegram.localization.language_handler import LanguageHandler


def register_language_handlers(application, localization_service: LocalizationService, user_language_service: UserLanguageService) -> None:
    """
    Регистрирует обработчики команд для локализации.

    Args:
        application: Объект приложения Telegram
        localization_service: Сервис локализации
        user_language_service: Сервис управления языком пользователя
    """
    # Создаем обработчик команды для выбора языка
    language_handler = LanguageHandler(localization_service, user_language_service)

    # Регистрируем обработчик команды /language
    application.add_handler(CommandHandler("language", language_handler.handle_language_command))

    # Регистрируем обработчик callback-запросов для выбора языка
    application.add_handler(CallbackQueryHandler(
        language_handler.handle_language_callback,
        pattern=r"^language_"
    ))
