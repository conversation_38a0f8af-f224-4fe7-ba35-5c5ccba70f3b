"""
Обработчик команды для выбора языка.
"""
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton

from src.application.services.localization import LocalizationService
from src.application.services.user_language import UserLanguageService
from src.presentation.telegram.menu import create_inline_button, create_inline_keyboard


class LanguageHandler:
    """
    Обработчик команды для выбора языка.
    """

    def __init__(self, localization_service: LocalizationService, user_language_service: UserLanguageService):
        """
        Инициализирует обработчик команды для выбора языка.

        Args:
            localization_service: Сервис локализации
            user_language_service: Сервис управления языком пользователя
        """
        self._localization_service = localization_service
        self._user_language_service = user_language_service

    async def get_language_menu(self, user_id: int) -> InlineKeyboardMarkup:
        """
        Создает меню выбора языка.

        Args:
            user_id: ID пользователя

        Returns:
            InlineKeyboardMarkup: Клавиатура с кнопками выбора языка
        """
        keyboard = []

        # Получаем текущий язык пользователя
        current_language = self._user_language_service.get_language(user_id)

        # Создаем кнопки для каждого доступного языка
        for language in self._user_language_service.get_available_languages():
            # Добавляем отметку к текущему языку
            text = f"{language} ✓" if language.code == current_language else str(language)
            button = create_inline_button(text, f"language_{language.code}")
            keyboard.append([button])

        return create_inline_keyboard(keyboard)

    async def handle_language_command(self, update: Update, context=None) -> None:
        """
        Обрабатывает команду /language.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await message.reply_text("Не удалось определить пользователя.")
            return

        user_id = message.from_user.id

        # Получаем текущий язык пользователя
        language = self._user_language_service.get_language(user_id)

        # Получаем название текущего языка
        language_name = None
        for lang in self._user_language_service.get_available_languages():
            if lang.code == language:
                language_name = str(lang)
                break

        if not language_name:
            language_name = language

        # Получаем локализованные тексты
        current_language_text = self._localization_service.get_text('current_language', language)
        current_language_text = self._localization_service.format_template(current_language_text, {'language': language_name})
        select_language_text = self._localization_service.get_text('select_language', language)

        # Формируем заголовок
        title = f"{current_language_text}\n\n{select_language_text}"

        # Отправляем меню выбора языка
        await message.reply_text(
            title,
            reply_markup=await self.get_language_menu(user_id)
        )

    async def handle_language_callback(self, update: Update, context=None) -> None:
        """
        Обрабатывает callback-запрос для выбора языка.

        Args:
            update: Объект обновления
            context: Контекст обработчика (не используется)
        """
        # Получаем callback-запрос
        query = update.callback_query
        if not query:
            return

        # Получаем ID пользователя
        if not query.from_user:
            await query.answer("Не удалось определить пользователя.")
            return

        user_id = query.from_user.id

        # Получаем текущий язык пользователя
        current_language = self._user_language_service.get_language(user_id)

        # Получаем выбранный язык
        callback_data = query.data
        if not callback_data or not isinstance(callback_data, str) or not callback_data.startswith("language_"):
            await query.answer("Неверный формат данных.")
            return

        language_code = callback_data.replace("language_", "")

        # Проверяем, что язык изменился
        if language_code == current_language:
            await query.answer("Язык не изменился.")
            return

        # Устанавливаем язык пользователя
        if not self._user_language_service.set_language(user_id, language_code):
            # Получаем локализованный текст
            error_message = self._localization_service.get_text('language_not_supported', current_language)
            error_message = self._localization_service.format_template(error_message, {'language': language_code})

            await query.answer(error_message)
            return

        # Получаем название языка
        language_name = None
        for language in self._user_language_service.get_available_languages():
            if language.code == language_code:
                language_name = str(language)
                break

        if not language_name:
            language_name = language_code

        # Получаем локализованный текст
        success_message = self._localization_service.get_text('language_changed', language_code)
        success_message = self._localization_service.format_template(success_message, {'language': language_name})

        # Отвечаем на callback-запрос
        await query.answer(success_message)

        # Обновляем сообщение
        if query.message:
            # Получаем локализованные тексты
            current_language_text = self._localization_service.get_text('current_language', language_code)
            current_language_text = self._localization_service.format_template(current_language_text, {'language': language_name})
            select_language_text = self._localization_service.get_text('select_language', language_code)

            # Формируем заголовок
            title = f"{current_language_text}\n\n{select_language_text}"

            # Создаем локализованное меню выбора языка
            localized_inline_menu = await self.get_language_menu(user_id)

            # Обновляем inline-клавиатуру с выбором языка
            try:
                await query.message.edit_text(
                    text=title,
                    reply_markup=localized_inline_menu
                )
                import logging
                logger = logging.getLogger("telegram_bot")
                logger.info(f"Обновлена inline-клавиатура для пользователя {user_id}")
            except Exception as e:
                import logging
                logger = logging.getLogger("telegram_bot")
                logger.warning(f"Не удалось обновить inline-клавиатуру: {e}")

            # Обновляем главное меню и основную клавиатуру автоматически
            try:
                import logging
                logger = logging.getLogger("telegram_bot")

                # Получаем бота из контекста
                # В python-telegram-bot 20.3 бот доступен через свойство message.bot или через контекст
                if hasattr(query, 'message') and query.message is not None and hasattr(query.message, 'bot'):
                    bot = query.message.bot
                else:
                    # Если не можем получить бота из сообщения, используем глобальный экземпляр бота
                    from src.presentation.telegram.bot import bot_instance
                    bot = bot_instance

                # Получаем chat_id из сообщения или из контекста
                if hasattr(query, 'message') and query.message is not None and hasattr(query.message, 'chat_id'):
                    chat_id = query.message.chat_id
                elif hasattr(query, 'from_user') and query.from_user is not None and hasattr(query.from_user, 'id'):
                    chat_id = query.from_user.id
                else:
                    # Если не можем получить chat_id, используем user_id
                    chat_id = user_id

                # Импортируем функцию для обновления основной клавиатуры
                from src.presentation.telegram.bot import PropertyBot
                from src.presentation.telegram.localization.menu import get_localized_main_menu, get_localized_language_menu
                from telegram import ReplyKeyboardRemove

                # Получаем язык пользователя
                language = self._user_language_service.get_language(user_id)

                # Получаем локализованный текст для заголовка главного меню
                main_menu_title = self._localization_service.get_text('main_menu_title', language) or "Главное меню:"
                if language == 'ua':
                    main_menu_title = "Головне меню:"

                # Inline-клавиатура уже обновлена выше

                # Теперь обновляем основную клавиатуру
                try:
                    # Создаем локализованное главное меню для основной клавиатуры
                    localized_main_keyboard = get_localized_main_menu(self._localization_service, self._user_language_service, user_id)

                    # Отправляем новую основную клавиатуру
                    # Используем статический метод PropertyBot.safe_send_message
                    from src.presentation.telegram.bot import PropertyBot
                    await PropertyBot.safe_send_message(
                        bot=bot,
                        chat_id=chat_id,
                        text=main_menu_title,
                        reply_markup=localized_main_keyboard
                    )
                    logger.info(f"Отправлена новая основная клавиатура для пользователя {user_id}")
                except Exception as e:
                    logger.error(f"Ошибка при обновлении основной клавиатуры: {e}")

                logger.info(f"Обновлена основная клавиатура для пользователя {user_id} в чате {chat_id} после смены языка на {language_code}")

            except Exception as e:
                import logging
                logger = logging.getLogger("telegram_bot")
                logger.error(f"Ошибка при обновлении главного меню и клавиатуры: {e}")
