"""
Обработчики команд Telegram бота.
"""
import json
import logging
import os
from typing import Optional, Dict, Any, List, Sequence

from telegram import Update, ReplyKeyboardRemove, InlineKeyboardMarkup, MessageEntity

from src.config.paths import get_data_path
from src.infrastructure.formatters.field_filter import FieldFilter
from src.presentation.telegram.menu import (
    get_admin_menu, get_broadcast_menu, get_categories_menu, get_category_action_menu,
    get_category_type_menu, get_help_menu, get_main_menu, get_notifications_menu,
    get_requests_menu, get_users_menu, get_notification_settings_keyboard,
    CATEGORY_NAMES, CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI
)
from src.presentation.telegram.localization.menu import get_localized_notification_settings_menu


async def safe_reply_text(message, text, reply_markup=None, parse_mode=None):
    """
    Безопасная обертка для метода reply_text, которая добавляет необходимые параметры с значениями по умолчанию.
    """
    # В новой версии python-telegram-bot некоторые параметры не могут быть None
    # Используем только необходимые параметры
    return await message.reply_text(
        text=text,
        parse_mode=parse_mode,
        reply_markup=reply_markup
    )


async def handle_start(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /start.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Параметры context и bot не используются в этой функции, но необходимы для совместимости с другими обработчиками
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Отправляем приветствие
    user_name = message.from_user.first_name if message.from_user else "Друг"
    await safe_reply_text(
        message,
        f"Привет, {user_name}! Я бот для уведомлений о новых объявлениях OLX.\n\n"
        "Используйте кнопки меню для управления ботом или команду /help для получения справки.",
        reply_markup=get_main_menu()
    )


async def handle_help(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /help.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Параметры context и bot не используются в этой функции, но необходимы для совместимости с другими обработчиками
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Этот код не будет выполняться, так как обработка команды /help перенесена в метод _handle_help класса PropertyBot
    # Оставляем для обратной совместимости
    await safe_reply_text(
        message,
        "Выберите раздел справки:",
        reply_markup=get_help_menu()
    )


async def handle_status(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /status.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Параметр context не используется в этой функции, но необходим для совместимости с другими обработчиками
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    status_running = bot.localization_service.get_text('status_running', language) or "Запущен"
    active_categories = bot.localization_service.get_text('categories_active', language) or "Активные категории:"
    no_active_categories = bot.localization_service.get_text('categories_empty', language) or "У вас нет активных категорий."
    bot_status_text = bot.localization_service.get_text('bot_status', language) or "Статус бота:"

    # Получаем категории пользователя
    categories = bot.get_categories(user_id)

    # Формируем статус
    # Устанавливаем флаг running в True, так как бот запущен
    bot.running = True
    status = f"{bot_status_text} {status_running}\n\n"

    if categories:
        status += f"{active_categories}\n"
        for category in categories:
            # Получаем локализованное описание категории
            category_parts = category.split('_')
            if len(category_parts) == 2:
                category_type, operation = category_parts
                category_name = bot.localization_service.get_text(f'category_{category_type}', language)
                operation_name = bot.localization_service.get_text(f'category_{operation}', language)
                if category_name and operation_name:
                    description = f"{operation_name} {category_name}"
                    emoji = CATEGORY_EMOJI.get(category, "")
                    status += f"- {emoji} {description}\n"
                else:
                    description = CATEGORY_DESCRIPTIONS.get(category, category)
                    emoji = CATEGORY_EMOJI.get(category, "")
                    status += f"- {emoji} {description}\n"
            else:
                description = CATEGORY_DESCRIPTIONS.get(category, category)
                emoji = CATEGORY_EMOJI.get(category, "")
                status += f"- {emoji} {description}\n"
    else:
        status += f"{no_active_categories}\n"

    # Отправляем статус с локализованными кнопками главного меню
    from src.presentation.telegram.localization.menu import get_localized_main_menu
    await safe_reply_text(
        message,
        status,
        reply_markup=get_localized_main_menu(bot.localization_service, bot.user_language_service, user_id)
    )


async def handle_stop(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /stop.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Параметр context не используется в этой функции, но необходим для совместимости с другими обработчиками
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Удаляем все категории пользователя
    if user_id in bot.active_categories:
        bot.active_categories[user_id] = []

    # Этот код не будет выполняться, так как обработка команды /stop перенесена в метод _handle_stop класса PropertyBot
    # Оставляем для обратной совместимости
    await safe_reply_text(
        message,
        "Уведомления остановлены. Выберите категории для подписки или вернитесь в главное меню.",
        reply_markup=get_categories_menu()
    )


async def handle_category(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /category.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Получаем аргументы команды
    args = context.args

    # Проверяем наличие аргументов
    if not args:
        await safe_reply_text(
            message,
            "Используйте команду /category add <категория> для добавления категории, "
            "/category remove <категория> для удаления категории, "
            "/category list для просмотра списка категорий или "
            "/category all для подписки на все категории."

    )
        return

    # Получаем действие
    action = args[0].lower()

    # Обрабатываем действие
    if action == 'all':
        # Загружаем список всех категорий
        categories_file = get_data_path("categories.json")
        if not os.path.exists(categories_file):
            categories_file = os.path.join(os.path.dirname(__file__), "../../../config/data/categories.json")

        try:
            with open(categories_file, 'r', encoding='utf-8') as f:
                categories_data = json.load(f)
        except Exception as e:
            logging.error(f"Ошибка при загрузке категорий: {e}")
            categories_data = {"categories": []}

        # Получаем список всех категорий
        all_categories = [cat.get("id") for cat in categories_data.get("categories", [])]

        # Инициализируем список категорий пользователя, если он не существует
        if user_id not in bot.active_categories:
            bot.active_categories[user_id] = []

        # Добавляем все категории
        for category in all_categories:
            if category not in bot.active_categories[user_id]:
                bot.active_categories[user_id].append(category)

        # Отправляем сообщение
        await safe_reply_text(message, f"Вы подписались на все категории ({len(all_categories)} шт.)."
        )

    elif action == 'add':
        # Проверяем наличие категории
        if len(args) < 2:
            await safe_reply_text(message, "Используйте команду /category add <категория> для добавления категории.")
            return

        # Получаем категорию
        category = args[1].lower()

        # Проверяем существование категории
        categories_file = get_data_path("categories.json")
        if not os.path.exists(categories_file):
            categories_file = os.path.join(os.path.dirname(__file__), "../../../config/data/categories.json")

        try:
            with open(categories_file, 'r', encoding='utf-8') as f:
                categories_data = json.load(f)
        except Exception as e:
            logging.error(f"Ошибка при загрузке категорий: {e}")
            categories_data = {"categories": []}

        # Проверяем, существует ли категория
        category_exists = False
        for cat in categories_data.get("categories", []):
            if cat.get("id") == category:
                category_exists = True
                break

        if not category_exists:
            await safe_reply_text(message, f"Категория {category} не найдена. Используйте команду /help для просмотра списка доступных категорий.")
            return

        # Добавляем категорию
        bot.add_category(user_id, category)

        # Отправляем сообщение
        await safe_reply_text(message, f"Категория {category} добавлена. Вы будете получать уведомления о новых объявлениях в этой категории.")

    elif action == 'remove':
        # Проверяем наличие категории
        if len(args) < 2:
            await safe_reply_text(message, "Используйте команду /category remove <категория> для удаления категории.")
            return

        # Получаем категорию
        category = args[1].lower()

        # Удаляем категорию
        bot.remove_category(user_id, category)

        # Отправляем сообщение
        await safe_reply_text(message, f"Категория {category} удалена. Вы больше не будете получать уведомления о новых объявлениях в этой категории.")

    elif action == 'list':
        # Получаем язык пользователя
        language = bot.user_language_service.get_language(user_id)

        # Получаем локализованные тексты
        active_categories = bot.localization_service.get_text('categories_active', language) or "Активные категории:"
        no_active_categories = bot.localization_service.get_text('categories_empty', language) or "У вас нет активных категорий."
        add_category_help = bot.localization_service.get_text('add_category_help', language) or "Используйте команду /category add <категория> для добавления категории."

        # Получаем категории пользователя
        categories = bot.get_categories(user_id)

        # Формируем список категорий
        if categories:
            category_list = []
            for category in categories:
                # Получаем локализованное описание категории
                category_parts = category.split('_')
                if len(category_parts) == 2:
                    category_type, operation = category_parts
                    category_name = bot.localization_service.get_text(f'category_{category_type}', language)
                    operation_name = bot.localization_service.get_text(f'category_{operation}', language)
                    if category_name and operation_name:
                        description = f"{operation_name} {category_name}"
                        emoji = CATEGORY_EMOJI.get(category, "")
                        category_list.append(f"- {emoji} {description}")
                    else:
                        description = CATEGORY_DESCRIPTIONS.get(category, category)
                        emoji = CATEGORY_EMOJI.get(category, "")
                        category_list.append(f"- {emoji} {description}")
                else:
                    description = CATEGORY_DESCRIPTIONS.get(category, category)
                    emoji = CATEGORY_EMOJI.get(category, "")
                    category_list.append(f"- {emoji} {description}")

            await safe_reply_text(message, f"{active_categories}\n" + "\n".join(category_list)
            )
        else:
            await safe_reply_text(message, f"{no_active_categories} {add_category_help}")

    else:
        # Неизвестное действие
        await safe_reply_text(
            message,
            "Неизвестное действие. Используйте команду /category add <категория> для добавления категории, "
            "/category remove <категория> для удаления категории или "
            "/category list для просмотра списка категорий."

    )


# Функция handle_filter удалена, так как функционал фильтрации больше не используется


async def handle_error(update: Optional[Update], context, bot) -> None:
    """
    Обрабатывает ошибки.

    Args:
        update: Объект обновления (может быть None)
        context: Контекст обработчика
        bot: Объект бота
    """
    # Логируем ошибку
    bot.logger.error(f"Ошибка: {context.error}")

    # Получаем текст команды, если есть
    command_text = ""
    if update and update.message and update.message.text:
        command_text = update.message.text

        # Удаляем имя бота из команды, если оно есть (например, @Assistent_pro_bot)
        if '@' in command_text:
            # Разбиваем текст на части по пробелу
            parts = command_text.split(' ')
            # Удаляем имя бота из первой части
            if parts and '@' in parts[0]:
                # Берем только команду без имени бота
                command_parts = parts[0].split('@')
                if len(command_parts) > 0:
                    parts[0] = command_parts[0]
                command_text = ' '.join(parts)
                bot.logger.info(f"[Обработка ошибки] Удалено имя бота из команды: {command_text}")

    # Отправляем сообщение об ошибке
    if update and update.message:
        # Проверяем, связана ли ошибка с командой /broadcast
        if command_text and command_text.startswith('/broadcast'):
            message = update.message
            await safe_reply_text(
                message,
                "Произошла ошибка при обработке команды /broadcast.\n\n"
                "Используйте формат:\n"
                "/broadcast [all|users|admins] <текст сообщения>\n\n"
                "Пример: /broadcast admins Завтра сборы",
                reply_markup=get_broadcast_menu()
            )
        else:
            # Общее сообщение об ошибке
            message = update.message
            await safe_reply_text(
                message,
                "Произошла ошибка при обработке команды. Пожалуйста, попробуйте еще раз."
            )


async def handle_admin(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /admin.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Проверяем, является ли пользователь администратором
    if user_id not in bot.admin_users:
        await safe_reply_text(message, "У вас нет прав администратора.")
        return

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем локализованный текст заголовка
    title = bot.localization_service.get_text('admin_menu_title', language) or "Меню администратора:"

    # Отправляем локализованное меню администратора
    from src.presentation.telegram.localization.menu import get_localized_admin_menu
    await safe_reply_text(
        message,
        title,
        reply_markup=get_localized_admin_menu(bot.localization_service, bot.user_language_service, user_id)
    )


async def handle_users(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /users.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Проверяем, является ли пользователь администратором
    if user_id not in bot.admin_users:
        await safe_reply_text(message, "У вас нет прав администратора.")
        return

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем локализованный текст заголовка
    title = bot.localization_service.get_text('users_menu_title', language) or "Управление пользователями:"

    # Отправляем локализованное меню управления пользователями
    from src.presentation.telegram.localization.menu import get_localized_users_menu
    await safe_reply_text(
        message,
        title,
        reply_markup=get_localized_users_menu(bot.localization_service, bot.user_language_service, user_id)
    )


async def handle_requests(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /requests.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Проверяем, является ли пользователь администратором
    if user_id not in bot.admin_users:
        await safe_reply_text(message, "У вас нет прав администратора.")
        return

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем локализованный текст заголовка
    title = bot.localization_service.get_text('requests_menu_title', language) or "Управление запросами на доступ:"

    # Отправляем локализованное меню управления запросами на доступ
    from src.presentation.telegram.localization.menu import get_localized_requests_menu
    await safe_reply_text(
        message,
        title,
        reply_markup=get_localized_requests_menu(bot.localization_service, bot.user_language_service, user_id)
    )


async def handle_broadcast(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /broadcast.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Проверяем, является ли пользователь администратором
    if user_id not in bot.admin_users:
        await safe_reply_text(message, "У вас нет прав администратора.")
        return

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем текст сообщения
    text = message.text

    # Проверяем, есть ли в тексте команда /broadcast
    if text:
        # Удаляем имя бота из команды, если оно есть (например, @Assistent_pro_bot)
        if '@' in text:
            # Разбиваем текст на части по пробелу
            parts = text.split(' ')
            # Удаляем имя бота из первой части
            if parts and '@' in parts[0]:
                # Берем только команду без имени бота
                command_parts = parts[0].split('@')
                if len(command_parts) > 0:
                    parts[0] = command_parts[0]
                text = ' '.join(parts)
                bot.logger.info(f"[Рассылка] Удалено имя бота из команды: {text}")

        # Проверяем, начинается ли текст с команды /broadcast
        if text.startswith('/broadcast'):
            # Разбиваем текст на части
            parts = text.split(' ')

            # Удаляем команду /broadcast
            parts = parts[1:] if len(parts) > 1 else []

            # Используем полученные части как аргументы
            args = parts
        else:
            # Используем стандартные аргументы команды
            args = context.args
    else:
        # Используем стандартные аргументы команды
        args = context.args

    # Проверяем наличие аргументов
    if not args:
        # Показываем меню выбора группы для рассылки
        await safe_reply_text(
        message,
        "Выберите группу пользователей для рассылки или используйте команду:\n"
            "/broadcast [all|users|admins] <текст сообщения>\n\n"
            "Пример: /broadcast admins Завтра сборы",
        reply_markup=get_broadcast_menu(
    )
        )
        return

    # Определяем целевую группу и текст сообщения
    # Проверяем, есть ли первый аргумент и является ли он целевой группой
    if args and args[0].lower() in ['all', 'users', 'admins']:
        target_group = args[0].lower()
        # Если указана целевая группа, берем текст сообщения начиная со второго аргумента
        broadcast_text = " ".join(args[1:])

        # Проверяем, есть ли текст сообщения
        if not broadcast_text.strip():
            await safe_reply_text(message, f"Используйте команду /broadcast {target_group} <текст сообщения> для отправки сообщения.\n\n"
                "Пример: /broadcast admins Завтра сборы")
            return
    else:
        # Если целевая группа не указана или неверна
        if args:
            # Если есть аргументы, но первый не является целевой группой
            await safe_reply_text(
        message,
        "Неверный формат команды. Используйте:\n"
                "/broadcast [all|users|admins] <текст сообщения>\n\n"
                "Пример: /broadcast admins Завтра сборы",
        reply_markup=get_broadcast_menu(
    )
            )
            return

        # Если нет аргументов, используем рассылку всем по умолчанию
        target_group = 'all'
        broadcast_text = " ".join(args)

    # Определяем список получателей в зависимости от целевой группы
    if target_group == 'all':
        # Все пользователи (разрешенные + администраторы)
        recipients = set(bot.allowed_users + bot.admin_users)
        group_name = "всем пользователям"
    elif target_group == 'users':
        # Только обычные пользователи (не администраторы)
        recipients = set(user_id for user_id in bot.allowed_users if user_id not in bot.admin_users)
        group_name = "обычным пользователям"
    elif target_group == 'admins':
        # Только администраторы
        recipients = set(bot.admin_users)
        group_name = "администраторам"
    else:
        # Если неизвестный тип рассылки, используем рассылку всем по умолчанию
        recipients = set(bot.allowed_users + bot.admin_users)
        group_name = "всем пользователям"
        bot.logger.warning(f"[Рассылка] Неизвестный тип рассылки: {target_group}, используем рассылку всем")

    # Добавляем ID чатов из настроек, если рассылка для всех
    if target_group == 'all' or (target_group not in ['users', 'admins']):
        from src.config import settings
        chat_ids = settings.get_telegram_chat_ids()
        if chat_ids:
            recipients.update(chat_ids)
            bot.logger.info(f"[Рассылка] Добавлены ID чатов из настроек: {chat_ids}")

    # Подробное логирование
    bot.logger.info(f"[Рассылка] Целевая группа: {target_group} ({group_name})")
    bot.logger.info(f"[Рассылка] Список получателей: {recipients}")
    bot.logger.info(f"[Рассылка] Начинаем отправку сообщений {len(recipients)} получателям")

    # Отправляем сообщение получателям
    sent_count = 0
    failed_count = 0

    for recipient_id in recipients:
        bot.logger.info(f"[Рассылка] Попытка отправки сообщения получателю {recipient_id}")
        try:
            # Проверяем, что recipient_id является целым числом
            if isinstance(recipient_id, str):
                try:
                    recipient_id = int(recipient_id)
                    bot.logger.info(f"[Рассылка] Преобразование recipient_id из строки в число: {recipient_id}")
                except ValueError:
                    bot.logger.error(f"[Рассылка] Невозможно преобразовать recipient_id в число: {recipient_id}")
                    failed_count += 1
                    continue

            # Проверяем, что recipient_id не равен 0 и не пуст
            if not recipient_id:
                bot.logger.error(f"[Рассылка] Пустой recipient_id: {recipient_id}")
                failed_count += 1
                continue

            # Отправляем сообщение
            bot.logger.info(f"[Рассылка] Отправка сообщения получателю {recipient_id}")
            await bot.application.bot.send_message(
                chat_id=recipient_id,
                text=f"📢 Сообщение от администратора:\n\n{broadcast_text}"
            )
            bot.logger.info(f"[Рассылка] Сообщение успешно отправлено получателю {recipient_id}")
            sent_count += 1
        except Exception as e:
            bot.logger.error(f"[Рассылка] Ошибка при отправке сообщения получателю {recipient_id}: {e}")
            bot.logger.error(f"[Рассылка] Тип ошибки: {type(e).__name__}")
            bot.logger.error(f"[Рассылка] Детали ошибки: {str(e)}")
            failed_count += 1

    # Отправляем отчет администратору
    await safe_reply_text(message, f"Сообщение отправлено {sent_count} {group_name}.\n"
        f"Не удалось отправить {failed_count} получателям.")


async def handle_stats(update: Update, context, bot) -> None:
    """
    Обрабатывает команду /stats.

    Args:
        update: Объект обновления
        context: Контекст обработчика
        bot: Объект бота
    """
    # Получаем сообщение
    message = update.message
    if not message:
        return

    # Получаем ID пользователя
    if not message.from_user:
        await safe_reply_text(message, "Не удалось определить пользователя.")
        return

    user_id = message.from_user.id

    # Проверяем, является ли пользователь администратором
    if user_id not in bot.admin_users:
        await safe_reply_text(message, "У вас нет прав администратора.")
        return

    # Получаем язык пользователя
    language = bot.user_language_service.get_language(user_id)

    # Получаем локализованные тексты
    stats_title = bot.localization_service.get_text('stats_title', language) or "📊 Статистика бота:"
    total_users_text = bot.localization_service.get_text('total_users', language) or "👥 Всего пользователей:"
    admin_users_text = bot.localization_service.get_text('admin_users', language) or "👑 Администраторов:"
    regular_users_text = bot.localization_service.get_text('regular_users', language) or "👤 Обычных пользователей:"
    access_requests_text = bot.localization_service.get_text('access_requests', language) or "📝 Запросы на доступ:"
    pending_requests_text = bot.localization_service.get_text('pending_requests', language) or "⏳ Ожидающие:"
    approved_requests_text = bot.localization_service.get_text('approved_requests', language) or "✅ Одобренные:"
    rejected_requests_text = bot.localization_service.get_text('rejected_requests', language) or "❌ Отклоненные:"
    active_categories_text = bot.localization_service.get_text('active_categories_count', language) or "🏠 Активные категории:"

    # Получаем статистику
    total_users = len(set(bot.allowed_users + bot.admin_users))
    admin_users = len(bot.admin_users)
    allowed_users = len(bot.allowed_users)

    # Получаем количество запросов на доступ
    pending_requests = len(bot.access_requests.get_pending_requests())
    approved_requests = sum(1 for _, request in bot.access_requests.requests.items() if request.get("status") == "approved")
    rejected_requests = sum(1 for _, request in bot.access_requests.requests.items() if request.get("status") == "rejected")

    # Получаем количество активных категорий
    active_categories = sum(len(categories) for categories in bot.active_categories.values())

    # Формируем статистику
    stats = f"{stats_title}\n\n"
    stats += f"{total_users_text} {total_users}\n"
    stats += f"{admin_users_text} {admin_users}\n"
    stats += f"{regular_users_text} {allowed_users}\n\n"
    stats += f"{access_requests_text}\n"
    stats += f"{pending_requests_text} {pending_requests}\n"
    stats += f"{approved_requests_text} {approved_requests}\n"
    stats += f"{rejected_requests_text} {rejected_requests}\n\n"
    stats += f"{active_categories_text} {active_categories}\n"

    # Отправляем статистику
    await safe_reply_text(message, stats)
