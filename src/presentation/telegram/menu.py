"""
Модуль для создания меню и кнопок Telegram бота.
"""
from typing import List
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton

# Игнорируем ошибки типов для этого файла
# type: ignore


def create_keyboard_button(text: str) -> KeyboardButton:
    """
    Создает кнопку клавиатуры с учетом типизации.

    Args:
        text: Текст кнопки

    Returns:
        KeyboardButton: Кнопка клавиатуры
    """
    # В новой версии python-telegram-bot параметры не могут быть None
    # Используем только обязательный параметр text
    # Игнорируем ошибки типов с помощью директивы type: ignore
    return KeyboardButton(text=text)  # type: ignore


def create_inline_button(text: str, callback_data: str) -> InlineKeyboardButton:
    """
    Создает inline-кнопку с учетом типизации.

    Args:
        text: Текст кнопки
        callback_data: Данные для обратного вызова

    Returns:
        InlineKeyboardButton: Inline-кнопка
    """
    # Дополнительное логирование для отладки
    print(f"DEBUG: Создание inline-кнопки с текстом: '{text}', callback_data: {callback_data}")

    # В новой версии python-telegram-bot параметры не могут быть None
    # Используем только обязательные параметры text и callback_data
    # Игнорируем ошибки типов с помощью директивы type: ignore
    button = InlineKeyboardButton(text=text, callback_data=callback_data)  # type: ignore
    print(f"DEBUG: Создана inline-кнопка: {button}")
    return button


def create_reply_keyboard(keyboard: List[List[KeyboardButton]], resize_keyboard: bool = True, input_field_placeholder: str = "Выберите действие") -> ReplyKeyboardMarkup:
    """
    Создает клавиатуру с учетом типизации.

    Args:
        keyboard: Массив кнопок
        resize_keyboard: Изменять размер клавиатуры
        input_field_placeholder: Текст-подсказка в поле ввода

    Returns:
        ReplyKeyboardMarkup: Клавиатура
    """
    # В новой версии python-telegram-bot параметры не могут быть None
    # Используем только необходимые параметры с конкретными значениями
    # Игнорируем ошибки типов с помощью директивы type: ignore
    return ReplyKeyboardMarkup(keyboard=keyboard, resize_keyboard=resize_keyboard, input_field_placeholder=input_field_placeholder)  # type: ignore


def create_inline_keyboard(keyboard: List[List[InlineKeyboardButton]]) -> InlineKeyboardMarkup:
    """
    Создает inline-клавиатуру с учетом типизации.

    Args:
        keyboard: Массив кнопок

    Returns:
        InlineKeyboardMarkup: Inline-клавиатура
    """
    # В новой версии python-telegram-bot параметры не могут быть None
    # Используем только необходимые параметры с конкретными значениями
    # Игнорируем ошибки типов с помощью директивы type: ignore
    return InlineKeyboardMarkup(inline_keyboard=keyboard)  # type: ignore


def get_main_menu() -> ReplyKeyboardMarkup:
    """
    Создает главное меню бота.

    Returns:
        ReplyKeyboardMarkup: Клавиатура с кнопками главного меню
    """
    keyboard = [
        [create_keyboard_button("📊 Статус"), create_keyboard_button("🔔 Уведомления")],
        [create_keyboard_button("🏠 Категории"), create_keyboard_button("❓ Помощь")]
    ]
    return create_reply_keyboard(keyboard)


def get_notifications_menu() -> ReplyKeyboardMarkup:
    """
    Создает меню управления уведомлениями.

    Returns:
        ReplyKeyboardMarkup: Клавиатура с кнопками меню уведомлений
    """
    keyboard = [
        [create_keyboard_button("🔔 Включить уведомления"), create_keyboard_button("🔕 Отключить уведомления")],
        [create_keyboard_button("◀️ Назад в главное меню")]
    ]
    return create_reply_keyboard(keyboard)


# Функция get_settings_menu удалена, так как функционал фильтрации больше не используется


def get_categories_menu() -> InlineKeyboardMarkup:
    """
    Создает меню выбора категорий недвижимости.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками категорий
    """
    keyboard = [
        [
            create_inline_button("🏢 Квартиры", "category_apartments"),
            create_inline_button("🏠 Дома", "category_houses")
        ],
        [
            create_inline_button("🚪 Комнаты", "category_rooms"),
            create_inline_button("🚗 Гаражи", "category_garages")
        ],
        [
            create_inline_button("🏡 Дачи", "category_dacha"),
            create_inline_button("🌳 Земля", "category_land")
        ],
        [
            create_inline_button("🏭 Коммерческая", "category_commercial")
        ],
        [
            create_inline_button("✅ Все категории", "category_all"),
            create_inline_button("❌ Очистить все", "category_clear")
        ]
    ]
    return create_inline_keyboard(keyboard)


def get_category_type_menu(category_type: str) -> InlineKeyboardMarkup:
    """
    Создает меню выбора типа операции для категории.

    Args:
        category_type: Тип категории (apartments, houses, rooms, etc.)

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками типов операций
    """
    # Для категории "land" (Земельные участки) показываем только кнопку "Продажа"
    if category_type == "land":
        keyboard = [
            [
                create_inline_button("🛒 Продажа", f"{category_type}_sale")
            ],
            [
                create_inline_button("◀️ Назад к категориям", "back_to_categories")
            ]
        ]
    else:
        keyboard = [
            [
                create_inline_button("🛒 Продажа", f"{category_type}_sale"),
                create_inline_button("💰 Аренда", f"{category_type}_rent")
            ],
            [
                create_inline_button("◀️ Назад к категориям", "back_to_categories")
            ]
        ]
    return create_inline_keyboard(keyboard)


def get_category_action_menu(category_id: str) -> InlineKeyboardMarkup:
    """
    Создает меню действий для категории.

    Args:
        category_id: ID категории (apartments_sale, houses_rent, etc.)

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками действий
    """
    keyboard = [
        [
            create_inline_button("➕ Подписаться", f"add_{category_id}"),
            create_inline_button("➖ Отписаться", f"remove_{category_id}")
        ],
        [
            create_inline_button("◀️ Назад к типам", f"back_to_{category_id.split('_')[0]}")
        ]
    ]
    return create_inline_keyboard(keyboard)


def get_help_menu() -> InlineKeyboardMarkup:
    """
    Создает меню помощи.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками помощи
    """
    keyboard = [
        [
            create_inline_button("📚 Команды", "help_commands"),
            create_inline_button("🏠 Категории", "help_categories")
        ],
        [
            create_inline_button("❓ О боте", "help_about")
        ]
    ]
    return create_inline_keyboard(keyboard)


# Словарь соответствия названий категорий и их ID
CATEGORY_NAMES = {
    "apartments": "Квартиры",
    "houses": "Дома",
    "rooms": "Комнаты",
    "garages": "Гаражи",
    "dacha": "Дачи",
    "land": "Земельные участки",
    "commercial": "Коммерческая недвижимость"
}

# Словарь соответствия ID категорий и их описаний
CATEGORY_DESCRIPTIONS = {
    "apartments_sale": "Продажа квартир",
    "apartments_rent": "Аренда квартир",
    "houses_sale": "Продажа домов",
    "houses_rent": "Аренда домов",
    "rooms_sale": "Продажа комнат",
    "rooms_rent": "Аренда комнат",
    "garages_sale": "Продажа гаражей",
    "garages_rent": "Аренда гаражей",
    "dacha_sale": "Продажа дач",
    "dacha_rent": "Аренда дач",
    "land_sale": "Продажа земельных участков",
    "commercial_sale": "Продажа коммерческой недвижимости",
    "commercial_rent": "Аренда коммерческой недвижимости"
}

# Словарь эмодзи для категорий
CATEGORY_EMOJI = {
    "apartments_sale": "💰 🏢",
    "apartments_rent": "💸 🏢",
    "houses_sale": "💰 🏠",
    "houses_rent": "💸 🏠",
    "rooms_sale": "💰 🚪",
    "rooms_rent": "💸 🚪",
    "garages_sale": "💰 🚗",
    "garages_rent": "💸 🚗",
    "dacha_sale": "💰 🏡",
    "dacha_rent": "💸 🏡",
    "land_sale": "💰 🌳",
    "commercial_sale": "💰 🏭",
    "commercial_rent": "💸 🏭"
}


def get_admin_menu() -> ReplyKeyboardMarkup:
    """
    Создает меню администратора.

    Returns:
        ReplyKeyboardMarkup: Клавиатура с кнопками меню администратора
    """
    keyboard = [
        [create_keyboard_button("👥 Пользователи"), create_keyboard_button("📝 Запросы")],
        [create_keyboard_button("📊 Статистика"), create_keyboard_button("📢 Рассылка")],
        [create_keyboard_button("🔔 Настройки уведомлений"), create_keyboard_button("◀️ Назад в главное меню")]
    ]
    return create_reply_keyboard(keyboard)


def get_broadcast_menu() -> InlineKeyboardMarkup:
    """
    Создает меню выбора группы для рассылки.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками выбора группы для рассылки
    """
    keyboard = [
        [
            create_inline_button("👥 Всем пользователям", "broadcast_all"),
        ],
        [
            create_inline_button("👤 Только пользователям", "broadcast_users"),
            create_inline_button("👑 Только администраторам", "broadcast_admins")
        ]
    ]
    return create_inline_keyboard(keyboard)


def get_users_menu() -> InlineKeyboardMarkup:
    """
    Создает меню управления пользователями.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками управления пользователями
    """
    keyboard = [
        [
            create_inline_button("📋 Список пользователей", "admin_users_list"),
            create_inline_button("🔒 Заблокированные", "admin_users_blocked")
        ],
        [
            create_inline_button("➕ Добавить пользователя", "admin_users_add"),
            create_inline_button("➖ Удалить пользователя", "admin_users_remove")
        ]
    ]
    return create_inline_keyboard(keyboard)


def get_requests_menu() -> InlineKeyboardMarkup:
    """
    Создает меню управления запросами на доступ.

    Returns:
        InlineKeyboardMarkup: Клавиатура с кнопками управления запросами
    """
    keyboard = [
        [
            create_inline_button("📋 Ожидающие запросы", "admin_requests_pending"),
            create_inline_button("✅ Одобренные запросы", "admin_requests_approved")
        ],
        [
            create_inline_button("❌ Отклоненные запросы", "admin_requests_rejected"),
            create_inline_button("🔄 Обновить", "admin_requests_refresh")
        ]
    ]
    return create_inline_keyboard(keyboard)


def get_notification_settings_keyboard() -> InlineKeyboardMarkup:
    """
    Создает inline-клавиатуру с кнопками подписки/отписки от уведомлений.

    Returns:
        InlineKeyboardMarkup: Inline-клавиатура с кнопками подписки/отписки
    """
    keyboard = [
        [
            create_inline_button("🔔 Подписаться на запросы", "admin_subscribe_requests"),
            create_inline_button("🔕 Отписаться от запросов", "admin_unsubscribe_requests")
        ]
    ]
    return create_inline_keyboard(keyboard)