"""
Точка входа для запуска Telegram бота.
"""
import asyncio
import logging
import os
import sys
from pathlib import Path

# Добавляем корневую директорию проекта в sys.path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.application.services.property import PropertyService
from src.config.paths import get_data_path
from src.config.settings import load_telegram_config, load_olx_config
from src.domain.services.property_validator import PropertyValidator
from src.infrastructure.external.telegram.notification_sender import TelegramNotificationSender
from src.infrastructure.parsers.olx.olx_parser import OlxParser
from src.infrastructure.persistence.processed_ad_storage import DbProcessedAdStorage
from src.infrastructure.storage.json_storage import JsonStorage
from src.presentation.telegram.bot import PropertyBot
from src.presentation.telegram.localization import create_localization_services, create_localized_notification_service


async def main():
    """
    Основная функция для запуска бота.
    """
    # Настраиваем логирование
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    logger = logging.getLogger("telegram_bot")

    # Загружаем конфигурацию
    telegram_config = load_telegram_config()

    # Проверяем наличие токена в переменных окружения
    env_token = os.environ.get("TELEGRAM_BOT_TOKEN")
    config_token = telegram_config.get("token")

    # Выбираем токен из переменных окружения или из конфигурации
    token = env_token or config_token

    # Логируем информацию о том, откуда взят токен
    if env_token:
        logger.info("Токен Telegram бота взят из переменных окружения")
    elif config_token:
        logger.info("Токен Telegram бота взят из конфигурации")

    if not token:
        logger.error("Не указан токен Telegram бота. Проверьте переменные окружения или файл конфигурации")
        return

    # Получаем список разрешенных пользователей
    allowed_users = telegram_config.get("allowed_users", [])

    # Получаем список администраторов
    admin_users = telegram_config.get("admin_users", [])

    # Получаем ID суперадминистраторов
    super_admin_ids = []

    # Проверяем наличие списка суперадминистраторов
    if "super_admin_ids" in telegram_config and isinstance(telegram_config["super_admin_ids"], list):
        super_admin_ids = [int(admin_id) for admin_id in telegram_config["super_admin_ids"] if isinstance(admin_id, int)]

    # Обратная совместимость с предыдущей версией
    if not super_admin_ids and "super_admin_id" in telegram_config and telegram_config["super_admin_id"]:
        super_admin_id = telegram_config["super_admin_id"]
        if isinstance(super_admin_id, int):
            super_admin_ids = [super_admin_id]

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=token)

    # Создаем сервисы локализации
    localization_service, user_language_service = create_localization_services()

    # Создаем локализованный сервис уведомлений
    notification_service = create_localized_notification_service(
        notification_sender=notification_sender,
        localization_service=localization_service,
        user_language_service=user_language_service,
        logger=logger
    )

    # Создаем хранилище обработанных объявлений
    storage = JsonStorage(get_data_path("processed_ads.json"))
    processed_ad_storage = DbProcessedAdStorage(storage, logger)

    # Создаем парсер OLX
    olx_config = load_olx_config()
    olx_parser = OlxParser(config=olx_config, processed_ad_storage=processed_ad_storage, logger=logger)

    # Создаем валидатор объектов недвижимости
    property_validator = PropertyValidator()

    # Создаем сервис работы с объектами недвижимости
    property_service = PropertyService(
        parser=olx_parser,
        processed_ad_storage=processed_ad_storage,
        notification_service=notification_service,
        property_validator=property_validator,
        logger=logger
    )

    # Создаем и запускаем бота
    bot = PropertyBot(
        token=token,
        property_service=property_service,
        allowed_users=allowed_users,
        admin_users=admin_users,
        super_admin_ids=super_admin_ids,
        logger=logger,
        localization_service=localization_service,
        user_language_service=user_language_service
    )

    # Устанавливаем глобальный экземпляр бота для доступа из других модулей
    import src.presentation.telegram.bot as bot_module
    bot_module.bot_instance = bot

    try:
        logger.info("Запуск бота...")
        await bot.start()

        # Держим бота запущенным до нажатия Ctrl+C
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Получен сигнал остановки")
    finally:
        # Останавливаем бота
        await bot.stop()
        logger.info("Бот остановлен")


if __name__ == "__main__":
    # Запускаем асинхронную функцию main
    asyncio.run(main())
