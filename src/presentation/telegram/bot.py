"""
Telegram бот для уведомлений о новых объявлениях.
"""
import logging
from typing import Dict, List, Optional

# Глобальный экземпляр бота для доступа из других модулей
bot_instance = None

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import CallbackQueryHandler, CommandHandler, MessageHandler, filters

from src.application.services.localization import LocalizationService
from src.application.services.property import PropertyService
from src.application.services.user_language import UserLanguageService
from src.infrastructure.persistence.access_requests import AccessRequestStorage
from src.presentation.telegram.handlers import (handle_admin, handle_broadcast, handle_category,
                                              handle_error, handle_help,
                                              handle_requests, handle_start, handle_stats,
                                              handle_status, handle_stop, handle_users)
from src.presentation.telegram.localization import create_localization_services, register_language_handlers
from src.presentation.telegram.menu import (get_broadcast_menu, get_categories_menu,
                                          get_main_menu, get_notifications_menu, get_requests_menu,
                                          get_users_menu, get_notification_settings_keyboard)


class PropertyBot:
    """
    Telegram бот для уведомлений о новых объявлениях.
    """

    @staticmethod
    async def safe_reply_text(message, text, reply_markup=None, parse_mode='HTML'):
        """
        Безопасный вызов метода reply_text с корректными типами параметров.
        """
        # Проверяем, что reply_markup не None
        if reply_markup is not None:
            # Логируем тип reply_markup для отладки
            print(f"Тип reply_markup: {type(reply_markup)}")

        return await message.reply_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode,
            quote=True,
            entities=[],
            api_kwargs={}
        )

    @staticmethod
    async def safe_answer(query, text):
        """
        Безопасный вызов метода answer с корректными типами параметров.
        """
        kwargs = {"text": text}
        return await query.answer(**kwargs)

    @staticmethod
    async def safe_edit_message_text(bot, chat_id, message_id, text, reply_markup=None, parse_mode='HTML'):
        """
        Безопасный вызов метода edit_message_text с корректными типами параметров.
        """
        # Логируем тип reply_markup для отладки
        if reply_markup is not None:
            print(f"Тип reply_markup в safe_edit_message_text: {type(reply_markup)}")

        kwargs = {
            "text": text,
            "chat_id": chat_id,
            "message_id": message_id,
            "parse_mode": parse_mode,
        }
        if reply_markup is not None:
            kwargs["reply_markup"] = reply_markup

        try:
            return await bot.edit_message_text(**kwargs)
        except Exception as e:
            print(f"Ошибка в safe_edit_message_text: {e}")
            # Пробуем отправить сообщение напрямую без parse_mode
            try:
                return await bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=None
                )
            except Exception as e2:
                print(f"Вторая ошибка в safe_edit_message_text: {e2}")
                return None

    @staticmethod
    async def safe_edit_message_text_from_query(bot, query, text, reply_markup=None, parse_mode='HTML'):
        """
        Безопасный вызов метода edit_message_text с использованием объекта query.
        """
        if query.message is None:
            return None

        # Логируем тип reply_markup для отладки
        if reply_markup is not None:
            print(f"Тип reply_markup в safe_edit_message_text_from_query: {type(reply_markup)}")

        kwargs = {
            "bot": bot,
            "chat_id": query.message.chat_id,
            "message_id": query.message.message_id,
            "text": text,
            "parse_mode": parse_mode,
        }
        if reply_markup is not None:
            kwargs["reply_markup"] = reply_markup

        try:
            return await PropertyBot.safe_edit_message_text(**kwargs)
        except Exception as e:
            print(f"Ошибка в safe_edit_message_text_from_query: {e}")
            # Пробуем отправить сообщение напрямую
            try:
                return await bot.edit_message_text(
                    chat_id=query.message.chat_id,
                    message_id=query.message.message_id,
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
            except Exception as e2:
                print(f"Вторая ошибка в safe_edit_message_text_from_query: {e2}")
                return None

    @staticmethod
    async def safe_send_message(bot, chat_id, text, reply_markup=None, parse_mode='HTML'):
        """
        Безопасный вызов метода send_message с корректными типами параметров.
        """
        kwargs = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode,
        }
        if reply_markup is not None:
            kwargs["reply_markup"] = reply_markup
        return await bot.send_message(**kwargs)

    @staticmethod
    async def safe_edit_message_reply_markup(bot, chat_id, message_id, reply_markup=None):
        """
        Безопасный вызов метода edit_message_reply_markup с корректными типами параметров.

        Args:
            bot: Объект бота
            chat_id: ID чата
            message_id: ID сообщения
            reply_markup: Клавиатура (опционально)

        Returns:
            Результат вызова метода edit_message_reply_markup
        """
        kwargs = {
            "chat_id": chat_id,
            "message_id": message_id,
        }
        if reply_markup is not None:
            kwargs["reply_markup"] = reply_markup

        try:
            return await bot.edit_message_reply_markup(**kwargs)
        except Exception as e:
            print(f"Ошибка в safe_edit_message_reply_markup: {e}")
            return None

    @staticmethod
    def safe_inline_keyboard_button(text, callback_data=None, url=None):
        """
        Безопасное создание объекта InlineKeyboardButton с корректными типами параметров.
        """
        # Дополнительное логирование для отладки
        print(f"DEBUG: Создание кнопки с текстом: '{text}', callback_data: {callback_data}, url: {url}")

        kwargs = {"text": text}
        if callback_data is not None:
            kwargs["callback_data"] = callback_data
        if url is not None:
            kwargs["url"] = url

        button = InlineKeyboardButton(**kwargs)
        print(f"DEBUG: Создана кнопка: {button}")
        return button

    def __init__(self, token: str, property_service: PropertyService,
                allowed_users: Optional[List[int]] = None,
                admin_users: Optional[List[int]] = None,
                super_admin_ids: Optional[List[int]] = None,
                logger: Optional[logging.Logger] = None,
                localization_service: Optional[LocalizationService] = None,
                user_language_service: Optional[UserLanguageService] = None):
        """
        Инициализирует Telegram бота.

        Args:
            token: Токен Telegram бота
            property_service: Сервис работы с объектами недвижимости
            allowed_users: Список разрешенных пользователей (опционально)
            admin_users: Список администраторов (опционально)
            super_admin_ids: ID суперадминистраторов, которые могут управлять администраторами (опционально)
            logger: Логгер (опционально)
            localization_service: Сервис локализации (опционально)
            user_language_service: Сервис управления языком пользователя (опционально)
        """
        self.token = token
        self.property_service = property_service
        self.allowed_users = allowed_users or []
        self.admin_users = admin_users or []
        self.super_admin_ids = super_admin_ids or []
        self.logger = logger or logging.getLogger(__name__)

        # Создаем хранилище запросов на доступ
        self.access_requests = AccessRequestStorage(logger=self.logger)

        # Словарь для хранения ID сообщений с запросами на доступ
        # Формат: {user_id: {admin_id: message_id, ...}, ...}
        self.access_request_messages = {}

        # Создаем бота через ApplicationBuilder
        from telegram.ext import ApplicationBuilder
        self.application = ApplicationBuilder().token(token).build()

        # Инициализируем сервисы локализации
        if localization_service is None or user_language_service is None:
            self.localization_service, self.user_language_service = create_localization_services()
        else:
            self.localization_service = localization_service
            self.user_language_service = user_language_service

        # Регистрируем обработчики
        self._register_handlers()

        # Статус бота
        self.running = False

        # Активные категории для парсинга
        self.active_categories: Dict[int, List[str]] = {}

        # Загружаем список разрешенных пользователей из файла
        self._load_allowed_users()

        # Загружаем активные категории из файла
        self._load_active_categories()

    def _register_handlers(self) -> None:
        """
        Регистрирует обработчики команд.
        """
        # Проверяем, что application не None
        if self.application is None:
            self.logger.error("Не удалось зарегистрировать обработчики: application is None")
            return

        # Регистрируем обработчики локализации
        register_language_handlers(self.application, self.localization_service, self.user_language_service)

        # Обработчики команд
        self.application.add_handler(CommandHandler('start', self._handle_start, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('help', self._handle_help, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('status', self._handle_status, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('stop', self._handle_stop, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('category', self._handle_category, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('filter', self._handle_filter, filters=filters.COMMAND))

        # Обработчики команд администратора
        self.application.add_handler(CommandHandler('admin', self._handle_admin, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('users', self._handle_users, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('requests', self._handle_requests, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('broadcast', self._handle_broadcast, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('stats', self._handle_stats, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('allow', self._handle_allow, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('disallow', self._handle_disallow, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('admininfo', self._handle_admin_info, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('addadmin', self._handle_add_admin, filters=filters.COMMAND))
        self.application.add_handler(CommandHandler('removeadmin', self._handle_remove_admin, filters=filters.COMMAND))

        # Обработчик кнопок
        self.application.add_handler(CallbackQueryHandler(self._handle_callback, pattern=".*"))

        # Обработчик ошибок
        self.application.add_error_handler(self._handle_error)

        # Обработчик текстовых сообщений
        # В python-telegram-bot 20.3 фильтры изменились
        # Используем фильтр для текстовых сообщений, не являющихся командами
        # В новой версии используются фильтры в нижнем регистре
        # Используем MessageHandler с фильтром для текстовых сообщений
        from telegram.ext.filters import TEXT
        self.application.add_handler(MessageHandler(TEXT, self._handle_text))

    async def _handle_start(self, update: Update, context) -> None:
        """
        Обрабатывает команду /start.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Получаем сообщение
        message = update.message
        if not message or not message.from_user:
            return

        # Получаем ID пользователя
        user_id = message.from_user.id
        user_name = message.from_user.first_name

        # Проверяем, установлен ли язык пользователя
        language = self.user_language_service.get_language(user_id)

        # Проверяем, первый ли это запуск бота для пользователя
        # Если язык не установлен явно (используется язык по умолчанию),
        # предлагаем выбрать язык
        is_first_launch = language == self.user_language_service._default_language and not self._user_has_set_language(user_id)

        if is_first_launch:
            # Получаем локализованное приветственное сообщение на русском и украинском языках
            ru_welcome = self.localization_service.get_text('welcome_message', 'ru') or "Привет! Добро пожаловать в бот! 👋\nВыберите язык:"
            ua_welcome = self.localization_service.get_text('welcome_message', 'ua') or "Привіт! Вітаємо у боті! 👋\nОберіть мову:"

            # Объединяем приветствия на обоих языках
            welcome_text = f"{ua_welcome}\n\n{ru_welcome}"

            # Создаем меню выбора языка
            from src.presentation.telegram.localization.menu import get_localized_language_menu
            await PropertyBot.safe_reply_text(
                message,
                welcome_text,
                get_localized_language_menu(self.localization_service, self.user_language_service, user_id)
            )
            return

        # Если язык не установлен, определяем его на основе языка Telegram
        if language == self.user_language_service._default_language:
            # Получаем язык пользователя из Telegram
            telegram_language = message.from_user.language_code

            # Определяем язык на основе языка Telegram
            detected_language = self.user_language_service.detect_language(telegram_language)

            # Устанавливаем язык пользователя
            self.user_language_service.set_language(user_id, detected_language)

            # Обновляем язык
            language = detected_language

            self.logger.info(f"Автоматически определен язык пользователя {user_id}: {language} (на основе {telegram_language})")

        # Получаем локализованный шаблон
        template = self.localization_service.get_template('command_start', language)

        # Если шаблон не найден, используем стандартный обработчик
        if not template:
            await handle_start(update, context, self)
            return

        # Форматируем сообщение
        text = self.localization_service.format_template(template, {'name': user_name})

        # Отправляем сообщение с локализованным меню
        from src.presentation.telegram.localization.menu import get_localized_main_menu
        await PropertyBot.safe_reply_text(
            message,
            text,
            get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
        )

    def _user_has_set_language(self, user_id: int) -> bool:
        """
        Проверяет, установил ли пользователь язык явно.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если пользователь установил язык явно
        """
        # Проверяем, есть ли запись о языке пользователя в хранилище
        storage = getattr(self.user_language_service, '_user_language_storage', None)
        if storage and hasattr(storage, '_languages'):
            return user_id in storage._languages
        return False

    async def _handle_help(self, update: Update, context) -> None:
        """
        Обрабатывает команду /help.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Получаем сообщение
        message = update.message
        if not message or not message.from_user:
            return

        # Получаем ID пользователя
        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self.user_language_service.get_language(user_id)

        # Получаем локализованный текст
        text = self.localization_service.get_text('help_menu_title', language)

        # Если текст не найден, используем стандартный обработчик
        if not text:
            await handle_help(update, context, self)
            return

        # Отправляем сообщение с локализованным меню
        from src.presentation.telegram.localization.menu import get_localized_help_menu
        await PropertyBot.safe_reply_text(
            message,
            text,
            get_localized_help_menu(self.localization_service, self.user_language_service, user_id)
        )

    async def _handle_status(self, update: Update, context) -> None:
        """
        Обрабатывает команду /status.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Получаем сообщение
        message = update.message
        if not message or not message.from_user:
            return

        # Получаем ID пользователя
        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self.user_language_service.get_language(user_id)

        # Получаем категории пользователя
        categories = self.get_categories(user_id)

        # Формируем информацию о категориях
        if categories:
            categories_info = self.localization_service.get_text('categories_active', language)
            if not categories_info:
                categories_info = "Активные категории:"

            for category in categories:
                # Получаем локализованное описание категории
                category_parts = category.split('_')
                if len(category_parts) == 2:
                    category_type, operation = category_parts
                    category_name = self.localization_service.get_text(f'category_{category_type}', language)
                    operation_name = self.localization_service.get_text(f'category_{operation}', language)
                    if category_name and operation_name:
                        description = f"{operation_name} {category_name}"
                    else:
                        from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI
                        description = CATEGORY_DESCRIPTIONS.get(category, category)
                        emoji = CATEGORY_EMOJI.get(category, "")
                        description = f"{emoji} {description}"
                else:
                    from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI
                    description = CATEGORY_DESCRIPTIONS.get(category, category)
                    emoji = CATEGORY_EMOJI.get(category, "")
                    description = f"{emoji} {description}"

                categories_info += f"\n- {description}"
        else:
            categories_info = self.localization_service.get_text('categories_empty', language)
            if not categories_info:
                categories_info = "У вас нет активных категорий."

        # Получаем локализованный шаблон
        template = self.localization_service.get_template('command_status', language)

        # Если шаблон не найден, используем стандартный обработчик
        if not template:
            await handle_status(update, context, self)
            return

        # Форматируем сообщение
        status_text = self.localization_service.get_text('status_running', language)
        if not status_text:
            status_text = "Запущен"

        text = self.localization_service.format_template(template, {
            'status': status_text,
            'categories_info': categories_info
        })

        # Отправляем сообщение с локализованным меню
        from src.presentation.telegram.localization.menu import get_localized_main_menu
        await PropertyBot.safe_reply_text(
            message,
            text,
            get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
        )

    async def _handle_stop(self, update: Update, context) -> None:
        """
        Обрабатывает команду /stop.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Получаем сообщение
        message = update.message
        if not message or not message.from_user:
            return

        # Получаем ID пользователя
        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self.user_language_service.get_language(user_id)

        # Удаляем все категории пользователя
        if user_id in self.active_categories:
            self.active_categories[user_id] = []
            # Сохраняем изменения
            self._save_active_categories()

        # Получаем локализованный текст
        text = self.localization_service.get_text('notifications_stopped', language)

        # Если текст не найден, используем стандартный обработчик
        if not text:
            await handle_stop(update, context, self)
            return

        # Отправляем сообщение с локализованным меню
        from src.presentation.telegram.localization.menu import get_localized_categories_menu

        # Создаем клавиатуру
        keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

        # Отправляем сообщение с клавиатурой
        try:
            await PropertyBot.safe_reply_text(
                message,
                text,
                reply_markup=keyboard
            )
        except Exception as e:
            self.logger.error(f"Ошибка при отправке меню категорий: {e}")
            # Пробуем отправить сообщение напрямую без использования safe_reply_text
            await message.reply_text(
                text=text,
                reply_markup=keyboard
            )

    async def _handle_category(self, update: Update, context) -> None:
        """
        Обрабатывает команду /category.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        await handle_category(update, context, self)

    async def _handle_filter(self, update: Update, _) -> None:
        """
        Обрабатывает команду /filter.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Функционал фильтрации удален
        message = update.message
        if message:
            await PropertyBot.safe_reply_text(
                message,
                "Команда /filter больше не поддерживается.",
                get_main_menu()
            )

    async def _handle_text(self, update: Update, context) -> None:
        """
        Обрабатывает текстовые сообщения.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Параметр context не используется в этой функции, но необходим для совместимости с другими обработчиками
        # Проверяем, разрешен ли пользователь
        if not await self._is_user_allowed(update):
            return

        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем текст сообщения
        text = message.text
        if not text:
            return

        # Проверяем, есть ли в тексте команда с именем бота (@bot_name /command)
        if '@' in text and text.split(' ')[0].startswith('/'):
            # Разбиваем текст на части по пробелу
            parts = text.split(' ')
            # Удаляем имя бота из первой части
            if '@' in parts[0]:
                # Берем только команду без имени бота
                command_parts = parts[0].split('@')
                if len(command_parts) > 0:
                    command = command_parts[0]
                    self.logger.info(f"[Обработка текста] Обнаружена команда с именем бота: {text}")

                    # Перенаправляем на соответствующий обработчик команды
                    if command == '/broadcast':
                        await self._handle_broadcast(update, context)
                        return
                    elif command == '/start':
                        await self._handle_start(update, context)
                        return
                    elif command == '/help':
                        await self._handle_help(update, context)
                        return
                    elif command == '/status':
                        await self._handle_status(update, context)
                        return
                    elif command == '/stop':
                        await self._handle_stop(update, context)
                        return
                    elif command == '/category':
                        await self._handle_category(update, context)
                        return
                    elif command == '/filter':
                        await self._handle_filter(update, context)
                        return
                    elif command == '/admin':
                        await self._handle_admin(update, context)
                        return
                    elif command == '/users':
                        await self._handle_users(update, context)
                        return
                    elif command == '/requests':
                        await self._handle_requests(update, context)
                        return
                    elif command == '/stats':
                        await self._handle_stats(update, context)
                        return

        # Получаем язык пользователя
        user_id = message.from_user.id if message.from_user else 0
        language = self.user_language_service.get_language(user_id)

        # Получаем локализованные тексты для кнопок главного меню
        status_text = self.localization_service.get_text('main_menu_status', language) or "📊 Статус"
        notifications_text = self.localization_service.get_text('main_menu_notifications', language) or "🔔 Уведомления"
        categories_text = self.localization_service.get_text('main_menu_categories', language) or "🏠 Категории"
        help_text = self.localization_service.get_text('main_menu_help', language) or "❓ Помощь"
        language_text = self.localization_service.get_text('main_menu_language', language) or "🌐 Язык"



        # Обрабатываем кнопки главного меню
        # Проверяем, не является ли это кнопкой "Статистика" из меню администратора
        stats_text = self.localization_service.get_text('admin_menu_stats', language) or "📊 Статистика"
        if text == stats_text:
            # Проверяем, является ли пользователь администратором
            if message.from_user and message.from_user.id in self.admin_users:
                await self._handle_stats(update, context)
                return

        if text.startswith(status_text.split(' ')[0]):
            await self._handle_status(update, context)
            return
        elif text == notifications_text:  # Проверяем точное совпадение, а не только начало строки
            # Получаем локализованный текст
            notifications_title = self.localization_service.get_text('notifications_menu_title', language) or "Управление уведомлениями:"

            # Получаем локализованное меню
            from src.presentation.telegram.localization.menu import get_localized_notifications_menu
            await PropertyBot.safe_reply_text(
                message,
                notifications_title,
                get_localized_notifications_menu(self.localization_service, self.user_language_service, user_id)
            )
            return
        elif text == categories_text:  # Проверяем точное совпадение, а не только начало строки
            # Получаем локализованный текст
            categories_title = self.localization_service.get_text('categories_menu_title', language) or "Выберите категорию недвижимости:"

            # Получаем локализованное меню
            from src.presentation.telegram.localization.menu import get_localized_categories_menu

            # Создаем клавиатуру
            keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

            # Отправляем сообщение с клавиатурой
            try:
                await PropertyBot.safe_reply_text(
                    message,
                    categories_title,
                    reply_markup=keyboard
                )
            except Exception as e:
                self.logger.error(f"Ошибка при отправке меню категорий: {e}")
                # Пробуем отправить сообщение напрямую без использования safe_reply_text
                await message.reply_text(
                    text=categories_title,
                    reply_markup=keyboard
                )
            return
        # Обработка кнопки "Настройки" удалена
        elif text.startswith(help_text.split(' ')[0]):
            await self._handle_help(update, context)
            return
        elif text.startswith(language_text.split(' ')[0]):
            # Получаем локализованные тексты
            language_title = self.localization_service.get_text('language_menu_title', language) or "Выберите язык:"

            # Получаем информацию о текущем языке
            current_language_text = self.localization_service.get_text('current_language', language)

            # Получаем название текущего языка для отображения
            current_language_name = ""
            for lang in self.user_language_service.get_available_languages():
                if lang.code == language:
                    current_language_name = str(lang)
                    break

            # Формируем полный текст сообщения с информацией о текущем языке
            full_message = language_title
            if current_language_text and current_language_name:
                # Форматируем текст о текущем языке
                formatted_text = current_language_text.format(language=current_language_name)
                full_message = f"{formatted_text}\n\n{language_title}"

            # Принудительно перезагружаем языковые настройки пользователей
            if hasattr(self.user_language_service, '_user_language_storage') and hasattr(self.user_language_service._user_language_storage, '_load'):
                try:
                    self.user_language_service._user_language_storage._load()
                    self.logger.info(f"Языковые настройки пользователей успешно перезагружены при открытии меню языка для пользователя {user_id}")
                except Exception as e:
                    self.logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

            # Получаем локализованное меню с обычными кнопками (не инлайн)
            from src.presentation.telegram.localization.menu import get_localized_language_keyboard

            # Отправляем меню выбора языка с обычными кнопками
            await PropertyBot.safe_reply_text(
                message,
                full_message,
                get_localized_language_keyboard(self.localization_service, self.user_language_service, user_id)
            )

            # Логируем информацию об открытии меню выбора языка
            self.logger.info(f"Открыто меню выбора языка для пользователя {user_id}")
            return

        # Обрабатываем кнопки меню администратора
        # Получаем локализованные тексты для кнопок меню администратора
        users_text = self.localization_service.get_text('admin_menu_users', language) or "👥 Пользователи"
        requests_text = self.localization_service.get_text('admin_menu_requests', language) or "📝 Запросы"
        stats_text = self.localization_service.get_text('admin_menu_stats', language) or "📊 Статистика"
        broadcast_text = self.localization_service.get_text('admin_menu_broadcast', language) or "📢 Рассылка"
        notification_settings_text = self.localization_service.get_text('admin_menu_notification_settings', language) or "🔔 Настройки уведомлений"

        if text == users_text:
            # Проверяем, является ли пользователь администратором
            if message.from_user and message.from_user.id in self.admin_users:
                await self._handle_users(update, context)
            return
        elif text == requests_text:
            # Проверяем, является ли пользователь администратором
            if message.from_user and message.from_user.id in self.admin_users:
                await self._handle_requests(update, context)
            return
        # Обработка кнопки "Статистика" перенесена выше
        elif text == broadcast_text:
            # Проверяем, является ли пользователь администратором
            if message.from_user and message.from_user.id in self.admin_users:
                # Получаем локализованный текст
                broadcast_title = self.localization_service.get_text('broadcast_menu_title', language) or "Выберите группу пользователей для рассылки или используйте команду:\n/broadcast [all|users|admins] [текст сообщения]\n\nПример: /broadcast admins Завтра сборы"

                # Получаем локализованное меню
                from src.presentation.telegram.localization.menu import get_localized_broadcast_menu
                await PropertyBot.safe_reply_text(
                    message,
                    broadcast_title,
                    get_localized_broadcast_menu(self.localization_service, self.user_language_service, user_id)
                )
            return
        elif text == notification_settings_text:
            # Проверяем, является ли пользователь администратором
            if message.from_user and message.from_user.id in self.admin_users:
                # Получаем ID пользователя
                user_id = message.from_user.id

                # Проверяем, подписан ли администратор на уведомления о новых пользователях
                is_subscribed = user_id in self.access_requests.get_notification_subscribers()

                # Получаем локализованные тексты
                settings_title = self.localization_service.get_text('notification_settings_title', language) or "Настройки уведомлений:"
                status_enabled = self.localization_service.get_text('notification_status_enabled', language) or "Включены"
                status_disabled = self.localization_service.get_text('notification_status_disabled', language) or "Отключены"
                access_requests_label = self.localization_service.get_text('notification_access_requests', language) or "Уведомления о запросах на доступ"

                # Формируем текст сообщения
                status_text = status_enabled if is_subscribed else status_disabled
                message_text = f"{settings_title}\n\n{access_requests_label}: {status_text}\n\nВыберите действие:"

                # Получаем локализованное меню с инлайн-кнопками
                from src.presentation.telegram.localization.menu import get_localized_notification_settings_menu

                # Создаем инлайн-клавиатуру с кнопками
                inline_keyboard = get_localized_notification_settings_menu(
                    self.localization_service,
                    self.user_language_service,
                    user_id
                )

                # Отправляем сообщение с инлайн-кнопками, сохраняя текущую клавиатуру
                try:
                    # Используем reply_text вместо send_message, чтобы сохранить текущую клавиатуру
                    await message.reply_text(
                        text=message_text,
                        reply_markup=inline_keyboard,
                        parse_mode='HTML'
                    )
                    self.logger.info(f"Отправлено сообщение с настройками уведомлений пользователю {user_id}")
                except Exception as e:
                    self.logger.error(f"Ошибка при отправке сообщения с настройками уведомлений: {e}")
                    # Пробуем отправить сообщение напрямую
                    try:
                        await self.application.bot.send_message(
                            chat_id=message.chat_id,
                            text=message_text,
                            reply_markup=inline_keyboard
                        )
                        self.logger.info(f"Отправлено сообщение с настройками уведомлений напрямую пользователю {user_id}")
                    except Exception as e2:
                        self.logger.error(f"Вторая ошибка при отправке сообщения с настройками уведомлений: {e2}")
            return
        # Получаем локализованный текст для кнопки "Назад в главное меню"
        back_text = self.localization_service.get_text('notifications_menu_back', language) or "◀️ Назад в главное меню"

        if text == back_text:
            # Получаем локализованное меню
            from src.presentation.telegram.localization.menu import get_localized_main_menu
            # Получаем локализованный текст для заголовка главного меню
            main_menu_title = self.localization_service.get_text('main_menu_title', language) or "Главное меню:"
            await PropertyBot.safe_reply_text(
                message,
                main_menu_title,
                get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
            )
            return

        # Получаем локализованные тексты для кнопок выбора языка
        ru_text = self.localization_service.get_text('language_ru', language) or "🇷🇺 Русский"
        ua_text = self.localization_service.get_text('language_ua', language) or "🇺🇦 Українська"

        # Удаляем отметку текущего языка, если она есть
        ru_text_clean = ru_text.replace(" ✓", "")
        ua_text_clean = ua_text.replace(" ✓", "")

        # Обрабатываем кнопки выбора языка
        if text.startswith(ru_text_clean):
            # Если текущий язык не русский, меняем его
            if language != 'ru':
                # Устанавливаем русский язык
                self.user_language_service.set_language(user_id, 'ru')
                self.logger.info(f"Язык пользователя {user_id} изменен на русский")

                # Получаем локализованный текст подтверждения
                success_message = self.localization_service.get_text('language_changed', 'ru') or "Язык изменен на {language}"
                success_message = success_message.format(language="Русский")

                # Отправляем сообщение с обновленным меню
                from src.presentation.telegram.localization.menu import get_localized_main_menu
                main_menu_title = self.localization_service.get_text('main_menu_title', 'ru') or "Главное меню:"

                await PropertyBot.safe_reply_text(
                    message,
                    f"{success_message}\n\n{main_menu_title}",
                    get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
                )
            else:
                # Если язык уже русский, просто возвращаемся в главное меню
                from src.presentation.telegram.localization.menu import get_localized_main_menu
                main_menu_title = self.localization_service.get_text('main_menu_title', language) or "Главное меню:"

                await PropertyBot.safe_reply_text(
                    message,
                    main_menu_title,
                    get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
                )
            return

        elif text.startswith(ua_text_clean):
            # Если текущий язык не украинский, меняем его
            if language != 'ua':
                # Устанавливаем украинский язык
                self.user_language_service.set_language(user_id, 'ua')
                self.logger.info(f"Язык пользователя {user_id} изменен на украинский")

                # Получаем локализованный текст подтверждения
                success_message = self.localization_service.get_text('language_changed', 'ua') or "Мову змінено на {language}"
                success_message = success_message.format(language="Українська")

                # Отправляем сообщение с обновленным меню
                from src.presentation.telegram.localization.menu import get_localized_main_menu
                main_menu_title = self.localization_service.get_text('main_menu_title', 'ua') or "Головне меню:"

                await PropertyBot.safe_reply_text(
                    message,
                    f"{success_message}\n\n{main_menu_title}",
                    get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
                )
            else:
                # Если язык уже украинский, просто возвращаемся в главное меню
                from src.presentation.telegram.localization.menu import get_localized_main_menu
                main_menu_title = self.localization_service.get_text('main_menu_title', language) or "Головне меню:"

                await PropertyBot.safe_reply_text(
                    message,
                    main_menu_title,
                    get_localized_main_menu(self.localization_service, self.user_language_service, user_id)
                )
            return

        # Получаем локализованные тексты для кнопок меню уведомлений
        enable_text = self.localization_service.get_text('notifications_menu_enable', language) or "🔔 Включить уведомления"
        disable_text = self.localization_service.get_text('notifications_menu_disable', language) or "🔕 Отключить уведомления"

        # Обрабатываем кнопки меню уведомлений
        if text == enable_text:
            # Проверяем наличие пользователя
            if not message.from_user:
                await PropertyBot.safe_reply_text(
                    message,
                    "Не удалось определить пользователя.",
                    None
                )
                return

            # Инициализируем список категорий пользователя, если он не существует
            user_id = message.from_user.id
            if user_id not in self.active_categories:
                self.active_categories[user_id] = []

            # Получаем локализованный текст
            categories_title = self.localization_service.get_text('categories_menu_title', language) or "Выберите категорию недвижимости:"

            # Получаем локализованное меню
            from src.presentation.telegram.localization.menu import get_localized_categories_menu

            # Создаем клавиатуру
            keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

            # Отправляем сообщение с клавиатурой
            try:
                await PropertyBot.safe_reply_text(
                    message,
                    categories_title,
                    reply_markup=keyboard
                )
            except Exception as e:
                self.logger.error(f"Ошибка при отправке меню категорий: {e}")
                # Пробуем отправить сообщение напрямую без использования safe_reply_text
                await message.reply_text(
                    text=categories_title,
                    reply_markup=keyboard
                )
            return
        elif text == disable_text:
            await self._handle_stop(update, context)
            return

        # Обработка кнопок меню настроек удалена

        # Проверяем, есть ли у пользователя сохраненные действия в контексте
        if hasattr(self, 'user_data') and message.from_user and message.from_user.id in self.user_data:
            user_id = message.from_user.id

            # Проверяем наличие целевой группы для рассылки
            if 'broadcast_target' in self.user_data[user_id]:
                # Получаем целевую группу
                target_group = self.user_data[user_id]['broadcast_target']
                self.logger.info(f"[Рассылка] Найдена сохраненная целевая группа {target_group} для пользователя {user_id}")

                # Используем текст сообщения как текст для рассылки
                broadcast_text = text

                # Удаляем сохраненную целевую группу
                del self.user_data[user_id]['broadcast_target']
                self.logger.info(f"[Рассылка] Удалена сохраненная целевая группа для пользователя {user_id}")

                # Вызываем обработчик рассылки
                # Создаем контекст с аргументами
                from telegram.ext import CallbackContext
                ctx = CallbackContext.from_update(update, self.application)
                ctx.args = [target_group, broadcast_text]

                # Вызываем обработчик рассылки
                await handle_broadcast(update, ctx, self)
                return

            # Проверяем наличие действия для добавления пользователя
            elif 'user_action' in self.user_data[user_id]:
                action = self.user_data[user_id]['user_action']

                # Обрабатываем добавление пользователя
                if action == 'add_user':
                    # Удаляем действие из контекста
                    del self.user_data[user_id]['user_action']
                    self.logger.info(f"[Пользователи] Удалено действие 'add_user' для пользователя {user_id}")

                    try:
                        # Пробуем преобразовать текст в ID пользователя
                        new_user_id = int(text.strip())

                        # Создаем контекст с аргументами
                        from telegram.ext import CallbackContext
                        ctx = CallbackContext.from_update(update, self.application)
                        ctx.args = [str(new_user_id)]

                        # Вызываем обработчик добавления пользователя
                        await self._handle_allow(update, ctx)
                    except ValueError:
                        # Если не удалось преобразовать текст в число
                        await PropertyBot.safe_reply_text(
        message,
        "Ошибка: ID пользователя должен быть числом."
                        ,
        None
    )
                    return

                # Обрабатываем удаление пользователя
                elif action == 'remove_user':
                    # Удаляем действие из контекста
                    del self.user_data[user_id]['user_action']
                    self.logger.info(f"[Пользователи] Удалено действие 'remove_user' для пользователя {user_id}")

                    try:
                        # Пробуем преобразовать текст в ID пользователя
                        remove_user_id = int(text.strip())

                        # Создаем контекст с аргументами
                        from telegram.ext import CallbackContext
                        ctx = CallbackContext.from_update(update, self.application)
                        ctx.args = [str(remove_user_id)]

                        # Вызываем обработчик удаления пользователя
                        await self._handle_disallow(update, ctx)
                    except ValueError:
                        # Если не удалось преобразовать текст в число
                        await PropertyBot.safe_reply_text(
        message,
        "Ошибка: ID пользователя должен быть числом."
                        ,
        None
    )
                    return

        # Если сообщение не распознано, отправляем справку
        await PropertyBot.safe_reply_text(
        message,
        "Я не понимаю это сообщение. Используйте команду /help для получения справки."
        ,
        None
    )

    async def _handle_error(self, update: object, context) -> None:
        """
        Обрабатывает ошибки.

        Args:
            update: Объект обновления (может быть любого типа)
            context: Контекст обработчика
        """
        # Преобразуем update в Update, если это возможно
        update_obj = update if isinstance(update, Update) else None
        await handle_error(update_obj, context, self)

    async def _is_user_allowed(self, update: Update) -> bool:
        """
        Проверяет, разрешен ли пользователь.

        Args:
            update: Объект обновления

        Returns:
            bool: True, если пользователь разрешен, иначе False
        """
        # Если список разрешенных пользователей пуст, разрешаем всех
        if not self.allowed_users:
            return True

        # Получаем ID пользователя
        user_id = update.effective_user.id if update.effective_user else None
        if user_id is None:
            return False

        # Проверяем, является ли пользователь администратором
        if user_id in self.admin_users:
            return True

        # Проверяем, разрешен ли пользователь
        if user_id in self.allowed_users:
            return True

        # Проверяем, есть ли одобренный запрос на доступ
        if self.access_requests.is_approved(user_id):
            # Добавляем пользователя в список разрешенных, если его там еще нет
            if user_id not in self.allowed_users:
                self.allowed_users.append(user_id)
                self.logger.info(f"Пользователь {user_id} добавлен в список разрешенных")
                # Сохраняем изменения
                self._save_allowed_users()
            return True

        # Проверяем, есть ли отклоненный запрос на доступ
        if self.access_requests.is_rejected(user_id):
            self.logger.warning(f"Попытка доступа от отклоненного пользователя: {user_id}")

            # Отправляем сообщение о запрете
            if update.message:
                # Получаем язык пользователя
                language = self.user_language_service.get_language(user_id)
                # Получаем локализованный текст
                access_denied_text = self.localization_service.get_text('access_denied', language)

                await PropertyBot.safe_reply_text(
                    update.message,
                    access_denied_text,
                    None
                )
            return False

        # Проверяем, есть ли ожидающий запрос на доступ
        if self.access_requests.is_pending(user_id):
            self.logger.info(f"Попытка доступа от пользователя с ожидающим запросом: {user_id}")

            # Отправляем сообщение о запросе
            if update.message:
                # Получаем язык пользователя
                language = self.user_language_service.get_language(user_id)
                # Получаем локализованный текст
                access_pending_text = self.localization_service.get_text('access_request_pending', language)

                await PropertyBot.safe_reply_text(
                    update.message,
                    access_pending_text,
                    None
                )
            return False

        # Нет запроса на доступ, создаем новый
        self.logger.warning(f"Попытка доступа от неразрешенного пользователя: {user_id}")

        # Создаем запрос на доступ
        user = update.effective_user
        username = ""
        first_name = ""
        if user:
            username = user.username or ""
            first_name = user.first_name or ""

        self.access_requests.add_request(
            user_id=user_id,
            username=username,
            first_name=first_name
        )

        # Отправляем сообщение о запросе
        if update.message:
            # Получаем язык пользователя
            language = self.user_language_service.get_language(user_id)
            # Получаем локализованный текст
            access_sent_text = self.localization_service.get_text('access_request_sent', language)

            await PropertyBot.safe_reply_text(
                update.message,
                access_sent_text,
                None
            )

        # Отправляем уведомление администраторам
        await self._send_access_request_to_admins(user_id, username, first_name)

        return False

    async def _send_access_request_to_admins(self, user_id: int, username: Optional[str], first_name: Optional[str]) -> None:
        """
        Отправляет уведомление администраторам о новом запросе на доступ.

        Args:
            user_id: ID пользователя
            username: Имя пользователя
            first_name: Имя пользователя
        """
        # Формируем сообщение
        message = f"Новый запрос на доступ:\n"
        message += f"ID: {user_id}\n"
        if username:
            message += f"Имя пользователя: @{username}\n"
        if first_name:
            message += f"Имя: {first_name}\n"

        # Создаем кнопки
        keyboard = [
            [
                PropertyBot.safe_inline_keyboard_button("Подтвердить", callback_data=f"approve_{user_id}"),
                PropertyBot.safe_inline_keyboard_button("Отклонить", callback_data=f"reject_{user_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard, api_kwargs={})

        # Инициализируем словарь для хранения ID сообщений для этого пользователя
        self.access_request_messages[user_id] = {}

        # Получаем список администраторов, подписанных на уведомления
        notification_subscribers = self.access_requests.get_notification_subscribers()

        # Если нет подписчиков, отправляем уведомления всем администраторам
        if not notification_subscribers:
            notification_subscribers = self.admin_users

        # Отправляем сообщение только подписанным администраторам
        for admin_id in notification_subscribers:
            # Проверяем, что пользователь является администратором
            if admin_id not in self.admin_users:
                continue

            try:
                # Отправляем сообщение и получаем его ID
                sent_message = await PropertyBot.safe_send_message(self.application.bot, admin_id, message, reply_markup
                )

                # Сохраняем ID сообщения
                self.access_request_messages[user_id][admin_id] = sent_message.message_id

                self.logger.info(f"Уведомление о запросе на доступ отправлено администратору {admin_id} (message_id: {sent_message.message_id})")
            except Exception as e:
                self.logger.error(f"Ошибка при отправке уведомления администратору {admin_id}: {e}")

    async def _handle_callback(self, update: Update, _) -> None:
        """
        Обрабатывает нажатия на кнопки.

        Args:
            update: Объект обновления
            _: Неиспользуемый контекст обработчика
        """

        # Получаем данные кнопки
        query = update.callback_query
        if not query:
            self.logger.warning("Получен пустой callback_query")
            return

        data = query.data
        if not data:
            self.logger.warning("Получены пустые данные кнопки")
            await PropertyBot.safe_answer(query, "Ошибка: пустые данные кнопки")
            return

        # Получаем ID пользователя
        if not query.from_user:
            self.logger.warning("Не удалось определить пользователя")
            await PropertyBot.safe_answer(query, "Ошибка: не удалось определить пользователя")
            return

        admin_id = query.from_user.id

        # Проверяем, является ли пользователь администратором для запросов на доступ
        if data.startswith("approve_") or data.startswith("reject_"):
            if admin_id not in self.admin_users:
                await PropertyBot.safe_answer(query, "У вас нет прав администратора.")
                return

        # Проверяем, разрешен ли пользователь для обычных кнопок
        if not data.startswith("approve_") and not data.startswith("reject_"):
            # Проверяем, есть ли пользователь в списке разрешенных
            user_id = query.from_user.id

            # Если пользователь не администратор и не в списке разрешенных
            if user_id not in self.admin_users and user_id not in self.allowed_users:
                # Если пользователь имеет активные категории, удаляем их
                if user_id in self.active_categories:
                    self.active_categories[user_id] = []
                    # Сохраняем изменения
                    self._save_active_categories()
                    self.logger.info(f"Удалены все категории для пользователя {user_id} при попытке доступа")

                await PropertyBot.safe_answer(query, "Ваш доступ к боту был отозван администратором.")
                return

        # Обрабатываем кнопки меню и подтверждения/отклонения запроса
        try:
            # Обрабатываем кнопки меню рассылки
            if data.startswith("broadcast_"):
                # Проверяем, является ли пользователь администратором
                if admin_id not in self.admin_users:
                    await PropertyBot.safe_answer(query, "У вас нет прав администратора.")
                    return

                # Получаем целевую группу
                target_group = data.split("_")[1]

                # Отвечаем на кнопку
                await PropertyBot.safe_answer(query, f"Выбрана группа: {target_group}")

                # Сохраняем выбранную группу в контексте пользователя
                # Используем user_data для хранения данных пользователя
                if not hasattr(self, 'user_data'):
                    self.user_data = {}

                if admin_id not in self.user_data:
                    self.user_data[admin_id] = {}

                self.user_data[admin_id]['broadcast_target'] = target_group
                self.logger.info(f"[Рассылка] Сохранена целевая группа {target_group} для пользователя {admin_id}")

                # Просим ввести текст сообщения
                message_text = f"Введите текст сообщения для рассылки группе: {target_group}\n\nПросто отправьте сообщение в этот чат"
                try:
                    # Проверяем, что query.message не None перед использованием его атрибутов
                    if query.message is not None:
                        await self.application.bot.edit_message_text(
                            chat_id=query.message.chat_id,
                            message_id=query.message.message_id,
                            text=message_text
                        )
                    else:
                        self.logger.error("Ошибка: query.message является None")
                except Exception as e:
                    self.logger.error(f"Ошибка при редактировании сообщения для рассылки: {e}")
                    # Если не удалось отредактировать, отправляем новое сообщение
                    if query.message is not None:
                        await PropertyBot.safe_send_message(
                            self.application.bot,
                            query.message.chat_id,
                            message_text,
                            None
                        )
                    else:
                        self.logger.error("Не удалось отправить сообщение, так как query.message является None")
                return

            # Продолжаем обработку других кнопок

            # Обрабатываем кнопки управления пользователями
            if data.startswith("admin_users_", None):
                # Проверяем, является ли пользователь администратором
                if admin_id not in self.admin_users:
                    await PropertyBot.safe_answer(query, "У вас нет прав администратора.")
                    return

                action = data.split("_")[2]

                if action == "list":
                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(admin_id)

                    # Получаем локализованные тексты
                    users_list_title = self.localization_service.get_text('users_list_title', language) or "Список пользователей:"
                    users_list_empty = self.localization_service.get_text('users_list_empty', language) or "Список пользователей пуст."
                    subscribed_to_categories = self.localization_service.get_text('subscribed_to_categories', language) or "Подписан на категории:"
                    user_language_text = self.localization_service.get_text('user_language', language) or "Язык бота:"

                    # Получаем список пользователей с дополнительной информацией
                    users_list = ""
                    from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI

                    for i, user_id in enumerate(self.allowed_users, 1):
                        # Получаем имя пользователя из запросов на доступ
                        user_info = self._get_user_info(user_id)
                        user_name = ""
                        if user_info and user_info.get("first_name"):
                            user_name = f"{user_info['first_name']}"

                        # Получаем список категорий пользователя
                        categories = self.get_categories(user_id)
                        categories_info = ""
                        if categories:
                            categories_info = f"\n{subscribed_to_categories}\n"
                            for category in categories:
                                # Получаем локализованное описание категории
                                category_parts = category.split('_')
                                if len(category_parts) == 2:
                                    category_type, operation = category_parts
                                    category_name = self.localization_service.get_text(f'category_{category_type}', language)
                                    operation_name = self.localization_service.get_text(f'category_{operation}', language)
                                    if category_name and operation_name:
                                        description = f"{operation_name} {category_name}"
                                        emoji = CATEGORY_EMOJI.get(category, "")
                                        categories_info += f"  - {emoji} {description}\n"
                                    else:
                                        description = CATEGORY_DESCRIPTIONS.get(category, category)
                                        emoji = CATEGORY_EMOJI.get(category, "")
                                        categories_info += f"  - {emoji} {description}\n"
                                else:
                                    description = CATEGORY_DESCRIPTIONS.get(category, category)
                                    emoji = CATEGORY_EMOJI.get(category, "")
                                    categories_info += f"  - {emoji} {description}\n"

                        # Получаем язык пользователя
                        user_language = self.user_language_service.get_language(user_id)

                        # Получаем название языка для отображения
                        language_name = ""
                        for lang in self.user_language_service.get_available_languages():
                            if lang.code == user_language:
                                language_name = str(lang)
                                break

                        # Если язык не найден, используем код языка
                        if not language_name and user_language:
                            language_name = user_language
                        elif not language_name:
                            language_name = self.user_language_service._default_language

                        # Формируем строку с информацией о пользователе
                        user_line = f"{i}. ID: {user_id}"
                        if user_name:
                            user_line += f" - {user_name}"

                        # Добавляем информацию о языке
                        user_line += f"\n   {user_language_text} {language_name}"

                        if categories_info:
                            user_line += f"{categories_info}"

                        users_list += f"{user_line}\n\n"

                    if not users_list:
                        users_list = users_list_empty

                    # Получаем локализованный текст для ответа
                    users_list_response = self.localization_service.get_text('users_list_response', language) or "Список пользователей"

                    await PropertyBot.safe_answer(query, users_list_response)

                    # Получаем локализованное меню пользователей
                    from src.presentation.telegram.localization.menu import get_localized_users_menu

                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        f"{users_list_title}\n\n{users_list}",
                        get_localized_users_menu(self.localization_service, self.user_language_service, admin_id),
                        parse_mode="HTML"
                    )
                    return

                elif action == "blocked":
                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(admin_id)

                    # Получаем локализованные тексты
                    blocked_list_title = self.localization_service.get_text('blocked_list_title', language) or "Заблокированные пользователи:"
                    blocked_list_empty = self.localization_service.get_text('blocked_list_empty', language) or "Список заблокированных пользователей пуст."
                    no_blocked_users = self.localization_service.get_text('no_blocked_users', language) or "Нет заблокированных пользователей."
                    username_label = self.localization_service.get_text('username_label', language) or "Имя пользователя:"
                    name_label = self.localization_service.get_text('name_label', language) or "Имя:"
                    blocked_list_response = self.localization_service.get_text('blocked_list_response', language) or "Список заблокированных пользователей"

                    # Получаем список заблокированных пользователей (отклоненных запросов)
                    blocked_users = self.access_requests.get_rejected_requests()

                    if not blocked_users:
                        await PropertyBot.safe_answer(query, no_blocked_users)

                        # Получаем локализованное меню пользователей
                        from src.presentation.telegram.localization.menu import get_localized_users_menu

                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            blocked_list_empty,
                            get_localized_users_menu(self.localization_service, self.user_language_service, admin_id)
                        )
                        return

                    # Формируем список заблокированных пользователей
                    blocked_list = f"{blocked_list_title}\n\n"
                    for user in blocked_users:
                        user_id = user.get("user_id", "")
                        username = user.get("username", "")
                        first_name = user.get("first_name", "")

                        user_info = f"ID: {user_id}\n"
                        if username:
                            user_info += f"{username_label} @{username}\n"
                        if first_name:
                            user_info += f"{name_label} {first_name}\n"

                        blocked_list += f"{user_info}\n"

                    await PropertyBot.safe_answer(query, blocked_list_response)

                    # Получаем локализованное меню пользователей
                    from src.presentation.telegram.localization.menu import get_localized_users_menu

                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        blocked_list,
                        get_localized_users_menu(self.localization_service, self.user_language_service, admin_id)
                    )
                    return

                elif action == "add":
                    # Сохраняем действие в контексте пользователя
                    # Инициализируем user_data, если необходимо
                    if not hasattr(self, 'user_data'):
                        self.user_data = {}

                    if admin_id not in self.user_data:
                        self.user_data[admin_id] = {}

                    # Сохраняем действие в контексте
                    self.user_data[admin_id]['user_action'] = 'add_user'
                    self.logger.info(f"[Пользователи] Сохранено действие 'add_user' для пользователя {admin_id}")

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Введите ID пользователя")

                    # Просим ввести ID пользователя
                    await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, "Введите ID пользователя для добавления:\n\n"
                        "Просто отправьте ID пользователя в этот чат", None)
                    return

                elif action == "remove":
                    # Сохраняем действие в контексте пользователя
                    # Инициализируем user_data, если необходимо
                    if not hasattr(self, 'user_data'):
                        self.user_data = {}

                    if admin_id not in self.user_data:
                        self.user_data[admin_id] = {}

                    # Сохраняем действие в контексте
                    self.user_data[admin_id]['user_action'] = 'remove_user'
                    self.logger.info(f"[Пользователи] Сохранено действие 'remove_user' для пользователя {admin_id}")

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Введите ID пользователя")

                    # Просим ввести ID пользователя
                    await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, "Введите ID пользователя для удаления:\n\n"
                        "Просто отправьте ID пользователя в этот чат", None)
                    return

            # Обрабатываем кнопки подписки/отписки от уведомлений
            elif data == "admin_subscribe_requests" or data == "admin_unsubscribe_requests":
                # Проверяем, является ли пользователь администратором
                if admin_id not in self.admin_users:
                    await PropertyBot.safe_answer(query, "У вас нет прав администратора.")
                    return

                # Получаем ID пользователя
                user_id = query.from_user.id

                if data == "admin_subscribe_requests":
                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Подписываем пользователя на уведомления
                    if self.access_requests.subscribe_to_notifications(user_id):
                        # Получаем локализованные тексты
                        success_text = "Вы успешно подписаны на уведомления о запросах на доступ."
                        detail_text = "Теперь вы будете получать уведомления, когда новые пользователи запрашивают доступ к боту."

                        # Отвечаем на кнопку
                        await PropertyBot.safe_answer(query, success_text)

                        # Обновляем сообщение
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            f"{success_text}\n\n{detail_text}"
                        )

                        # Обновляем статус подписки в сообщении
                        is_subscribed = True
                        settings_title = self.localization_service.get_text('notification_settings_title', language) or "Настройки уведомлений:"
                        status_enabled = self.localization_service.get_text('notification_status_enabled', language) or "Включены"
                        access_requests_label = self.localization_service.get_text('notification_access_requests', language) or "Уведомления о запросах на доступ"

                        # Формируем текст сообщения
                        message_text = f"{settings_title}\n\n{access_requests_label}: {status_enabled}\n\nВыберите действие:"

                        # Получаем локализованное меню с инлайн-кнопками
                        from src.presentation.telegram.localization.menu import get_localized_notification_settings_menu

                        # Обновляем сообщение с новым статусом
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            message_text,
                            get_localized_notification_settings_menu(self.localization_service, self.user_language_service, user_id)
                        )
                    else:
                        already_text = "Вы уже подписаны на уведомления о запросах на доступ."
                        await PropertyBot.safe_answer(query, already_text)
                    return

                elif data == "admin_unsubscribe_requests":
                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Отписываем пользователя от уведомлений
                    if self.access_requests.unsubscribe_from_notifications(user_id):
                        # Получаем локализованные тексты
                        success_text = "Вы успешно отписаны от уведомлений о запросах на доступ."
                        detail_text = "Теперь вы не будете получать уведомления, когда новые пользователи запрашивают доступ к боту."

                        # Отвечаем на кнопку
                        await PropertyBot.safe_answer(query, success_text)

                        # Обновляем сообщение
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            f"{success_text}\n\n{detail_text}"
                        )

                        # Обновляем статус подписки в сообщении
                        is_subscribed = False
                        settings_title = self.localization_service.get_text('notification_settings_title', language) or "Настройки уведомлений:"
                        status_disabled = self.localization_service.get_text('notification_status_disabled', language) or "Отключены"
                        access_requests_label = self.localization_service.get_text('notification_access_requests', language) or "Уведомления о запросах на доступ"

                        # Формируем текст сообщения
                        message_text = f"{settings_title}\n\n{access_requests_label}: {status_disabled}\n\nВыберите действие:"

                        # Получаем локализованное меню с инлайн-кнопками
                        from src.presentation.telegram.localization.menu import get_localized_notification_settings_menu

                        # Обновляем сообщение с новым статусом
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            message_text,
                            get_localized_notification_settings_menu(self.localization_service, self.user_language_service, user_id)
                        )
                    else:
                        already_text = "Вы уже отписаны от уведомлений о запросах на доступ."
                        await PropertyBot.safe_answer(query, already_text)
                    return

            # Обрабатываем кнопки управления запросами
            elif data.startswith("admin_requests_"):
                # Проверяем, является ли пользователь администратором
                if admin_id not in self.admin_users:
                    await PropertyBot.safe_answer(query, "У вас нет прав администратора.")
                    return

                action = data.split("_")[2]

                if action == "pending":
                    # Получаем список ожидающих запросов
                    pending_requests = self.access_requests.get_pending_requests()

                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованные тексты
                    requests_id = self.localization_service.get_text('requests_id', language) or "ID:"
                    requests_username = self.localization_service.get_text('requests_username', language) or "Имя пользователя:"
                    requests_name = self.localization_service.get_text('requests_name', language) or "Имя:"
                    requests_no_pending = self.localization_service.get_text('requests_no_pending', language) or "Нет ожидающих запросов."

                    if pending_requests:
                        requests_list = ""
                        for i, request_data in enumerate(pending_requests, 1):
                            req_user_id = request_data.get("user_id", "")
                            username = request_data.get("username", "")
                            first_name = request_data.get("first_name", "")
                            requests_list += f"{i}. {requests_id} {req_user_id}\n"
                            if username:
                                requests_list += f"   {requests_username} @{username}\n"
                            if first_name:
                                requests_list += f"   {requests_name} {first_name}\n"
                            requests_list += "\n"
                    else:
                        requests_list = requests_no_pending

                    # Получаем локализованный текст для ответа
                    pending_response = self.localization_service.get_text('requests_pending_response', language) or "Список ожидающих запросов"

                    # Получаем локализованный заголовок
                    pending_title = self.localization_service.get_text('requests_pending_title', language) or "Ожидающие запросы на доступ:"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, pending_response)

                    # Используем локализованное меню запросов
                    from src.presentation.telegram.localization.menu import get_localized_requests_menu
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        f"{pending_title}\n\n{requests_list}",
                        get_localized_requests_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return

                elif action == "approved":
                    # Получаем список одобренных запросов
                    approved_requests = {}
                    for user_id, request in self.access_requests.requests.items():
                        if request.get("status") == "approved":
                            approved_requests[user_id] = request

                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованные тексты
                    requests_id = self.localization_service.get_text('requests_id', language) or "ID:"
                    requests_username = self.localization_service.get_text('requests_username', language) or "Имя пользователя:"
                    requests_name = self.localization_service.get_text('requests_name', language) or "Имя:"
                    requests_no_approved = self.localization_service.get_text('requests_no_approved', language) or "Нет одобренных запросов."

                    if approved_requests:
                        requests_list = ""
                        for i, (req_user_id, request) in enumerate(approved_requests.items(), 1):
                            username = request.get("username", "")
                            first_name = request.get("first_name", "")
                            requests_list += f"{i}. {requests_id} {req_user_id}\n"
                            if username:
                                requests_list += f"   {requests_username} @{username}\n"
                            if first_name:
                                requests_list += f"   {requests_name} {first_name}\n"
                            requests_list += "\n"
                    else:
                        requests_list = requests_no_approved

                    # Получаем локализованный текст для ответа
                    approved_response = self.localization_service.get_text('requests_approved_response', language) or "Список одобренных запросов"

                    # Получаем локализованный заголовок
                    approved_title = self.localization_service.get_text('requests_approved_title', language) or "Одобренные запросы на доступ:"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, approved_response)

                    # Используем локализованное меню запросов
                    from src.presentation.telegram.localization.menu import get_localized_requests_menu
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        f"{approved_title}\n\n{requests_list}",
                        get_localized_requests_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return

                elif action == "rejected":
                    # Получаем список отклоненных запросов
                    rejected_requests = {}
                    for user_id, request in self.access_requests.requests.items():
                        if request.get("status") == "rejected":
                            rejected_requests[user_id] = request

                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованные тексты
                    requests_id = self.localization_service.get_text('requests_id', language) or "ID:"
                    requests_username = self.localization_service.get_text('requests_username', language) or "Имя пользователя:"
                    requests_name = self.localization_service.get_text('requests_name', language) or "Имя:"
                    requests_no_rejected = self.localization_service.get_text('requests_no_rejected', language) or "Нет отклоненных запросов."

                    if rejected_requests:
                        requests_list = ""
                        for i, (req_user_id, request) in enumerate(rejected_requests.items(), 1):
                            username = request.get("username", "")
                            first_name = request.get("first_name", "")
                            requests_list += f"{i}. {requests_id} {req_user_id}\n"
                            if username:
                                requests_list += f"   {requests_username} @{username}\n"
                            if first_name:
                                requests_list += f"   {requests_name} {first_name}\n"
                            requests_list += "\n"
                    else:
                        requests_list = requests_no_rejected

                    # Получаем локализованный текст для ответа
                    rejected_response = self.localization_service.get_text('requests_rejected_response', language) or "Список отклоненных запросов"

                    # Получаем локализованный заголовок
                    rejected_title = self.localization_service.get_text('requests_rejected_title', language) or "Отклоненные запросы на доступ:"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, rejected_response)

                    # Используем локализованное меню запросов
                    from src.presentation.telegram.localization.menu import get_localized_requests_menu
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        f"{rejected_title}\n\n{requests_list}",
                        get_localized_requests_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return

                elif action == "refresh":
                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст для ответа
                    refresh_response = self.localization_service.get_text('requests_refresh_response', language) or "Список запросов обновлен"

                    # Получаем локализованный заголовок
                    title = self.localization_service.get_text('requests_menu_title', language) or "Управление запросами на доступ:"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, refresh_response)

                    # Используем локализованное меню запросов
                    from src.presentation.telegram.localization.menu import get_localized_requests_menu
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        title,
                        get_localized_requests_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return


            # Обрабатываем кнопки выбора языка
            elif data.startswith("language_"):
                # Получаем код языка
                language_code = data.split("_")[1]

                # Получаем ID пользователя
                user_id = query.from_user.id

                # Проверяем, первый ли это запуск бота для пользователя
                is_first_launch = not self._user_has_set_language(user_id)

                # Устанавливаем язык пользователя
                if self.user_language_service.set_language(user_id, language_code):
                    # Принудительно перезагружаем языковые настройки пользователей
                    if hasattr(self.user_language_service, '_user_language_storage') and hasattr(self.user_language_service._user_language_storage, '_load'):
                        try:
                            self.user_language_service._user_language_storage._load()
                            self.logger.info(f"Языковые настройки пользователей успешно перезагружены после смены языка для пользователя {user_id}")
                        except Exception as e:
                            self.logger.error(f"Ошибка при перезагрузке языковых настроек пользователей: {e}")

                    # Получаем название языка
                    language_name = ""
                    for lang in self.user_language_service.get_available_languages():
                        if lang.code == language_code:
                            language_name = lang.name
                            break

                    # Получаем локализованный текст
                    language = language_code
                    language_changed_text = self.localization_service.get_text('language_changed', language) or f"Язык изменен на {language_name}"

                    # Отвечаем на кнопку с локализованным текстом
                    await PropertyBot.safe_answer(query, language_changed_text)

                    # Если это первый запуск, отправляем приветственное сообщение
                    if is_first_launch:
                        # Получаем имя пользователя
                        user_name = query.from_user.first_name if query.from_user else "Друг"

                        # Получаем локализованный шаблон
                        template = self.localization_service.get_template('command_start', language)

                        # Форматируем сообщение
                        welcome_text = self.localization_service.format_template(template, {'name': user_name})

                        # Получаем название языка для отображения
                        language_name_display = ""
                        for lang in self.user_language_service.get_available_languages():
                            if lang.code == language_code:
                                language_name_display = str(lang)
                                break

                        # Получаем локализованные тексты
                        current_language_text = self.localization_service.get_text('current_language', language_code)
                        language_menu_title = self.localization_service.get_text('language_menu_title', language_code) or "Выберите язык:"

                        # Формируем текст сообщения с информацией о текущем языке
                        if current_language_text and language_name_display:
                            # Форматируем текст о текущем языке
                            formatted_text = current_language_text.format(language=language_name_display)
                            title = f"{formatted_text}\n\n{language_menu_title}"
                        else:
                            title = language_menu_title

                        # Обновляем сообщение с выбором языка
                        from src.presentation.telegram.localization.menu import get_localized_language_menu
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            title,
                            get_localized_language_menu(self.localization_service, self.user_language_service, user_id)
                        )

                        # Отправляем приветственное сообщение с главным меню
                        if query.message:
                            # Отправляем приветственное сообщение
                            await PropertyBot.safe_send_message(
                                self.application.bot,
                                query.message.chat_id,
                                welcome_text,
                                None
                            )

                            # Клавиатура будет обновлена автоматически в обработчике языка
                    else:
                        # Получаем название языка для отображения
                        language_name_display = ""
                        for lang in self.user_language_service.get_available_languages():
                            if lang.code == language_code:
                                language_name_display = str(lang)
                                break

                        # Получаем локализованные тексты
                        current_language_text = self.localization_service.get_text('current_language', language_code)
                        language_menu_title = self.localization_service.get_text('language_menu_title', language_code) or "Выберите язык:"

                        # Формируем текст сообщения с информацией о текущем языке
                        if current_language_text and language_name_display:
                            # Форматируем текст о текущем языке
                            formatted_text = current_language_text.format(language=language_name_display)
                            title = f"{formatted_text}\n\n{language_menu_title}"
                        else:
                            title = language_menu_title

                        # Обновляем сообщение с выбором языка
                        from src.presentation.telegram.localization.menu import get_localized_language_menu
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            title,
                            get_localized_language_menu(self.localization_service, self.user_language_service, user_id)
                        )

                        # Клавиатура будет обновлена автоматически в обработчике языка
                        if query.message:
                            # Логируем информацию о смене языка
                            self.logger.info(f"Язык пользователя {user_id} изменен на {language_code}")
                else:
                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Ошибка при изменении языка")

                return

            # Обрабатываем кнопки категорий
            elif data.startswith("category_"):
                category_type = data.split("_")[1]

                # Получаем ID пользователя
                user_id = query.from_user.id

                # Обрабатываем кнопку "Все категории"
                if category_type == "all":
                    # Импортируем необходимые модули
                    from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI

                    # Получаем список всех категорий из словаря CATEGORY_DESCRIPTIONS
                    all_categories = list(CATEGORY_DESCRIPTIONS.keys())

                    # Инициализируем список категорий пользователя, если он не существует
                    if user_id not in self.active_categories:
                        self.active_categories[user_id] = []

                    # Добавляем все категории
                    for category in all_categories:
                        if category not in self.active_categories[user_id]:
                            self.active_categories[user_id].append(category)

                    # Сохраняем изменения
                    self._save_active_categories()

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст для ответа
                    subscribed_all_text = self.localization_service.get_text('subscribed_all_categories', language) or f"Вы подписались на все категории ({len(all_categories)} шт.)"

                    # Если в тексте есть плейсхолдер для количества категорий, заменяем его
                    if "{count}" in subscribed_all_text:
                        subscribed_all_text = subscribed_all_text.format(count=len(all_categories))

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, subscribed_all_text)

                    # Формируем список категорий с их описаниями
                    categories_list = ""
                    for category in all_categories:
                        # Получаем локализованное описание категории
                        category_parts = category.split('_')
                        if len(category_parts) == 2:
                            category_type, operation = category_parts
                            category_name = self.localization_service.get_text(f'category_{category_type}', language)
                            operation_name = self.localization_service.get_text(f'category_{operation}', language)
                            if category_name and operation_name:
                                description = f"{operation_name} {category_name}"
                                emoji = CATEGORY_EMOJI.get(category, "")
                                categories_list += f"- {emoji} {description}\n"
                            else:
                                description = CATEGORY_DESCRIPTIONS.get(category, category)
                                emoji = CATEGORY_EMOJI.get(category, "")
                                categories_list += f"- {emoji} {description}\n"
                        else:
                            description = CATEGORY_DESCRIPTIONS.get(category, category)
                            emoji = CATEGORY_EMOJI.get(category, "")
                            categories_list += f"- {emoji} {description}\n"

                    # Получаем локализованные тексты
                    subscribed_all_title = self.localization_service.get_text('subscribed_all_categories_title', language) or f"Вы подписались на все категории:"
                    active_categories_text = self.localization_service.get_text('active_categories', language) or "Ваши активные категории:"
                    choose_more_text = self.localization_service.get_text('choose_more_categories', language) or "Выберите другие категории или вернитесь в главное меню:"

                    # Формируем сообщение
                    message_text = f"{subscribed_all_title}\n\n" \
                                   f"{active_categories_text}\n{categories_list}\n" \
                                   f"{choose_more_text}"

                    # Обновляем сообщение с локализованным меню
                    from src.presentation.telegram.localization.menu import get_localized_categories_menu

                    # Создаем клавиатуру
                    keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

                    # Отправляем сообщение с клавиатурой
                    try:
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            message_text,
                            reply_markup=keyboard
                        )
                    except Exception as e:
                        self.logger.error(f"Ошибка при обновлении меню категорий: {e}")
                        # Пробуем отправить сообщение напрямую
                        if query.message:
                            await self.application.bot.edit_message_text(
                                chat_id=query.message.chat_id,
                                message_id=query.message.message_id,
                                text=message_text,
                                reply_markup=keyboard
                            )
                    return

                # Обрабатываем кнопку "Очистить все"
                elif category_type == "clear":
                    # Инициализируем список категорий пользователя, если он не существует
                    if user_id in self.active_categories:
                        self.active_categories[user_id] = []
                        # Сохраняем изменения
                        self._save_active_categories()

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Все категории удалены")

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст
                    clear_message = self.localization_service.get_text('categories_clear_message', language) or "Все категории удалены. Выберите категории для подписки:"

                    # Обновляем сообщение с локализованным меню
                    from src.presentation.telegram.localization.menu import get_localized_categories_menu

                    # Создаем клавиатуру
                    keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

                    # Отправляем сообщение с клавиатурой
                    try:
                        await PropertyBot.safe_edit_message_text_from_query(
                            self.application.bot,
                            query,
                            clear_message,
                            reply_markup=keyboard
                        )
                    except Exception as e:
                        self.logger.error(f"Ошибка при обновлении меню категорий: {e}")
                        # Пробуем отправить сообщение напрямую
                        if query.message:
                            await self.application.bot.edit_message_text(
                                chat_id=query.message.chat_id,
                                message_id=query.message.message_id,
                                text=clear_message,
                                reply_markup=keyboard
                            )
                    return

                # Обрабатываем выбор конкретной категории (apartments, houses, rooms, garages, dacha, land, commercial)
                else:
                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Импортируем необходимые модули
                    from src.presentation.telegram.menu import CATEGORY_NAMES, CATEGORY_EMOJI

                    # Получаем локализованное название категории
                    category_name_key = f'category_{category_type}'
                    category_name = self.localization_service.get_text(category_name_key, language)

                    # Если локализованное название не найдено, используем стандартное
                    if not category_name:
                        category_name = CATEGORY_NAMES.get(category_type, category_type)

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, f"Выбрана категория: {category_name}")

                    # Получаем локализованный текст заголовка
                    title_template = self.localization_service.get_text('category_type_menu_title', language) or "Выберите тип операции для категории '{category_name}':"
                    title = title_template.format(category_name=category_name)

                    # Показываем локализованное меню выбора типа операции (продажа/аренда)
                    from src.presentation.telegram.localization.menu import get_localized_category_type_menu
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        title,
                        get_localized_category_type_menu(self.localization_service, self.user_language_service, user_id, category_type)
                    )
                    return

            # Обрабатываем выбор типа операции (продажа/аренда)
            elif data.endswith("_sale") or data.endswith("_rent"):
                # Получаем ID пользователя
                user_id = query.from_user.id

                # Получаем ID категории
                category_id = data

                # Импортируем необходимые модули
                from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI

                # Инициализируем список категорий пользователя, если он не существует
                if user_id not in self.active_categories:
                    self.active_categories[user_id] = []

                # Добавляем категорию в список активных категорий пользователя
                if category_id not in self.active_categories[user_id]:
                    self.active_categories[user_id].append(category_id)
                    # Сохраняем изменения
                    self._save_active_categories()

                # Получаем язык пользователя
                language = self.user_language_service.get_language(user_id)

                # Получаем локализованное описание категории
                category_parts = category_id.split('_')
                if len(category_parts) == 2:
                    category_type, operation = category_parts
                    category_name = self.localization_service.get_text(f'category_{category_type}', language)
                    operation_name = self.localization_service.get_text(f'category_{operation}', language)
                    if category_name and operation_name:
                        localized_description = f"{operation_name} {category_name}"
                        emoji = CATEGORY_EMOJI.get(category_id, "")
                        category_description = f"{emoji} {localized_description}"
                    else:
                        emoji = CATEGORY_EMOJI.get(category_id, "")
                        category_description = f"{emoji} {CATEGORY_DESCRIPTIONS.get(category_id, category_id)}"
                else:
                    emoji = CATEGORY_EMOJI.get(category_id, "")
                    category_description = f"{emoji} {CATEGORY_DESCRIPTIONS.get(category_id, category_id)}"

                # Отвечаем на кнопку
                await PropertyBot.safe_answer(query, f"Вы подписались на категорию: {category_description}")

                # Формируем список активных категорий с локализацией
                active_categories_list = ""
                for cat_id in self.active_categories[user_id]:
                    # Получаем локализованное описание категории
                    category_parts = cat_id.split('_')
                    if len(category_parts) == 2:
                        category_type, operation = category_parts
                        category_name = self.localization_service.get_text(f'category_{category_type}', language)
                        operation_name = self.localization_service.get_text(f'category_{operation}', language)
                        if category_name and operation_name:
                            description = f"{operation_name} {category_name}"
                            emoji = CATEGORY_EMOJI.get(cat_id, "")
                            active_categories_list += f"{emoji} {description}\n"
                        else:
                            cat_desc = CATEGORY_DESCRIPTIONS.get(cat_id, cat_id)
                            emoji = CATEGORY_EMOJI.get(cat_id, "")
                            active_categories_list += f"{emoji} {cat_desc}\n"
                    else:
                        cat_desc = CATEGORY_DESCRIPTIONS.get(cat_id, cat_id)
                        emoji = CATEGORY_EMOJI.get(cat_id, "")
                        active_categories_list += f"{emoji} {cat_desc}\n"

                # Получаем локализованные тексты
                subscribed_template = self.localization_service.get_text('category_subscribed', language) or "Вы подписались на категорию: {category}"
                active_categories_text = self.localization_service.get_text('active_categories', language) or "Ваши активные категории:"
                choose_more_text = self.localization_service.get_text('choose_more_categories', language) or "Выберите другие категории или вернитесь в главное меню:"

                # Формируем сообщение
                message_text = f"{subscribed_template.format(category=category_description)}\n\n" \
                               f"{active_categories_text}\n{active_categories_list}\n" \
                               f"{choose_more_text}"

                # Обновляем сообщение с локализованным меню
                from src.presentation.telegram.localization.menu import get_localized_categories_menu

                # Создаем клавиатуру
                keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

                # Отправляем сообщение с клавиатурой
                try:
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        message_text,
                        reply_markup=keyboard
                    )
                except Exception as e:
                    self.logger.error(f"Ошибка при обновлении меню категорий: {e}")
                    # Пробуем отправить сообщение напрямую
                    if query.message:
                        await self.application.bot.edit_message_text(
                            chat_id=query.message.chat_id,
                            message_id=query.message.message_id,
                            text=message_text,
                            reply_markup=keyboard
                        )
                return

            # Обрабатываем кнопку "Назад к категориям"
            elif data == "back_to_categories":
                # Получаем ID пользователя
                user_id = query.from_user.id

                # Получаем язык пользователя
                language = self.user_language_service.get_language(user_id)

                # Получаем локализованный текст для ответа
                back_text = self.localization_service.get_text('back_to_categories', language) or "Возврат к выбору категорий"

                # Отвечаем на кнопку
                await PropertyBot.safe_answer(query, back_text)

                # Получаем локализованный заголовок
                categories_title = self.localization_service.get_text('categories_menu_title', language) or "Выберите категорию недвижимости:"

                # Показываем локализованное меню выбора категорий
                from src.presentation.telegram.localization.menu import get_localized_categories_menu

                # Создаем клавиатуру
                keyboard = get_localized_categories_menu(self.localization_service, self.user_language_service, user_id)

                # Отправляем сообщение с клавиатурой
                try:
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        categories_title,
                        reply_markup=keyboard
                    )
                except Exception as e:
                    self.logger.error(f"Ошибка при обновлении меню категорий: {e}")
                    # Пробуем отправить сообщение напрямую
                    if query.message:
                        await self.application.bot.edit_message_text(
                            chat_id=query.message.chat_id,
                            message_id=query.message.message_id,
                            text=categories_title,
                            reply_markup=keyboard
                        )
                return

            # Обрабатываем кнопки помощи
            elif data.startswith("help_"):
                help_type = data.split("_")[1]

                # Обрабатываем разные типы помощи
                if help_type == "commands":
                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст для ответа
                    help_commands_text = self.localization_service.get_text('help_commands', language) or "Справка по командам"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, help_commands_text)

                    # Получаем локализованный шаблон команд
                    commands_template = self.localization_service.get_template('command_help', language)

                    # Если шаблон не найден, используем стандартный текст
                    if not commands_template:
                        commands_text = "Доступные команды:\n\n" \
                            "/start - Начать работу с ботом\n" \
                            "/help - Показать справку\n" \
                            "/status - Показать статус бота\n" \
                            "/stop - Остановить уведомления\n" \
                            "/category add [категория] - Добавить категорию для уведомлений\n" \
                            "/category remove [категория] - Удалить категорию для уведомлений\n" \
                            "/category list - Показать список категорий для уведомлений\n" \
                            "/category all - Подписаться сразу на все категории"
                    else:
                        commands_text = commands_template

                    # Получаем локализованное меню помощи
                    from src.presentation.telegram.localization.menu import get_localized_help_menu

                    # Пробуем отправить новое сообщение через безопасный метод
                    try:
                        # Проверяем, что query.message не None перед использованием его атрибутов
                        if query.message is not None:
                            try:
                                # Пробуем отправить сообщение с HTML-разметкой
                                await PropertyBot.safe_send_message(
                                    self.application.bot,
                                    query.message.chat_id,
                                    commands_text,
                                    get_localized_help_menu(self.localization_service, self.user_language_service, user_id)
                                )
                            except Exception as e:
                                self.logger.warning(f"Ошибка при отправке сообщения с HTML-разметкой: {e}")
                                # Если не удалось отправить с HTML-разметкой, отправляем без разметки
                                await self.application.bot.send_message(
                                    chat_id=query.message.chat_id,
                                    text=commands_text,
                                    reply_markup=get_localized_help_menu(self.localization_service, self.user_language_service, user_id),
                                    parse_mode=None  # Отключаем HTML-разметку
                                )
                        else:
                            self.logger.error("Ошибка: query.message является None")
                    except Exception as e:
                        self.logger.error(f"Ошибка при отправке сообщения со справкой по командам: {e}")
                    return

                elif help_type == "categories":
                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст для ответа
                    help_categories_text = self.localization_service.get_text('help_categories_response', language) or "Справка по категориям"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, help_categories_text)

                    # Импортируем константы
                    from src.presentation.telegram.menu import CATEGORY_DESCRIPTIONS, CATEGORY_EMOJI

                    # Формируем список категорий с локализованными описаниями
                    categories_list = ""
                    for category_id, _ in CATEGORY_DESCRIPTIONS.items():
                        # Получаем локализованное описание категории
                        category_parts = category_id.split('_')
                        if len(category_parts) == 2:
                            category_type, operation = category_parts
                            category_name = self.localization_service.get_text(f'category_{category_type}', language)
                            operation_name = self.localization_service.get_text(f'category_{operation}', language)
                            if category_name and operation_name:
                                description = f"{operation_name} {category_name}"
                                emoji = CATEGORY_EMOJI.get(category_id, "")
                                categories_list += f"{emoji} {description}\n"
                            else:
                                description = CATEGORY_DESCRIPTIONS.get(category_id, category_id)
                                emoji = CATEGORY_EMOJI.get(category_id, "")
                                categories_list += f"{emoji} {description}\n"
                        else:
                            description = CATEGORY_DESCRIPTIONS.get(category_id, category_id)
                            emoji = CATEGORY_EMOJI.get(category_id, "")
                            categories_list += f"{emoji} {description}\n"

                    # Получаем локализованный заголовок
                    available_categories_title = self.localization_service.get_text('available_categories', language) or "Доступные категории:"

                    # Получаем локализованное меню помощи
                    from src.presentation.telegram.localization.menu import get_localized_help_menu

                    # Обновляем сообщение
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        f"{available_categories_title}\n\n{categories_list}",
                        get_localized_help_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return

                elif help_type == "settings":
                    # Показываем справку по настройкам
                    from src.presentation.telegram.menu import get_help_menu
                    await PropertyBot.safe_answer(query, "Справка по настройкам")
                    await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, "Настройки бота:\n\n"
                        "В настоящее время дополнительные настройки недоступны.", get_help_menu()
                    )
                    return

                elif help_type == "about":
                    # Показываем информацию о боте
                    # Получаем ID пользователя
                    user_id = query.from_user.id

                    # Получаем язык пользователя
                    language = self.user_language_service.get_language(user_id)

                    # Получаем локализованный текст для ответа
                    about_response = self.localization_service.get_text('help_about', language) or "О боте"

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, about_response)

                    # Получаем локализованный шаблон информации о боте
                    about_template = self.localization_service.get_template('about_bot', language)

                    # Если шаблон не найден, используем стандартный текст
                    if not about_template:
                        about_template = "О боте:\n\n" \
                            "Этот бот предназначен для отправки уведомлений о новых объявлениях на OLX.\n\n" \
                            "Вы можете подписаться на различные категории недвижимости и получать уведомления о новых объявлениях в этих категориях.\n\n" \
                            "Версия: 1.0.0\n" \
                            "Разработчик: @vitalii820"

                    # Получаем локализованное меню помощи
                    from src.presentation.telegram.localization.menu import get_localized_help_menu

                    # Обновляем сообщение
                    await PropertyBot.safe_edit_message_text_from_query(
                        self.application.bot,
                        query,
                        about_template,
                        get_localized_help_menu(self.localization_service, self.user_language_service, user_id)
                    )
                    return
            elif data.startswith("approve_"):
                # Получаем ID пользователя
                user_id = int(data.split("_")[1])

                # Подтверждаем запрос
                if self.access_requests.approve_request(user_id):
                    # Добавляем пользователя в список разрешенных
                    if user_id not in self.allowed_users:
                        self.allowed_users.append(user_id)
                        self.logger.info(f"Пользователь {user_id} добавлен в список разрешенных")
                        # Сохраняем изменения
                        self._save_allowed_users()

                    # Отправляем уведомление пользователю и главное меню
                    try:
                        # Получаем язык пользователя
                        language = self.user_language_service.get_language(user_id)
                        # Получаем локализованный текст
                        access_approved_text = self.localization_service.get_text('access_request_approved', language)

                        # Сначала отправляем сообщение о подтверждении доступа
                        await PropertyBot.safe_send_message(self.application.bot, user_id, access_approved_text)

                        # Затем отправляем приветствие и главное меню
                        from src.presentation.telegram.menu import get_main_menu

                        # Получаем информацию о пользователе
                        user_info = await self.application.bot.get_chat(chat_id=user_id, api_kwargs={})
                        user_name = user_info.first_name if user_info and user_info.first_name else "Друг"

                        # Отправляем приветствие и главное меню
                        await PropertyBot.safe_send_message(
                            self.application.bot,
                            user_id,
                            f"Привет, {user_name}! Я бот для уведомлений о новых объявлениях OLX.\n\n"
                            "Используйте кнопки меню для управления ботом или команду /help для получения справки.",
                            get_main_menu()
                        )
                    except Exception as e:
                        self.logger.error(f"Ошибка при отправке уведомления пользователю {user_id}: {e}")

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Запрос подтвержден.")
                    # Получаем язык администратора
                    admin_id = query.from_user.id
                    language = self.user_language_service.get_language(admin_id)
                    # Получаем локализованный текст
                    access_approved_admin_text = self.localization_service.get_text('access_request_approved_admin', language)
                    # Форматируем текст
                    access_approved_admin_text = access_approved_admin_text.format(user_id=user_id)

                    await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, access_approved_admin_text, None)

                    # Обновляем сообщения у всех администраторов
                    if user_id in self.access_request_messages:
                        admin_id = query.from_user.id
                        for other_admin_id, message_id in self.access_request_messages[user_id].items():
                            # Пропускаем администратора, который нажал на кнопку
                            if other_admin_id == admin_id:
                                continue

                            try:
                                # Обновляем сообщение у другого администратора
                                await PropertyBot.safe_edit_message_text(
                                    self.application.bot,
                                    other_admin_id,
                                    message_id,
                                    f"Запрос на доступ от пользователя {user_id} был подтвержден администратором {admin_id}."
                                )
                                self.logger.info(f"Сообщение о запросе на доступ обновлено у администратора {other_admin_id}")
                            except Exception as e:
                                self.logger.error(f"Ошибка при обновлении сообщения у администратора {other_admin_id}: {e}")

                        # Удаляем записи о сообщениях, так как они больше не нужны
                        del self.access_request_messages[user_id]
                else:
                    await PropertyBot.safe_answer(query, "Не удалось подтвердить запрос.")

            elif data.startswith("reject_"):
                # Получаем ID пользователя
                user_id = int(data.split("_")[1])

                # Отклоняем запрос
                if self.access_requests.reject_request(user_id):
                    # Отправляем уведомление пользователю
                    try:
                        # Получаем язык пользователя
                        language = self.user_language_service.get_language(user_id)
                        # Получаем локализованный текст
                        access_rejected_text = self.localization_service.get_text('access_request_rejected', language)

                        await PropertyBot.safe_send_message(self.application.bot, user_id, access_rejected_text)
                    except Exception as e:
                        self.logger.error(f"Ошибка при отправке уведомления пользователю {user_id}: {e}", None)

                    # Отвечаем на кнопку
                    await PropertyBot.safe_answer(query, "Запрос отклонен.")
                    # Получаем язык администратора
                    admin_id = query.from_user.id
                    language = self.user_language_service.get_language(admin_id)
                    # Получаем локализованный текст
                    access_rejected_admin_text = self.localization_service.get_text('access_request_rejected_admin', language)
                    # Форматируем текст
                    access_rejected_admin_text = access_rejected_admin_text.format(user_id=user_id)

                    await PropertyBot.safe_edit_message_text_from_query(self.application.bot, query, access_rejected_admin_text, None)

                    # Обновляем сообщения у всех администраторов
                    if user_id in self.access_request_messages:
                        admin_id = query.from_user.id
                        for other_admin_id, message_id in self.access_request_messages[user_id].items():
                            # Пропускаем администратора, который нажал на кнопку
                            if other_admin_id == admin_id:
                                continue

                            try:
                                # Обновляем сообщение у другого администратора
                                await PropertyBot.safe_edit_message_text(
                                    self.application.bot,
                                    other_admin_id,
                                    message_id,
                                    f"Запрос на доступ от пользователя {user_id} был отклонен администратором {admin_id}."
                                )
                                self.logger.info(f"Сообщение о запросе на доступ обновлено у администратора {other_admin_id}")
                            except Exception as e:
                                self.logger.error(f"Ошибка при обновлении сообщения у администратора {other_admin_id}: {e}")

                        # Удаляем записи о сообщениях, так как они больше не нужны
                        del self.access_request_messages[user_id]
                else:
                    await PropertyBot.safe_answer(query, "Не удалось отклонить запрос.")
            else:
                await PropertyBot.safe_answer(query, "Неизвестная команда.")
        except Exception as e:
            self.logger.error(f"Ошибка при обработке кнопки: {e}")
            if query:
                await PropertyBot.safe_answer(query, "Произошла ошибка при обработке кнопки.")

    async def start(self) -> None:
        """
        Запускает бота.
        """
        if self.running:
            return

        self.running = True
        await self.application.initialize()
        await self.application.start()

        # Проверяем наличие updater перед запуском polling
        if hasattr(self.application, 'updater') and self.application.updater:
            await self.application.updater.start_polling(allowed_updates=[], drop_pending_updates=False, error_callback=lambda e: self.logger.error(f"Error in polling: {e}"))
        else:
            self.logger.warning("Не удалось запустить polling: updater не найден")

        self.logger.info("Бот запущен")

    async def stop(self) -> None:
        """
        Останавливает бота.
        """
        if not self.running:
            return

        # Сохраняем активные категории и список пользователей перед остановкой
        self._save_active_categories()
        self._save_allowed_users()

        self.running = False
        await self.application.stop()
        await self.application.shutdown()
        self.logger.info("Бот остановлен")

    def _get_categories_file_path(self) -> str:
        """
        Возвращает путь к файлу с активными категориями.

        Returns:
            str: Путь к файлу
        """
        from src.config.paths import get_data_path
        return get_data_path("active_categories.json")

    def _save_active_categories(self) -> None:
        """
        Сохраняет активные категории в файл.
        """
        import json
        import os

        try:
            # Преобразуем ключи в строки, так как JSON не поддерживает целочисленные ключи
            categories_dict = {str(user_id): categories for user_id, categories in self.active_categories.items()}

            # Создаем директорию, если она не существует
            file_path = self._get_categories_file_path()
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Для отладки выводим полный путь к файлу
            self.logger.info(f"Попытка сохранения в файл: {os.path.abspath(file_path)}")

            # Сохраняем в файл
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(categories_dict, f, ensure_ascii=False, indent=4)

            self.logger.info(f"Активные категории сохранены в файл: {file_path}")
        except Exception as e:
            self.logger.error(f"Ошибка при сохранении активных категорий: {e}")

    def _save_allowed_users(self) -> None:
        """
        Сохраняет список разрешенных пользователей в файл.
        """
        import json
        import os
        from src.config.paths import get_data_path

        # Путь к файлу с разрешенными пользователями
        file_path = get_data_path("allowed_users.json")

        # Создаем директорию, если она не существует
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        try:
            # Создаем словарь с разрешенными пользователями
            allowed_users_dict = {
                "allowed_users": self.allowed_users,
                "admin_users": self.admin_users,
                "super_admin_ids": self.super_admin_ids
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(allowed_users_dict, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Список разрешенных пользователей сохранен в файл: {file_path}")
        except Exception as e:
            self.logger.error(f"Ошибка при сохранении списка разрешенных пользователей: {e}")

    def _load_active_categories(self) -> None:
        """
        Загружает активные категории из файла.
        """
        import json
        import os

        file_path = self._get_categories_file_path()

        if not os.path.exists(file_path):
            self.logger.info(f"Файл с активными категориями не найден: {file_path}")
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                categories_dict = json.load(f)

            # Преобразуем ключи обратно в целые числа
            self.active_categories = {int(user_id): categories for user_id, categories in categories_dict.items()}
            self.logger.info(f"Активные категории загружены из файла: {file_path}")
        except Exception as e:
            self.logger.error(f"Ошибка при загрузке активных категорий: {e}")
            self.active_categories = {}

    def _load_allowed_users(self) -> None:
        """
        Загружает список разрешенных пользователей из файла.
        """
        import json
        import os
        from src.config.paths import get_data_path

        # Путь к файлу с разрешенными пользователями
        file_path = get_data_path("allowed_users.json")

        if not os.path.exists(file_path):
            self.logger.info(f"Файл с разрешенными пользователями не найден: {file_path}")
            # Сохраняем текущие списки пользователей
            self._save_allowed_users()
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                users_dict = json.load(f)

            # Загружаем списки пользователей
            if "allowed_users" in users_dict:
                self.allowed_users = users_dict["allowed_users"]
            if "admin_users" in users_dict:
                self.admin_users = users_dict["admin_users"]
            if "super_admin_ids" in users_dict:
                self.super_admin_ids = users_dict["super_admin_ids"]
            # Обратная совместимость с предыдущей версией
            elif "super_admin_id" in users_dict and users_dict["super_admin_id"]:
                self.super_admin_ids = [users_dict["super_admin_id"]]

            self.logger.info(f"Список разрешенных пользователей загружен из файла: {file_path}")
        except Exception as e:
            self.logger.error(f"Ошибка при загрузке списка разрешенных пользователей: {e}")

    def add_category(self, user_id: int, category: str) -> None:
        """
        Добавляет категорию для пользователя.

        Args:
            user_id: ID пользователя
            category: Категория
        """
        if user_id not in self.active_categories:
            self.active_categories[user_id] = []

        if category not in self.active_categories[user_id]:
            self.active_categories[user_id].append(category)
            self.logger.info(f"Добавлена категория {category} для пользователя {user_id}")
            # Сохраняем изменения
            self._save_active_categories()

    def remove_category(self, user_id: int, category: str) -> None:
        """
        Удаляет категорию для пользователя.

        Args:
            user_id: ID пользователя
            category: Категория
        """
        if user_id in self.active_categories and category in self.active_categories[user_id]:
            self.active_categories[user_id].remove(category)
            self.logger.info(f"Удалена категория {category} для пользователя {user_id}")
            # Сохраняем изменения
            self._save_active_categories()

    def _get_user_info(self, user_id: int) -> Optional[Dict[str, str]]:
        """
        Получает информацию о пользователе из запросов на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            Optional[Dict[str, str]]: Информация о пользователе или None, если информация не найдена
        """
        # Проверяем, есть ли информация в запросах на доступ
        request = self.access_requests.get_request(user_id)
        if request:
            return {
                "username": request.get("username", ""),
                "first_name": request.get("first_name", "")
            }
        return None

    def get_categories(self, user_id: int) -> List[str]:
        """
        Возвращает список категорий для пользователя.

        Args:
            user_id: ID пользователя

        Returns:
            List[str]: Список категорий
        """
        # Загружаем активные категории из файла перед получением списка
        self._load_active_categories()
        return self.active_categories.get(user_id, [])

    def remove_all_categories(self, user_id: int) -> None:
        """
        Удаляет все категории для пользователя.

        Args:
            user_id: ID пользователя
        """
        if user_id in self.active_categories:
            self.active_categories[user_id] = []
            self.logger.info(f"Удалены все категории для пользователя {user_id}")
            # Сохраняем изменения
            self._save_active_categories()

    async def _handle_admin(self, update: Update, context) -> None:
        """
        Обрабатывает команду /admin.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        await handle_admin(update, context, self)

    async def _handle_users(self, update: Update, context) -> None:
        """
        Обрабатывает команду /users.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        await handle_users(update, context, self)

    async def _handle_requests(self, update: Update, context) -> None:
        """
        Обрабатывает команду /requests.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        await handle_requests(update, context, self)

    async def _handle_broadcast(self, update: Update, context) -> None:
        """
        Обрабатывает команду /broadcast.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        await handle_broadcast(update, context, self)

    async def _handle_stats(self, update: Update, context) -> None:
        """
        Обрабатывает команду /stats.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        await handle_stats(update, context, self)

    async def _handle_admin_info(self, update: Update, _) -> None:
        """
        Обрабатывает команду /admininfo.
        Показывает информацию о текущих администраторах и инструкции по управлению ими.

        Args:
            update: Объект обновления
            _: Неиспользуемый контекст обработчика
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await PropertyBot.safe_reply_text(
                message,
                "Не удалось определить пользователя.",
                None
            )
            return

        user_id = message.from_user.id

        # Получаем язык пользователя
        language = self.user_language_service.get_language(user_id)

        # Проверяем, является ли пользователь суперадминистратором
        if not self.super_admin_ids or user_id not in self.super_admin_ids:
            # Получаем локализованный текст
            not_available_text = self.localization_service.get_text('admininfo_not_available', language) or "Эта команда доступна только суперадминистратору."

            await PropertyBot.safe_reply_text(
                message,
                not_available_text,
                None
            )
            return

        # Получаем локализованные тексты
        super_admins_text = self.localization_service.get_text('admininfo_super_admins', language) or "Суперадминистраторы:"
        current_admins_text = self.localization_service.get_text('admininfo_current_admins', language) or "Текущие администраторы:"
        management_text = self.localization_service.get_text('admininfo_management', language) or "Управление администраторами:"
        super_admin_instructions_text = self.localization_service.get_text('admininfo_super_admin_instructions', language) or "Вы являетесь суперадминистратором и можете управлять администраторами с помощью следующих команд:"
        add_admin_text = self.localization_service.get_text('admininfo_add_admin', language) or "добавить нового администратора"
        remove_admin_text = self.localization_service.get_text('admininfo_remove_admin', language) or "удалить администратора"
        config_title_text = self.localization_service.get_text('admininfo_config_title', language) or "Настройка через конфигурацию:"
        config_file_text = self.localization_service.get_text('admininfo_config_file', language) or "Изменить файл"
        env_vars_text = self.localization_service.get_text('admininfo_env_vars', language) or "Или изменить переменные окружения:"
        restart_required_text = self.localization_service.get_text('admininfo_restart_required', language) or "После изменения необходимо перезапустить бота."

        # Формируем информацию о суперадминистраторах
        super_admin_info = ""
        if self.super_admin_ids:
            super_admins_str = ", ".join(str(admin_id) for admin_id in self.super_admin_ids)
            super_admin_info = f"<b>{super_admins_text}</b> {super_admins_str}\n\n"

        # Формируем список администраторов
        admin_list = ""
        for i, admin_id in enumerate(self.admin_users, 1):
            admin_list += f"{i}. ID: {admin_id}\n"

        # Формируем инструкцию по управлению администраторами
        instructions = f"\n\n<b>{management_text}</b>\n\n"

        # Показываем команды управления суперадминистратору
        instructions += (
            f"{super_admin_instructions_text}\n\n"
            f"1. <code>/addadmin ID</code> - {add_admin_text}\n"
            f"2. <code>/removeadmin ID</code> - {remove_admin_text}\n\n"
        )

        # Добавляем информацию о настройке через конфигурацию
        instructions += (
            f"<b>{config_title_text}</b>\n\n"
            f"1. {config_file_text} <code>config/telegram/bot.yaml</code>:\n"
            f"<pre>super_admin_id: 123456789  # ID суперадминистратора\n\nadmin_users:\n  - 123456789  # ID первого администратора\n  - 987654321  # ID второго администратора</pre>\n\n"
            f"2. {env_vars_text}\n"
            f"<pre>TELEGRAM_SUPER_ADMIN_ID=123456789\nTELEGRAM_ADMIN_USERS=123456789,987654321</pre>\n\n"
            f"3. {restart_required_text}"
        )

        # Отправляем сообщение
        await PropertyBot.safe_reply_text(
            message,
            f"{super_admin_info}<b>{current_admins_text}</b>\n\n{admin_list}{instructions}",
            None,
            'HTML'
        )

    async def _handle_add_admin(self, update: Update, context) -> None:
        """
        Обрабатывает команду /addadmin.
        Добавляет нового администратора (только для суперадминистратора).

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await PropertyBot.safe_reply_text(
        message,
        "Не удалось определить пользователя.",
        None
    )
            return

        user_id = message.from_user.id

        # Проверяем, является ли пользователь суперадминистратором
        if not self.super_admin_ids or user_id not in self.super_admin_ids:
            await PropertyBot.safe_reply_text(
        message,
        "Эта команда доступна только суперадминистратору.",
        None
    )
            return

        # Получаем аргументы команды
        args = context.args

        # Проверяем наличие аргументов
        if not args:
            await PropertyBot.safe_reply_text(
        message,
        "Используйте команду /addadmin <user_id> для добавления администратора."
            ,
        None
    )
            return

        # Получаем ID пользователя для добавления
        try:
            new_admin_id = int(args[0])
        except ValueError:
            await PropertyBot.safe_reply_text(
        message,
        "Неверный формат ID пользователя. Используйте числовой ID."
            ,
        None
    )
            return

        # Проверяем, есть ли пользователь уже в списке администраторов
        if new_admin_id in self.admin_users:
            await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {new_admin_id} уже в списке администраторов."
            ,
        None
    )
            return

        # Добавляем пользователя в список администраторов
        self.admin_users.append(new_admin_id)
        self.logger.info(f"Пользователь {new_admin_id} добавлен в список администраторов")

        # Сохраняем изменения
        self._save_allowed_users()

        # Добавляем пользователя в список разрешенных, если его там еще нет
        if new_admin_id not in self.allowed_users:
            self.allowed_users.append(new_admin_id)
            self.logger.info(f"Пользователь {new_admin_id} добавлен в список разрешенных")
            self._save_allowed_users()

        # Отправляем уведомление пользователю
        try:
            await PropertyBot.safe_send_message(self.application.bot, new_admin_id, "Вам предоставлены права администратора бота."
            )
        except Exception as e:
            self.logger.error(f"Ошибка при отправке уведомления пользователю {new_admin_id}: {e}", None)

        # Отвечаем суперадминистратору
        await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {new_admin_id} добавлен в список администраторов."
        ,
        None
    )

    async def _handle_allow(self, update: Update, context) -> None:
        """
        Обрабатывает команду /allow.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await PropertyBot.safe_reply_text(
        message,
        "Не удалось определить пользователя.",
        None
    )
            return

        user_id = message.from_user.id

        # Проверяем, является ли пользователь администратором
        if user_id not in self.admin_users:
            await PropertyBot.safe_reply_text(
        message,
        "У вас нет прав администратора.",
        None
    )
            return

        # Получаем аргументы команды
        args = context.args

        # Проверяем наличие аргументов
        if not args:
            await PropertyBot.safe_reply_text(
        message,
        "Используйте команду /allow <user_id> для добавления пользователя."
            ,
        None
    )
            return

        # Получаем ID пользователя для добавления
        try:
            new_user_id = int(args[0])
        except ValueError:
            await PropertyBot.safe_reply_text(
        message,
        "Неверный формат ID пользователя. Используйте числовой ID."
            ,
        None
    )
            return

        # Проверяем, есть ли пользователь уже в списке разрешенных
        if new_user_id in self.allowed_users:
            await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {new_user_id} уже в списке разрешенных."
            ,
        None
    )
            return

        # Добавляем пользователя в список разрешенных
        self.allowed_users.append(new_user_id)
        self.logger.info(f"Пользователь {new_user_id} добавлен в список разрешенных")

        # Сохраняем изменения
        self._save_allowed_users()

        # Обновляем статус запроса на доступ, если он существует
        if new_user_id in self.access_requests.requests:
            # Если запрос был отклонен, меняем его статус на "approved"
            self.access_requests.approve_request(new_user_id)
            self.logger.info(f"Статус запроса на доступ пользователя {new_user_id} изменен на \"approved\"")

        # Отправляем уведомление пользователю и главное меню
        try:
            # Сначала отправляем сообщение о предоставлении доступа
            await PropertyBot.safe_send_message(self.application.bot, new_user_id, "Вам предоставлен доступ к боту. Теперь вы можете использовать все его функции."
            )

            # Затем отправляем приветствие и главное меню
            from src.presentation.telegram.menu import get_main_menu

            # Получаем информацию о пользователе
            user_info = await self.application.bot.get_chat(chat_id=new_user_id, api_kwargs={})
            user_name = user_info.first_name if user_info and user_info.first_name else "Друг"

            # Отправляем приветствие и главное меню
            await PropertyBot.safe_send_message(
                self.application.bot,
                new_user_id,
                f"Привет, {user_name}! Я бот для уведомлений о новых объявлениях OLX.\n\n"
                "Используйте кнопки меню для управления ботом или команду /help для получения справки.",
                get_main_menu()
            )
        except Exception as e:
            self.logger.error(f"Ошибка при отправке уведомления пользователю {new_user_id}: {e}")

        # Отвечаем администратору
        await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {new_user_id} добавлен в список разрешенных."
        ,
        None
    )

    async def _handle_remove_admin(self, update: Update, context) -> None:
        """
        Обрабатывает команду /removeadmin.
        Удаляет администратора (только для суперадминистратора).

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await PropertyBot.safe_reply_text(
        message,
        "Не удалось определить пользователя.",
        None
    )
            return

        user_id = message.from_user.id

        # Проверяем, является ли пользователь суперадминистратором
        if not self.super_admin_ids or user_id not in self.super_admin_ids:
            await PropertyBot.safe_reply_text(
        message,
        "Эта команда доступна только суперадминистратору.",
        None
    )
            return

        # Получаем аргументы команды
        args = context.args

        # Проверяем наличие аргументов
        if not args:
            await PropertyBot.safe_reply_text(
        message,
        "Используйте команду /removeadmin <user_id> для удаления администратора."
            ,
        None
    )
            return

        # Получаем ID пользователя для удаления
        try:
            remove_admin_id = int(args[0])
        except ValueError:
            await PropertyBot.safe_reply_text(
        message,
        "Неверный формат ID пользователя. Используйте числовой ID."
            ,
        None
    )
            return

        # Проверяем, не пытается ли пользователь удалить суперадминистратора
        if remove_admin_id in self.super_admin_ids:
            await PropertyBot.safe_reply_text(
        message,
        "Нельзя удалить суперадминистратора."
            ,
        None
    )
            return

        # Проверяем, есть ли пользователь в списке администраторов
        if remove_admin_id not in self.admin_users:
            await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {remove_admin_id} не найден в списке администраторов."
            ,
        None
    )
            return

        # Удаляем пользователя из списка администраторов
        self.admin_users.remove(remove_admin_id)
        self.logger.info(f"Пользователь {remove_admin_id} удален из списка администраторов")

        # Сохраняем изменения
        self._save_allowed_users()

        # Отправляем уведомление пользователю
        try:
            await PropertyBot.safe_send_message(self.application.bot, remove_admin_id, "Ваши права администратора были отозваны."
            )
        except Exception as e:
            self.logger.error(f"Ошибка при отправке уведомления пользователю {remove_admin_id}: {e}", None)

        # Отвечаем суперадминистратору
        await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {remove_admin_id} удален из списка администраторов."
        ,
        None
    )

    async def _handle_disallow(self, update: Update, context) -> None:
        """
        Обрабатывает команду /disallow.

        Args:
            update: Объект обновления
            context: Контекст обработчика
        """
        # Получаем сообщение
        message = update.message
        if not message:
            return

        # Получаем ID пользователя
        if not message.from_user:
            await PropertyBot.safe_reply_text(
        message,
        "Не удалось определить пользователя.",
        None
    )
            return

        user_id = message.from_user.id

        # Проверяем, является ли пользователь администратором
        if user_id not in self.admin_users:
            await PropertyBot.safe_reply_text(
        message,
        "У вас нет прав администратора.",
        None
    )
            return

        # Получаем аргументы команды
        args = context.args

        # Проверяем наличие аргументов
        if not args:
            await PropertyBot.safe_reply_text(
        message,
        "Используйте команду /disallow <user_id> для удаления пользователя."
            ,
        None
    )
            return

        # Получаем ID пользователя для удаления
        try:
            remove_user_id = int(args[0])
        except ValueError:
            await PropertyBot.safe_reply_text(
        message,
        "Неверный формат ID пользователя. Используйте числовой ID."
            ,
        None
    )
            return

        # Проверяем, есть ли пользователь в списке разрешенных
        if remove_user_id not in self.allowed_users:
            await PropertyBot.safe_reply_text(
        message,
        f"Пользователя {remove_user_id} нет в списке разрешенных."
            ,
        None
    )
            return

        # Проверяем, не является ли пользователь администратором
        if remove_user_id in self.admin_users:
            await PropertyBot.safe_reply_text(
        message,
        f"Нельзя удалить администратора {remove_user_id} из списка разрешенных."
            ,
        None
    )
            return

        # Удаляем пользователя из списка разрешенных
        self.allowed_users.remove(remove_user_id)
        self.logger.info(f"Пользователь {remove_user_id} удален из списка разрешенных")

        # Сохраняем изменения
        self._save_allowed_users()

        # Удаляем все категории пользователя
        self.remove_all_categories(remove_user_id)

        # Изменяем статус запроса на доступ на "rejected"
        self.access_requests.reject_request(remove_user_id)
        self.logger.info(f"Запрос на доступ пользователя {remove_user_id} отклонен")

        # Отправляем уведомление пользователю
        try:
            # Получаем язык пользователя
            language = self.user_language_service.get_language(remove_user_id)
            # Получаем локализованный текст
            access_revoked_text = self.localization_service.get_text('access_revoked', language)

            await PropertyBot.safe_send_message(self.application.bot, remove_user_id, access_revoked_text)
        except Exception as e:
            self.logger.error(f"Ошибка при отправке уведомления пользователю {remove_user_id}: {e}", None)

        # Отвечаем администратору
        await PropertyBot.safe_reply_text(
        message,
        f"Пользователь {remove_user_id} удален из списка разрешенных."
        ,
        None
    )

    @staticmethod
    async def update_main_keyboard(bot, chat_id, localization_service, user_language_service, user_id, message_id=None):
        """
        Обновляет главную клавиатуру в чате.

        Args:
            bot: Объект бота
            chat_id: ID чата
            localization_service: Сервис локализации
            user_language_service: Сервис управления языком пользователя
            user_id: ID пользователя
            message_id: ID сообщения для обновления (опционально)
        """
        try:
            logger = logging.getLogger("telegram_bot")
            logger.info(f"Обновление клавиатуры для пользователя {user_id} в чате {chat_id}")

            # Импортируем функцию для создания локализованного главного меню
            from src.presentation.telegram.localization.menu import get_localized_main_menu

            # Получаем язык пользователя
            language = user_language_service.get_language(user_id)
            logger.info(f"Текущий язык пользователя {user_id}: {language}")

            # Получаем локализованный текст для заголовка главного меню
            main_menu_title = localization_service.get_text('main_menu_title', language) or "Главное меню:"
            if language == 'ua':
                main_menu_title = "Головне меню:"

            # Создаем локализованное главное меню
            localized_main_menu = get_localized_main_menu(localization_service, user_language_service, user_id)

            # Если указан ID сообщения, обновляем существующую клавиатуру
            if message_id:
                try:
                    # Пробуем обновить существующее сообщение с клавиатурой
                    # Используем безопасный метод для обновления клавиатуры
                    if hasattr(PropertyBot, 'safe_edit_message_reply_markup'):
                        await PropertyBot.safe_edit_message_reply_markup(
                            bot=bot,
                            chat_id=chat_id,
                            message_id=message_id,
                            reply_markup=localized_main_menu
                        )
                    else:
                        # Если безопасный метод не существует, используем стандартный
                        await bot.edit_message_reply_markup(
                            chat_id=chat_id,
                            message_id=message_id,
                            reply_markup=localized_main_menu
                        )
                    logger.info(f"Существующая клавиатура обновлена для пользователя {user_id} в чате {chat_id}")
                    return
                except Exception as e:
                    logger.warning(f"Не удалось обновить существующую клавиатуру: {e}. Отправляем новую.")

            # Отправляем новую клавиатуру с использованием safe_send_message
            await PropertyBot.safe_send_message(
                bot=bot,
                chat_id=chat_id,
                text=main_menu_title,
                reply_markup=localized_main_menu
            )

            logger.info(f"Новая клавиатура отправлена для пользователя {user_id} в чате {chat_id}")

        except Exception as e:
            logger = logging.getLogger("telegram_bot")
            logger.error(f"Ошибка при обновлении клавиатуры: {e}")


if __name__ == "__main__":
    # Импортируем необходимые модули для запуска бота
    import asyncio
    import logging
    import sys
    from pathlib import Path

    # Добавляем корневую директорию проекта в sys.path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))

    # Импортируем модуль __main__ и запускаем функцию main
    from src.presentation.telegram.__main__ import main

    # Запускаем бота
    asyncio.run(main())
