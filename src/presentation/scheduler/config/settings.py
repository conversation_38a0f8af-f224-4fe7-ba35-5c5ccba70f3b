"""
Настройки планировщика.
"""
from typing import Dict, List, Optional

from src.config.settings import load_scheduler_config


class SchedulerConfig:
    """
    Конфигурация планировщика.
    """

    def __init__(self, config_data: Optional[Dict] = None):
        """
        Инициализирует конфигурацию планировщика.

        Args:
            config_data: Данные конфигурации (опционально)
        """
        self.config_data = config_data or load_scheduler_config()
        self.scheduler_config = self.config_data
        print(f"DEBUG [SchedulerConfig.__init__]: config_data={self.config_data}, scheduler_config={self.scheduler_config}")

    def get_interval(self, category: Optional[str] = None) -> int:
        """
        Возвращает интервал парсинга.

        Args:
            category: Категория (опционально)

        Returns:
            int: Интервал парсинга в секундах
        """
        # Если указана категория, пытаемся получить интервал для нее
        if category:
            category_intervals = self.scheduler_config.get('category_intervals', {})
            if category in category_intervals:
                return category_intervals[category]

        # Иначе возвращаем общий интервал
        return self.scheduler_config.get('interval', 3600)

    def get_limit(self, category: Optional[str] = None) -> Optional[int]:
        """
        Возвращает лимит объявлений.

        Args:
            category: Категория (опционально)

        Returns:
            Optional[int]: Лимит объявлений или None, если лимит не указан
        """
        # Если указана категория, пытаемся получить лимит для нее
        if category:
            category_limits = self.scheduler_config.get('category_limits', {})
            if category in category_limits:
                return category_limits[category]

        # Иначе возвращаем общий лимит
        return self.scheduler_config.get('limit')

    def get_categories(self) -> List[str]:
        """
        Возвращает список категорий для парсинга.

        Returns:
            List[str]: Список категорий
        """
        return self.scheduler_config.get('categories', [])

    def get_chat_ids(self) -> List[int]:
        """
        Возвращает список ID чатов для отправки уведомлений.

        Returns:
            List[int]: Список ID чатов
        """
        return self.config_data.get('telegram', {}).get('chat_ids', [])

    def get_start_delay(self, category: Optional[str] = None) -> int:
        """
        Возвращает задержку перед первым запуском.

        Args:
            category: Категория (опционально)

        Returns:
            int: Задержка в минутах
        """
        # Если указана категория, пытаемся получить задержку для нее
        if category:
            category_delays = self.scheduler_config.get('category_delays', {})
            if category in category_delays:
                return category_delays[category]

        # Иначе возвращаем общую задержку
        return self.scheduler_config.get('start_delay', 0)

    def reload_config(self):
        """
        Перезагружает конфигурацию из файла.
        """
        self.config_data = load_scheduler_config()
        self.scheduler_config = self.config_data
        print(f"DEBUG [SchedulerConfig.reload_config]: config_data={self.config_data}, scheduler_config={self.scheduler_config}")

    def get_category_offset(self) -> int:
        """
        Возвращает смещение времени запуска между категориями.

        Returns:
            int: Смещение в минутах
        """
        category_offset = self.scheduler_config.get('category_offset')
        # Если значение не указано, используем значение по умолчанию
        if category_offset is None:
            return 1
        return category_offset

    def is_enabled(self, category: Optional[str] = None) -> bool:
        """
        Проверяет, включен ли парсинг.

        Args:
            category: Категория (опционально)

        Returns:
            bool: True, если парсинг включен, иначе False
        """
        # Перезагружаем конфигурацию перед чтением значения
        self.reload_config()

        # Если указана категория, пытаемся получить статус для нее
        if category:
            category_enabled = self.scheduler_config.get('category_enabled', {})
            if category in category_enabled:
                return category_enabled[category]

        # Иначе возвращаем общий статус
        enabled = self.scheduler_config.get('enabled', True)
        print(f"DEBUG [SchedulerConfig.is_enabled]: scheduler_config={self.scheduler_config}, enabled={enabled}")
        return enabled

    def is_schedule_enabled(self) -> bool:
        """
        Проверяет, включено ли расписание.

        Returns:
            bool: True, если расписание включено, иначе False
        """
        # Перезагружаем конфигурацию перед чтением значения
        self.reload_config()
        schedule = self.scheduler_config.get('schedule', {})
        enabled = schedule.get('enabled', True)
        print(f"DEBUG [SchedulerConfig.is_schedule_enabled]: scheduler_config={self.scheduler_config}, schedule={schedule}, enabled={enabled}")
        return enabled

    def get_schedule_start_time(self) -> str:
        """
        Возвращает время начала работы планировщика.

        Returns:
            str: Время начала работы в формате "HH:MM"
        """
        schedule = self.scheduler_config.get('schedule', {})
        start_time = schedule.get('start_time')
        # Если значение не указано или пустое, используем значение по умолчанию
        if start_time is None or start_time == "":
            return "08:00"
        return start_time

    def get_schedule_end_time(self) -> str:
        """
        Возвращает время окончания работы планировщика.

        Returns:
            str: Время окончания работы в формате "HH:MM"
        """
        schedule = self.scheduler_config.get('schedule', {})
        end_time = schedule.get('end_time')
        # Если значение не указано или пустое, используем значение по умолчанию
        if end_time is None or end_time == "":
            return "20:00"
        return end_time

    def get_schedule_interval_hours(self) -> int:
        """
        Возвращает интервал запуска в часах.

        Returns:
            int: Интервал запуска в часах
        """
        schedule = self.scheduler_config.get('schedule', {})
        interval_hours = schedule.get('interval_hours')
        # Если значение не указано или пустое, используем значение по умолчанию
        if interval_hours is None:
            return 2
        return interval_hours

    def get_schedule_days_of_week(self) -> List[int]:
        """
        Возвращает список дней недели для запуска.

        Returns:
            List[int]: Список дней недели (1-7, где 1 - понедельник, 7 - воскресенье)
        """
        schedule = self.scheduler_config.get('schedule', {})
        days = schedule.get('days_of_week')
        # Если значение не указано или пустое, используем значение по умолчанию
        if days is None or days == []:
            days = [1, 2, 3, 4, 5, 6, 7]  # По умолчанию все дни недели
        print(f"DEBUG [SchedulerConfig.get_schedule_days_of_week]: schedule={schedule}, days={days}")
        return days

    def should_skip_holidays(self) -> bool:
        """
        Проверяет, нужно ли пропускать праздничные дни.

        Returns:
            bool: True, если нужно пропускать праздничные дни, иначе False
        """
        schedule = self.scheduler_config.get('schedule', {})
        skip_holidays = schedule.get('skip_holidays')
        # Если значение не указано, используем значение по умолчанию
        if skip_holidays is None:
            return False
        return skip_holidays

    def is_parse_only_subscribed_categories(self) -> bool:
        """
        Проверяет, нужно ли парсить только категории, на которые подписаны пользователи.

        Returns:
            bool: True, если нужно парсить только категории с подписчиками, иначе False
        """
        # Перезагружаем конфигурацию перед чтением значения
        self.reload_config()
        parse_only_subscribed = self.scheduler_config.get('parse_only_subscribed_categories')
        # Если значение не указано, используем значение по умолчанию
        if parse_only_subscribed is None:
            return False
        return parse_only_subscribed


