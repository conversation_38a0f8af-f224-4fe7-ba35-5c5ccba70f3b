"""
Базовый класс задачи.
"""
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional


class Task(ABC):
    """
    Базовый класс задачи.
    """

    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        """
        Инициализирует задачу.

        Args:
            name: Имя задачи
            logger: Логг<PERSON><PERSON> (опционально)
        """
        self.name = name
        self.logger = logger or logging.getLogger(__name__)

    @abstractmethod
    async def run(self) -> Any:
        """
        Выполняет задачу.

        Returns:
            Any: Результат выполнения задачи
        """
        pass

    def on_success(self, result: Any) -> None:
        """
        Обрабатывает успешное выполнение задачи.

        Args:
            result: Результат выполнения задачи
        """
        self.logger.info(f"Задача {self.name} успешно выполнена")

    def on_error(self, error: Exception) -> None:
        """
        Обрабатывает ошибку при выполнении задачи.

        Args:
            error: Ошибка
        """
        self.logger.error(f"Ошибка при выполнении задачи {self.name}: {error}")

    async def execute(self) -> None:
        """
        Выполняет задачу и обрабатывает результат.
        """
        try:
            # Используем await для вызова асинхронного метода run
            result = await self.run()
            self.on_success(result)
        except Exception as e:
            self.on_error(e)


class PeriodicTask(Task):
    """
    Периодическая задача.
    """

    def __init__(self, name: str, interval: int, logger: Optional[logging.Logger] = None):
        """
        Инициализирует периодическую задачу.

        Args:
            name: Имя задачи
            interval: Интервал выполнения в секундах
            logger: Логгер (опционально)
        """
        super().__init__(name, logger)
        self.interval = interval

    def get_interval(self) -> int:
        """
        Возвращает интервал выполнения.

        Returns:
            int: Интервал выполнения в секундах
        """
        return self.interval

    def set_interval(self, interval: int) -> None:
        """
        Устанавливает интервал выполнения.

        Args:
            interval: Интервал выполнения в секундах
        """
        self.interval = interval


class ConfigurableTask(PeriodicTask):
    """
    Настраиваемая задача.
    """

    def __init__(self, name: str, interval: int, config: Dict[str, Any],
                logger: Optional[logging.Logger] = None):
        """
        Инициализирует настраиваемую задачу.

        Args:
            name: Имя задачи
            interval: Интервал выполнения в секундах
            config: Конфигурация задачи
            logger: Логгер (опционально)
        """
        super().__init__(name, interval, logger)
        self.config = config

    def get_config(self) -> Dict[str, Any]:
        """
        Возвращает конфигурацию задачи.

        Returns:
            Dict[str, Any]: Конфигурация задачи
        """
        return self.config

    def set_config(self, config: Dict[str, Any]) -> None:
        """
        Устанавливает конфигурацию задачи.

        Args:
            config: Конфигурация задачи
        """
        self.config = config

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Возвращает значение из конфигурации задачи.

        Args:
            key: Ключ
            default: Значение по умолчанию (опционально)

        Returns:
            Any: Значение из конфигурации задачи или значение по умолчанию
        """
        return self.config.get(key, default)

    def set_config_value(self, key: str, value: Any) -> None:
        """
        Устанавливает значение в конфигурации задачи.

        Args:
            key: Ключ
            value: Значение
        """
        self.config[key] = value
