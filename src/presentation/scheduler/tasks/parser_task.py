"""
Задача парсинга.
"""
import logging
from typing import List, Optional, Tuple

from src.application.services.property import PropertyService
from src.presentation.scheduler.tasks.base import ConfigurableTask


class ParserTask(ConfigurableTask):
    """
    Задача парсинга.
    """

    def __init__(self, name: str, interval: int, property_service: PropertyService,
                category: str, chat_ids: List[int], limit: Optional[int] = None,
                logger: Optional[logging.Logger] = None, error_chat_id: Optional[int] = None):
        """
        Инициализирует задачу парсинга.

        Args:
            name: Имя задачи
            interval: Интервал выполнения в секундах
            property_service: Сервис работы с объектами недвижимости
            category: Категория для парсинга
            chat_ids: Список ID чатов для отправки уведомлений
            limit: Максимальное количество объявлений для обработки (опционально)
            logger: Логгер (опционально)
            error_chat_id: ID чата для отправки уведомлений об ошибках (опционально)
        """
        # Создаем конфигурацию задачи
        config = {
            'category': category,
            'chat_ids': chat_ids,
            'limit': limit,
            'error_chat_id': error_chat_id
        }

        super().__init__(name, interval, config, logger)
        self.property_service = property_service

    async def run(self) -> Tuple[int, int, int]:
        """
        Выполняет задачу парсинга.

        Returns:
            Tuple[int, int, int]: Количество новых, обработанных и ошибочных объявлений
        """
        # Получаем параметры из конфигурации
        category = self.get_config_value('category')
        chat_ids = self.get_config_value('chat_ids', [])
        limit = self.get_config_value('limit')
        error_chat_id = self.get_config_value('error_chat_id')

        self.logger.info(f"Начало парсинга категории: {category}")
        if error_chat_id:
            self.logger.info(f"ID чата для отправки уведомлений об ошибках: {error_chat_id}")

        # Выполняем парсинг
        # Используем await, так как метод process_category является асинхронным
        new_count, processed_count, error_count = await self.property_service.process_category(
            category=category,
            chat_ids=chat_ids,
            limit=limit,
            error_chat_id=self.get_config_value('error_chat_id')
        )

        self.logger.info(f"Парсинг категории {category} завершен. "
                       f"Новых: {new_count}, Обработано: {processed_count}, Ошибок: {error_count}")

        return new_count, processed_count, error_count

    def on_success(self, result: Tuple[int, int, int]) -> None:
        """
        Обрабатывает успешное выполнение задачи.

        Args:
            result: Результат выполнения задачи (новые, обработанные, ошибки)
        """
        new_count, processed_count, error_count = result
        category = self.get_config_value('category')

        self.logger.info(f"Задача парсинга категории {category} успешно выполнена. "
                       f"Новых: {new_count}, Обработано: {processed_count}, Ошибок: {error_count}")

    def on_error(self, error: Exception) -> None:
        """
        Обрабатывает ошибку при выполнении задачи.

        Args:
            error: Ошибка
        """
        category = self.get_config_value('category')
        self.logger.error(f"Ошибка при парсинге категории {category}: {error}")

    def __str__(self) -> str:
        """
        Возвращает строковое представление задачи.

        Returns:
            str: Строковое представление задачи
        """
        category = self.get_config_value('category')
        limit = self.get_config_value('limit')
        return f"ParserTask(name={self.name}, category={category}, limit={limit}, interval={self.interval})"
