"""
Исполнитель задач.
"""
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Callable, Dict, List, Optional, Union

from src.presentation.scheduler.core.scheduler import Task


class TaskExecutor:
    """
    Исполнитель задач.
    """
    
    def __init__(self, max_workers: int = 5, logger: Optional[logging.Logger] = None):
        """
        Инициализирует исполнитель задач.
        
        Args:
            max_workers: Максимальное количество рабочих потоков (опционально)
            logger: Логгер (опционально)
        """
        self.max_workers = max_workers
        self.logger = logger or logging.getLogger(__name__)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running_tasks: Dict[str, Task] = {}
    
    def execute(self, task: Task) -> None:
        """
        Выполняет задачу.
        
        Args:
            task: Задача
        """
        if task.name in self.running_tasks:
            self.logger.warning(f"Задача {task.name} уже выполняется")
            return
        
        self.logger.info(f"Запуск задачи {task.name}")
        self.running_tasks[task.name] = task
        
        # Запускаем задачу в пуле потоков
        future = self.executor.submit(self._execute_task, task)
        future.add_done_callback(lambda f: self._task_done(task.name))
    
    def _execute_task(self, task: Task) -> None:
        """
        Выполняет задачу.
        
        Args:
            task: Задача
        """
        task.last_run = datetime.now()
        
        try:
            task.func(*task.args, **task.kwargs)
        except Exception as e:
            self.logger.error(f"Ошибка при выполнении задачи {task.name}: {e}")
    
    def _task_done(self, task_name: str) -> None:
        """
        Обрабатывает завершение задачи.
        
        Args:
            task_name: Имя задачи
        """
        if task_name in self.running_tasks:
            del self.running_tasks[task_name]
            self.logger.info(f"Задача {task_name} завершена")
    
    def shutdown(self, wait: bool = True) -> None:
        """
        Останавливает исполнитель задач.
        
        Args:
            wait: Ожидать завершения выполняющихся задач (опционально)
        """
        self.executor.shutdown(wait=wait)
        self.logger.info("Исполнитель задач остановлен")


class ParallelTaskExecutor:
    """
    Параллельный исполнитель задач.
    """
    
    def __init__(self, max_workers: int = 5, logger: Optional[logging.Logger] = None):
        """
        Инициализирует параллельный исполнитель задач.
        
        Args:
            max_workers: Максимальное количество рабочих потоков (опционально)
            logger: Логгер (опционально)
        """
        self.max_workers = max_workers
        self.logger = logger or logging.getLogger(__name__)
        self.executors: Dict[str, TaskExecutor] = {}
    
    def execute(self, category: str, task: Task) -> None:
        """
        Выполняет задачу в указанной категории.
        
        Args:
            category: Категория
            task: Задача
        """
        # Создаем исполнитель для категории, если его нет
        if category not in self.executors:
            self.executors[category] = TaskExecutor(
                max_workers=self.max_workers,
                logger=self.logger
            )
        
        # Выполняем задачу
        self.executors[category].execute(task)
    
    def shutdown(self, wait: bool = True) -> None:
        """
        Останавливает все исполнители задач.
        
        Args:
            wait: Ожидать завершения выполняющихся задач (опционально)
        """
        for category, executor in self.executors.items():
            self.logger.info(f"Остановка исполнителя задач для категории {category}")
            executor.shutdown(wait=wait)
        
        self.logger.info("Все исполнители задач остановлены")
