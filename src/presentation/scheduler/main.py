"""
Точка входа для планировщика задач.
"""
import argparse
import json
import logging
import os
import signal
import sys
import time as time_module
import pytz
from datetime import datetime, timedelta, time, timezone
from src.application.services.dummy_notification import DummyNotificationService
from src.application.services.notification import NotificationService
from src.application.services.localized_notification import LocalizedNotificationService
from src.application.services.property import PropertyService
from src.config import settings
from src.config.paths import get_data_path
from src.domain.services.property_validator import PropertyValidator
from src.infrastructure.external.telegram.notification_sender import TelegramNotificationSender
from src.infrastructure.logging.logger import setup_logger
from src.infrastructure.parsers.olx.olx_parser import OlxParser
from src.infrastructure.persistence.processed_ad_storage import DbProcessedAdStorage
from src.presentation.scheduler.core import ParsingScheduler
from src.presentation.scheduler.config.settings import SchedulerConfig
from src.infrastructure.storage.json_storage import JsonStorage
from src.presentation.telegram.localization.factory import create_localization_services
from src.presentation.telegram.localization.notification_factory import create_localized_notification_service


def parse_args():
    """
    Парсит аргументы командной строки.

    Returns:
        argparse.Namespace: Аргументы командной строки
    """
    parser = argparse.ArgumentParser(description='Планировщик задач для парсинга OLX')

    parser.add_argument('--categories', '-c', help='Категории для парсинга (через запятую). Если не указано, используются категории из файла конфигурации')
    parser.add_argument('--interval', '-i', type=int, help='Интервал парсинга в секундах. Если не указан, используется значение из файла конфигурации или 3600 (1 час)')
    parser.add_argument('--limit', '-l', type=int, help='Максимальное количество объявлений для обработки')
    parser.add_argument('--delay', '-d', type=int, default=0, help='Задержка перед первым запуском в минутах (по умолчанию 0)')
    parser.add_argument('--config', help='Путь к файлу конфигурации')
    parser.add_argument('--verbose', '-v', action='store_true', help='Подробный вывод')
    parser.add_argument('--force', '-f', action='store_true', help='Принудительный запуск вне рабочего времени')

    return parser.parse_args()


def main():
    """
    Точка входа для планировщика задач.
    """
    # Импортируем модуль логирования
    import logging

    # Парсим аргументы командной строки
    args = parse_args()

    # Настраиваем логирование
    log_level = logging.DEBUG if args.verbose else logging.INFO

    # Очищаем и настраиваем лог шедулера
    scheduler_log_file = "logs/olx_scheduler.log"
    logger = setup_logger("olx_scheduler", log_level=log_level, log_file=scheduler_log_file, clear_logs=True)
    logger.info(f"Файл логов {scheduler_log_file} очищен")

    # Очищаем лог парсера
    parser_log_file = "logs/olx_parser.log"
    from src.infrastructure.logging.logger import clear_log_file
    clear_log_file(parser_log_file)
    logger.info(f"Файл логов {parser_log_file} очищен")

    # Загружаем конфигурацию планировщика
    from src.config.settings import load_scheduler_config
    scheduler_config_data = load_scheduler_config()
    scheduler_config = SchedulerConfig(config_data=scheduler_config_data)

    # Разбиваем категории
    if args.categories:
        category_list = [category.strip() for category in args.categories.split(',')]
    else:
        # Используем категории из файла конфигурации
        category_list = scheduler_config_data.get('categories', [])
        if not category_list:
            logger.error("Не указаны категории ни в командной строке, ни в файле конфигурации")
            print("Ошибка: Не указаны категории ни в командной строке, ни в файле конфигурации")
            return 1

    logger.info(f"Запуск планировщика для категорий: {', '.join(category_list)}")

    # Загружаем конфигурацию
    if args.config:
        # TODO: Реализовать загрузку конфигурации из указанного файла
        logger.info(f"Загрузка конфигурации из файла: {args.config}")

    # Используем централизованные настройки
    # Получаем настройки хранилища
    storage_file_path = settings.get_storage_file_path()

    # Если путь не абсолютный, преобразуем его относительно директории данных
    if storage_file_path and not os.path.isabs(storage_file_path):
        storage_file_path = get_data_path(storage_file_path)

    # Создаем хранилище
    json_storage = JsonStorage(file_path=storage_file_path, logger=logger)
    processed_ad_storage = DbProcessedAdStorage(json_storage=json_storage, logger=logger)

    # Создаем валидатор
    property_validator = PropertyValidator()

    # Загружаем категории
    categories_file = get_data_path("categories.json")
    if not os.path.exists(categories_file):
        # Пробуем найти файл в директории config/data
        categories_file = os.path.join(os.path.dirname(__file__), "../../../config/data/categories.json")
        if not os.path.exists(categories_file):
            # Пробуем найти файл в директории src/config/data
            categories_file = os.path.join(os.path.dirname(__file__), "../../../src/config/data/categories.json")

    try:
        with open(categories_file, 'r', encoding='utf-8') as f:
            categories_data = json.load(f)
    except Exception as e:
        logger.error(f"Ошибка при загрузке категорий: {e}")
        categories_data = {"categories": []}

    # Проверяем, существуют ли указанные категории
    valid_categories = []
    for category in category_list:
        category_exists = False
        for cat in categories_data.get("categories", []):
            if cat.get("id") == category:
                category_exists = True
                break

        if category_exists:
            valid_categories.append(category)
        else:
            logger.warning(f"Категория {category} не найдена")
            print(f"Предупреждение: Категория {category} не найдена")

    if not valid_categories:
        logger.error("Не найдено ни одной валидной категории")
        print("Ошибка: Не найдено ни одной валидной категории")
        return 1

    # Загружаем настройки OLX
    olx_config = settings.olx

    # Создаем парсер и передаем ему хранилище
    parser = OlxParser(config=olx_config, processed_ad_storage=processed_ad_storage, logger=logger)

    # Загружаем настройки Telegram
    telegram_token = settings.get_telegram_token()
    telegram_chat_ids_raw = settings.get_telegram_chat_ids()

    # Получаем ID чата для отправки уведомлений об ошибках
    telegram_error_chat_id = settings.get_telegram_error_chat_id()
    logger.info(f"DEBUG: ID чата для отправки уведомлений об ошибках: {telegram_error_chat_id}, тип: {type(telegram_error_chat_id)}")

    # Проверяем наличие секрета в директории секретов
    cloud66_secrets_dir = '/mnt/cloud66/secrets'
    if os.path.exists(cloud66_secrets_dir):
        secret_path = os.path.join(cloud66_secrets_dir, 'TELEGRAM_ERROR_CHAT_ID')
        if os.path.isfile(secret_path):
            try:
                with open(secret_path, 'r') as f:
                    secret_error_chat_id = f.read().strip()
                    logger.info(f"DEBUG: Загружен секрет TELEGRAM_ERROR_CHAT_ID из файла: {secret_error_chat_id}")
                    try:
                        telegram_error_chat_id = int(secret_error_chat_id)
                        logger.info(f"DEBUG: Используется значение из секрета: {telegram_error_chat_id}, тип: {type(telegram_error_chat_id)}")
                    except (ValueError, TypeError):
                        logger.error(f"DEBUG: Неверный формат ID чата в секрете: {secret_error_chat_id}")
            except Exception as e:
                logger.error(f"DEBUG: Ошибка при чтении секрета TELEGRAM_ERROR_CHAT_ID: {e}")

    # Преобразуем все ID чатов в целые числа
    telegram_chat_ids = []
    for chat_id in telegram_chat_ids_raw:
        try:
            if isinstance(chat_id, str):
                telegram_chat_ids.append(int(chat_id))
            else:
                telegram_chat_ids.append(chat_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Ошибка при преобразовании ID чата {chat_id} в число: {e}")

    logger.info(f"DEBUG: Список чатов для отправки уведомлений: {telegram_chat_ids}")

    if not telegram_token:
        logger.warning("Токен Telegram не указан. Уведомления не будут отправляться.")
        print("Предупреждение: Токен Telegram не указан. Уведомления не будут отправляться.")

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=telegram_token, logger=logger) if telegram_token else None

    # Создаем сервисы локализации
    localization_service, user_language_service = create_localization_services()
    logger.info("Созданы сервисы локализации для уведомлений")

    # Создаем локализованный сервис уведомлений с поддержкой вебхука
    if notification_sender:
        notification_service = create_localized_notification_service(
            notification_sender=notification_sender,
            localization_service=localization_service,
            user_language_service=user_language_service,
            logger=logger
        )
        logger.info("Создан локализованный сервис уведомлений")
    else:
        notification_service = None

    # Если сервис уведомлений не создан, используем заглушку
    if notification_service is None:
        logger.warning("Нет доступных отправителей уведомлений. Используем заглушку.")
        notification_service = DummyNotificationService(logger=logger)

    # Создаем сервис работы с объектами недвижимости
    property_service = PropertyService(
        parser=parser,
        processed_ad_storage=processed_ad_storage,
        notification_service=notification_service,
        property_validator=property_validator,
        logger=logger
    )

    # Перезагружаем конфигурацию
    scheduler_config.reload_config()

    # Проверяем, включен ли планировщик в целом
    scheduler_enabled = scheduler_config.is_enabled()
    logger.info(f"Планировщик включен: {scheduler_enabled}")
    if not scheduler_enabled:
        logger.info("Планировщик отключен в настройках (enabled: false). Завершение работы.")
        print("Планировщик отключен в настройках (enabled: false). Завершение работы.")
        return 0

    # Выводим информацию о настройках расписания
    schedule_enabled = scheduler_config.is_schedule_enabled()
    logger.info(f"Расписание включено: {schedule_enabled}")
    if schedule_enabled:
        start_time = scheduler_config.get_schedule_start_time()
        end_time = scheduler_config.get_schedule_end_time()
        interval_hours = scheduler_config.get_schedule_interval_hours()
        days_of_week = scheduler_config.get_schedule_days_of_week()
        logger.info(f"Рабочее время: {start_time}-{end_time}, интервал: {interval_hours} ч, дни недели: {days_of_week}")

    # Создаем планировщик парсинга с расписанием
    logger.info(f"DEBUG: Передаем error_chat_id в ParsingScheduler: {telegram_error_chat_id}, тип: {type(telegram_error_chat_id)}")
    parsing_scheduler = ParsingScheduler(
        property_service=property_service,
        categories=valid_categories,
        chat_ids=telegram_chat_ids,
        limit=args.limit,
        logger=logger,
        config=scheduler_config,
        force=args.force,
        error_chat_id=telegram_error_chat_id
    )

    # Проверяем, включено ли расписание
    if schedule_enabled:
        # Получаем текущее время с учетом часового пояса
        # Получаем часовой пояс из конфигурации
        try:
            from src.config.settings import get_timezone
            tz_name = get_timezone()
        except Exception:
            # Если не удалось загрузить функцию, используем значение по умолчанию
            tz_name = os.environ.get('TZ', 'Europe/Kiev')

        try:
            tz = pytz.timezone(tz_name)
            logger.info(f"Используется часовой пояс: {tz_name}")
        except pytz.exceptions.UnknownTimeZoneError:
            logger.warning(f"Неизвестный часовой пояс: {tz_name}, используется UTC")
            tz = pytz.UTC

        # Получаем текущее время в указанном часовом поясе
        now = datetime.now(tz)
        current_time = now.time()
        # Устанавливаем часовой пояс для логгера
        import logging
        logging.Formatter.converter = lambda *args: now.timetuple()
        logger.info(f"Текущее время с учетом часового пояса {tz_name}: {now}")

        # Парсим время начала и окончания работы
        start_time_str = scheduler_config.get_schedule_start_time()
        end_time_str = scheduler_config.get_schedule_end_time()

        # Преобразуем строки во время
        start_hour, start_minute = map(int, start_time_str.split(':'))
        end_hour, end_minute = map(int, end_time_str.split(':'))

        # Создаем объекты времени
        start_time_obj = time(start_hour, start_minute)
        end_time_obj = time(end_hour, end_minute)

        # Проверяем, что текущее время в рамках рабочего времени
        print(f"DEBUG: Текущее время: {current_time}, Рабочее время: {start_time_obj} - {end_time_obj}")
        # Если указан флаг --force, запускаем в любом случае
        if args.force:
            logger.info("Принудительный запуск вне рабочего времени (флаг --force)")
            print("Принудительный запуск вне рабочего времени (флаг --force)")
        elif current_time < start_time_obj or current_time > end_time_obj:
            logger.info(f"Запуск планировщика пропущен: вне рабочего времени ({start_time_str}-{end_time_str})")
            print(f"Запуск планировщика пропущен: вне рабочего времени ({start_time_str}-{end_time_str})")
            print("Планировщик будет запущен автоматически в рабочее время. Нажмите Ctrl+C для завершения.")

            # Вычисляем время следующего запуска
            if current_time > end_time_obj:
                # Если текущее время позже времени окончания работы, запускаем завтра в начале рабочего времени
                next_run = datetime.combine(now.date() + timedelta(days=1), start_time_obj)
                # Добавляем информацию о часовом поясе
                next_run = next_run.replace(tzinfo=tz)
            else:
                # Если текущее время раньше времени начала работы, запускаем сегодня в начале рабочего времени
                next_run = datetime.combine(now.date(), start_time_obj)
                # Добавляем информацию о часовом поясе
                next_run = next_run.replace(tzinfo=tz)

            # Вычисляем время ожидания в секундах
            wait_seconds = (next_run - now).total_seconds()

            # Выводим информацию о времени следующего запуска
            logger.info(f"Следующий запуск планировщика: {next_run.strftime('%Y-%m-%d %H:%M:%S')} (через {wait_seconds:.0f} секунд)")
            print(f"Следующий запуск планировщика: {next_run.strftime('%Y-%m-%d %H:%M:%S')} (через {wait_seconds:.0f} секунд)")

            # Ждем до следующего запуска в текущем терминале
            print(f"Ожидание до следующего запуска в {next_run.strftime('%Y-%m-%d %H:%M:%S')}...")
            print(f"Терминал останется открытым. Нажмите Ctrl+C для отмены.")
            logger.info(f"Ожидание до следующего запуска в {next_run.strftime('%Y-%m-%d %H:%M:%S')}...")

            try:
                # Ждем до следующего запуска
                time_module.sleep(wait_seconds)

                # После ожидания запускаем парсер в текущем терминале
                print(f"Время ожидания истекло. Запуск парсера...")
                logger.info(f"Время ожидания истекло. Запуск парсера...")

                # Вместо рекурсивного вызова main() продолжаем выполнение
                # Это позволит избежать переполнения стека
                # Просто продолжаем выполнение, как будто мы в рабочее время
                pass
            except KeyboardInterrupt:
                print("\nОжидание прервано пользователем.")
                logger.info("Ожидание прервано пользователем.")
                return 1

        # Проверяем день недели
        day_of_week = now.isoweekday()  # 1 - понедельник, 7 - воскресенье
        allowed_days = scheduler_config.get_schedule_days_of_week()
        print(f"DEBUG: Текущий день недели: {day_of_week}, Разрешенные дни: {allowed_days}")

        if day_of_week not in allowed_days:
            logger.info(f"Запуск планировщика пропущен: нерабочий день (день недели {day_of_week})")
            print(f"Запуск планировщика пропущен: нерабочий день (день недели {day_of_week})")
            print("Планировщик будет запущен автоматически в следующий рабочий день. Нажмите Ctrl+C для завершения.")

            # Вычисляем дату следующего рабочего дня
            next_day = now.date() + timedelta(days=1)
            next_day_of_week = (day_of_week % 7) + 1  # Следующий день недели

            # Ищем следующий разрешенный день недели
            days_to_add = 1
            while next_day_of_week not in allowed_days:
                days_to_add += 1
                next_day = now.date() + timedelta(days=days_to_add)
                next_day_of_week = next_day.isoweekday()

            # Создаем время следующего запуска (начало рабочего дня следующего рабочего дня)
            next_run = datetime.combine(next_day, start_time_obj)
            # Добавляем информацию о часовом поясе
            next_run = next_run.replace(tzinfo=tz)

            # Вычисляем время ожидания в секундах
            wait_seconds = (next_run - now).total_seconds()

            # Выводим информацию о времени следующего запуска
            logger.info(f"Следующий запуск планировщика: {next_run.strftime('%Y-%m-%d %H:%M:%S')} (через {wait_seconds:.0f} секунд)")
            print(f"Следующий запуск планировщика: {next_run.strftime('%Y-%m-%d %H:%M:%S')} (через {wait_seconds:.0f} секунд)")

            # Ждем до следующего запуска в текущем терминале
            print(f"Ожидание до следующего запуска в {next_run.strftime('%Y-%m-%d %H:%M:%S')}...")
            print(f"Терминал останется открытым. Нажмите Ctrl+C для отмены.")
            logger.info(f"Ожидание до следующего запуска в {next_run.strftime('%Y-%m-%d %H:%M:%S')}...")

            try:
                # Ждем до следующего запуска
                time_module.sleep(wait_seconds)

                # После ожидания запускаем парсер в текущем терминале
                print(f"Время ожидания истекло. Запуск парсера...")
                logger.info(f"Время ожидания истекло. Запуск парсера...")

                # Вместо рекурсивного вызова main() продолжаем выполнение
                # Это позволит избежать переполнения стека
                # Просто продолжаем выполнение, как будто мы в рабочий день
                pass
            except KeyboardInterrupt:
                print("\nОжидание прервано пользователем.")
                logger.info("Ожидание прервано пользователем.")
                return 1

    # Вычисляем время первого запуска
    # Получаем часовой пояс из конфигурации
    try:
        from src.config.settings import get_timezone
        tz_name = get_timezone()
    except Exception:
        # Если не удалось загрузить функцию, используем значение по умолчанию
        tz_name = os.environ.get('TZ', 'Europe/Kiev')

    try:
        tz = pytz.timezone(tz_name)
    except pytz.exceptions.UnknownTimeZoneError:
        tz = pytz.UTC

    start_time = datetime.now(tz) + timedelta(minutes=args.delay) if args.delay > 0 else None

    # Определяем интервал
    if args.interval is not None:
        interval = args.interval
    else:
        # Используем интервал из файла конфигурации
        interval = scheduler_config.scheduler_config.get('interval', 3600)

    # Добавляем задачи для всех категорий
    tasks = parsing_scheduler.add_all_categories(interval=interval, start_time=start_time)

    # Выводим информацию о задачах
    print(f"Добавлено задач: {len(tasks)}")
    for task in tasks:
        next_run = task.next_run.strftime("%Y-%m-%d %H:%M:%S")
        print(f"Задача: {task.name}, Следующий запуск: {next_run}, Интервал: {task.interval} сек.")

    # Если не добавлено ни одной задачи парсинга категорий, добавляем задачу проверки подписчиков
    if len(tasks) == 0 and scheduler_config.is_parse_only_subscribed_categories():
        print("Не добавлено ни одной задачи парсинга категорий. Добавляем задачу проверки подписчиков.")
        logger.info("Не добавлено ни одной задачи парсинга категорий. Добавляем задачу проверки подписчиков.")

        # Получаем интервал из конфигурации
        check_interval = scheduler_config.get_schedule_interval_hours() * 3600  # Переводим часы в секунды

        # Добавляем задачу проверки подписчиков
        check_task = parsing_scheduler.scheduler.add_task(
            name="check_subscribers",
            func=parsing_scheduler._check_subscribers,
            interval=check_interval,
            start_time=datetime.now(tz) + timedelta(minutes=1),  # Запускаем через 1 минуту
            args=[],
            kwargs={}
        )

        print(f"Добавлена задача проверки подписчиков с интервалом {check_interval} сек.")
        logger.info(f"Добавлена задача проверки подписчиков с интервалом {check_interval} сек.")

    # Обработка сигналов для корректного завершения
    def signal_handler(*_):
        print("\nОстановка планировщика...")
        parsing_scheduler.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Запускаем планировщик
    print("Запуск планировщика. Нажмите Ctrl+C для остановки.")
    parsing_scheduler.start()

    # Ожидаем завершения (бесконечный цикл)
    try:
        while True:
            signal.pause()
    except (KeyboardInterrupt, SystemExit):
        parsing_scheduler.stop()

    return 0


if __name__ == '__main__':
    sys.exit(main())
