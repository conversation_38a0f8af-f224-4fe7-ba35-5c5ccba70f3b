"""
Команда для запуска планировщика парсинга.
"""
import json
import logging
import os
import signal
import sys
from datetime import datetime, timedelta
from typing import Optional

import click

from src.application.services.dummy_notification import DummyNotificationService
from src.application.services.notification import NotificationService
from src.application.services.property import PropertyService
from src.config import settings
from src.config.paths import get_data_path
from src.domain.services.property_validator import PropertyValidator
from src.infrastructure.external.telegram.notification_sender import TelegramNotificationSender
from src.infrastructure.logging.logger import setup_logger
from src.infrastructure.parsers.olx.olx_parser import OlxParser
from src.infrastructure.persistence.processed_ad_storage import DbProcessedAdStorage
from src.presentation.scheduler.core import ParsingScheduler
from src.infrastructure.storage.json_storage import JsonStorage


@click.command(name="schedule")
@click.option("--categories", "-c", help="Категории для парсинга (через запятую)", required=True)
@click.option("--interval", "-i", type=int, default=3600, help="Интервал парсинга в секундах (по умолчанию 1 час)")
@click.option("--limit", "-l", type=int, help="Максимальное количество объявлений для обработки")
@click.option("--delay", "-d", type=int, default=0, help="Задержка перед первым запуском в минутах (по умолчанию 0)")
@click.option("--config", type=click.Path(exists=True), help="Путь к файлу конфигурации")
@click.option("--verbose", "-v", is_flag=True, help="Подробный вывод")
def schedule_command(categories: str, interval: int = 3600, limit: Optional[int] = None,
                    delay: int = 0, config: Optional[str] = None, verbose: bool = False):
    """
    Запускает планировщик парсинга для указанных категорий.

    Args:
        categories: Категории для парсинга (через запятую)
        interval: Интервал парсинга в секундах (по умолчанию 1 час)
        limit: Максимальное количество объявлений для обработки (опционально)
        delay: Задержка перед первым запуском в минутах (по умолчанию 0)
        config: Путь к файлу конфигурации (опционально)
        verbose: Подробный вывод (опционально)
    """
    # Настраиваем логирование
    log_level = logging.DEBUG if verbose else logging.INFO
    logger = setup_logger("olx_parser", log_level=log_level)

    # Разбиваем категории
    category_list = [category.strip() for category in categories.split(',')]

    logger.info(f"Запуск планировщика для категорий: {', '.join(category_list)}")

    # Загружаем конфигурацию
    if config:
        # TODO: Реализовать загрузку конфигурации из указанного файла
        logger.info(f"Загрузка конфигурации из файла: {config}")

    # Используем централизованные настройки
    # Получаем настройки хранилища
    storage_file_path = settings.get_storage_file_path()

    # Если путь не абсолютный, преобразуем его относительно директории данных
    if storage_file_path and not os.path.isabs(storage_file_path):
        storage_file_path = get_data_path(storage_file_path)

    # Создаем хранилище
    json_storage = JsonStorage(file_path=storage_file_path, logger=logger)
    processed_ad_storage = DbProcessedAdStorage(json_storage=json_storage, logger=logger)

    # Создаем валидатор
    property_validator = PropertyValidator()

    # Загружаем категории
    categories_file = get_data_path("categories.json")
    if not os.path.exists(categories_file):
        categories_file = os.path.join(os.path.dirname(__file__), "../../../config/data/categories.json")

    try:
        with open(categories_file, 'r', encoding='utf-8') as f:
            categories_data = json.load(f)
    except Exception as e:
        logger.error(f"Ошибка при загрузке категорий: {e}")
        categories_data = {"categories": []}

    # Проверяем, существуют ли указанные категории
    valid_categories = []
    for category in category_list:
        category_exists = False
        for cat in categories_data.get("categories", []):
            if cat.get("id") == category:
                category_exists = True
                break

        if category_exists:
            valid_categories.append(category)
        else:
            logger.warning(f"Категория {category} не найдена")
            click.echo(f"Предупреждение: Категория {category} не найдена")

    if not valid_categories:
        logger.error("Не найдено ни одной валидной категории")
        click.echo("Ошибка: Не найдено ни одной валидной категории")
        return

    # Загружаем настройки OLX
    olx_config = settings.olx

    # Создаем парсер
    parser = OlxParser(config=olx_config, logger=logger)

    # Загружаем настройки Telegram
    telegram_token = settings.get_telegram_token()
    telegram_chat_ids = settings.get_telegram_chat_ids()

    if not telegram_token:
        logger.warning("Токен Telegram не указан. Уведомления не будут отправляться.")
        click.echo("Предупреждение: Токен Telegram не указан. Уведомления не будут отправляться.")

    # Создаем отправитель уведомлений
    notification_sender = TelegramNotificationSender(token=telegram_token, logger=logger) if telegram_token else None

    # Создаем сервис уведомлений
    notification_service = NotificationService(notification_sender=notification_sender) if notification_sender else None

    # Если сервис уведомлений не создан, используем заглушку
    if notification_service is None:
        notification_service = DummyNotificationService(logger=logger)

    # Создаем сервис работы с объектами недвижимости
    property_service = PropertyService(
        parser=parser,
        processed_ad_storage=processed_ad_storage,
        notification_service=notification_service,
        property_validator=property_validator,
        logger=logger
    )

    # Создаем планировщик парсинга
    parsing_scheduler = ParsingScheduler(
        property_service=property_service,
        categories=valid_categories,
        chat_ids=telegram_chat_ids,
        limit=limit,
        logger=logger
    )

    # Вычисляем время первого запуска
    start_time = datetime.now() + timedelta(minutes=delay) if delay > 0 else None

    # Добавляем задачи для всех категорий
    tasks = parsing_scheduler.add_all_categories(interval=interval, start_time=start_time)

    # Выводим информацию о задачах
    click.echo(f"Добавлено задач: {len(tasks)}")
    for task in tasks:
        next_run = task.next_run.strftime("%Y-%m-%d %H:%M:%S")
        click.echo(f"Задача: {task.name}, Следующий запуск: {next_run}, Интервал: {interval} сек.")

    # Обработка сигналов для корректного завершения
    def signal_handler(_sig, _frame):
        click.echo("\nОстановка планировщика...")
        parsing_scheduler.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Запускаем планировщик
    click.echo("Запуск планировщика. Нажмите Ctrl+C для остановки.")
    parsing_scheduler.start()

    # Ожидаем завершения (бесконечный цикл)
    try:
        while True:
            signal.pause()
    except (KeyboardInterrupt, SystemExit):
        parsing_scheduler.stop()
