"""
Точка входа для интерфейса командной строки.
"""
import asyncio
import click
import functools

from src.presentation.cli.commands.schedule import schedule_command


# Декоратор для запуска асинхронных команд
def coro(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper


# Импортируем функцию из модуля parse.py
from src.presentation.cli.commands.parse import parse_command_impl


# Обертка для асинхронной команды parse
@click.command(name="parse")
@click.option("--category", "-c", help="Категория для парсинга (по умолчанию: apartments_sale)")
@click.option("--limit", "-l", type=int, help="Максимальное количество объявлений для обработки")
@click.option("--config", type=click.Path(exists=True), help="Путь к файлу конфигурации")
@click.option("--verbose", "-v", is_flag=True, help="Подробный вывод")
@coro
def parse_command_wrapper(category=None, limit=None, config=None, verbose=False):
    """
    Запускает парсинг объявлений для указанной категории.
    """
    return parse_command_impl(category=category, limit=limit, config=config, verbose=verbose)


@click.group()
def cli():
    """
    Парсер объявлений OLX.
    """
    pass


# Регистрируем команды
cli.add_command(parse_command_wrapper, name="parse")
cli.add_command(schedule_command)


if __name__ == "__main__":
    cli()
