"""
Entity для объекта недвижимости.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional

from src.domain.value_objects.ad_id import AdId
from src.domain.value_objects.address import Address
from src.domain.value_objects.area import Area
from src.domain.value_objects.contact import Contact
from src.domain.value_objects.price import Price


@dataclass
class Property:
    """
    Доменная сущность, представляющая объект недвижимости.
    Используется для обработки данных в памяти после парсинга.
    """
    ad_id: AdId
    title: str
    description: Optional[str]
    price: Price
    address: Address
    url: str

    # Опциональные поля
    area: Optional[Area] = None
    rooms: Optional[int] = None
    floor: Optional[int] = None
    total_floors: Optional[int] = None
    property_type: Optional[str] = None  # квартира, дом, участок и т.д.
    building_type: Optional[str] = None  # тип дома: панельный, кирпичный, монолитный и т.д.
    numeric_id: Optional[str] = None  # Числовой ID объявления из элемента span.css-w85dhy
    contact: Optional[Contact] = None
    images: Optional[List[str]] = None
    published_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    additional_info: Optional[Dict[str, str]] = None  # Дополнительные характеристики
    warnings: List[str] = None  # type: ignore # Предупреждения, которые не являются критическими ошибками

    def __post_init__(self):
        """Валидация после инициализации."""
        if not self.ad_id:
            raise ValueError("ID объявления обязателен")

        if not self.title:
            raise ValueError("Заголовок объявления обязателен")

        if not self.price:
            raise ValueError("Цена объявления обязательна")

        if not self.address:
            raise ValueError("Адрес объекта обязателен")

        if not self.url:
            raise ValueError("URL объявления обязателен")

        # Инициализируем список предупреждений, если он не был инициализирован
        if self.warnings is None:
            object.__setattr__(self, 'warnings', [])

        # Проверка согласованности этажей
        if self.floor is not None and self.total_floors is not None:
            if self.floor > self.total_floors:
                raise ValueError("Этаж не может быть больше общего количества этажей")
            if self.floor <= 0:
                raise ValueError("Этаж должен быть положительным числом")
            if self.total_floors <= 0:
                raise ValueError("Общее количество этажей должно быть положительным числом")

        # Проверка количества комнат
        if self.rooms is not None and self.rooms <= 0:
            raise ValueError("Количество комнат должно быть положительным числом")

    def add_warning(self, warning: str) -> None:
        """Добавляет предупреждение в список предупреждений.

        Args:
            warning: Текст предупреждения
        """
        if self.warnings is None:
            object.__setattr__(self, 'warnings', [])
        self.warnings.append(warning)  # type: ignore

    @property
    def is_new_building(self) -> bool:
        """Определяет, является ли объект новостройкой."""
        if not self.additional_info:
            return False

        # Проверка по дополнительным характеристикам
        keywords = ["новостройка", "новый дом", "от застройщика"]
        for keyword in keywords:
            if any(keyword.lower() in str(value).lower() for value in self.additional_info.values()):
                return True

        return False

    @property
    def price_per_meter(self) -> Optional[float]:
        """Рассчитывает цену за квадратный метр."""
        if not self.area or not self.area.total or self.area.total <= 0:
            return None

        return self.price.amount / self.area.total

    def __str__(self) -> str:
        """Строковое представление."""
        return f"{self.title} - {self.price} ({self.address})"

    @classmethod
    def from_dict(cls, data: dict) -> Optional['Property']:
        """Создание объекта из словаря."""
        import logging
        logger = logging.getLogger('property_entity')

        if not data:
            logger.warning("Получены пустые данные для создания Property")
            return None

        # Логируем входные данные
        import json
        logger.info(f"Создание Property из данных: {json.dumps(data, ensure_ascii=False, default=str)}")

        # Обязательные поля
        try:
            # Логируем наличие обязательных полей
            logger.info(f"Проверка обязательных полей: id={data.get('id')}, title={data.get('title')}, url={data.get('url')}")
            logger.info(f"Проверка price: {data.get('price')}")
            logger.info(f"Проверка address: {data.get('address')}")

            ad_id = AdId.from_dict(data) if 'ad_id' in data else AdId(data['id'])
            price = Price.from_dict(data['price']) if 'price' in data else None
            address = Address.from_dict(data['address']) if 'address' in data else None

            # Проверяем наличие всех обязательных полей
            missing_fields = []
            if not ad_id: missing_fields.append('ad_id')
            if not price: missing_fields.append('price')
            if not address: missing_fields.append('address')
            if not data.get('title'): missing_fields.append('title')
            if not data.get('url'): missing_fields.append('url')

            if missing_fields:
                logger.warning(f"Отсутствуют обязательные поля: {', '.join(missing_fields)}")
                return None

            # Опциональные поля
            logger.info(f"Проверка опциональных полей: area={data.get('area')}, contact={data.get('contact')}")
            logger.info(f"Проверка images: {data.get('images')}")
            logger.info(f"Проверка rooms={data.get('rooms')}, floor={data.get('floor')}, total_floors={data.get('total_floors')}")

            # Создаем объект Area из площадей в корне объекта
            area = None
            if 'area' in data:
                # Если есть поле area, используем его
                area = Area.from_dict(data['area'])
            elif 'total_area' in data:
                # Иначе создаем объект Area из площадей в корне объекта
                try:
                    area_data = {
                        'total': data['total_area'],
                        'living': data.get('living_area'),
                        'kitchen': data.get('kitchen_area'),
                        'land': data.get('land_area')
                    }
                    area = Area.from_dict(area_data)
                    logger.info(f"Создан объект Area из площадей в корне: {area}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Ошибка при создании объекта Area: {e}")
            contact = Contact.from_dict(data['contact']) if 'contact' in data else None

            # Преобразование дат
            published_at = None
            if 'published_at' in data:
                if isinstance(data['published_at'], str):
                    published_at = datetime.fromisoformat(data['published_at'])
                elif isinstance(data['published_at'], datetime):
                    published_at = data['published_at']

            updated_at = None
            if 'updated_at' in data:
                if isinstance(data['updated_at'], str):
                    updated_at = datetime.fromisoformat(data['updated_at'])
                elif isinstance(data['updated_at'], datetime):
                    updated_at = data['updated_at']

            # Проверяем и ограничиваем количество изображений
            images = data.get('images', [])
            if images and len(images) > 0:
                logger.info(f"Получено {len(images)} изображений")
                # Ограничиваем количество изображений до 1
                images = images[:1]
                logger.info(f"Ограничено до {len(images)} изображений")

            # На этом этапе мы уже проверили, что все обязательные поля не None
            # Поэтому мы можем безопасно использовать утверждение типа (type assertion)

            # Получаем тип дома из additional_info, если он там есть
            building_type = None
            if 'additional_info' in data and 'building_type' in data['additional_info']:
                building_type = data['additional_info']['building_type']
                logger.info(f"Получен тип дома из additional_info: {building_type}")

            # Получаем числовой ID объявления
            numeric_id = data.get('numeric_id')
            if numeric_id:
                logger.info(f"Получен числовой ID объявления: {numeric_id}")

            property_obj = cls(
                ad_id=ad_id,  # type: ignore
                title=data['title'],
                description=data.get('description'),
                price=price,  # type: ignore
                address=address,  # type: ignore
                url=data['url'],
                area=area,
                rooms=data.get('rooms'),
                floor=data.get('floor'),
                total_floors=data.get('total_floors'),
                property_type=data.get('property_type'),
                building_type=building_type,  # Добавляем тип дома
                numeric_id=numeric_id,  # Добавляем числовой ID
                contact=contact,
                images=images,  # Используем ограниченный список изображений
                published_at=published_at,
                updated_at=updated_at,
                additional_info=data.get('additional_info', {}),
                warnings=data.get('warnings', [])
            )

            # Логируем созданный объект
            logger.info(f"Создан объект Property: id={property_obj.ad_id}, title={property_obj.title}, images={len(property_obj.images or [])}")
            return property_obj
        except (ValueError, KeyError) as e:
            # Логирование ошибки
            logger.error(f"Ошибка при создании объекта Property: {e}", exc_info=True)
            return None
