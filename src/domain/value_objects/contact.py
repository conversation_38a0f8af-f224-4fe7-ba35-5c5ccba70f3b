"""
Value Object для контактной информации.
"""
import re
from dataclasses import dataclass
from typing import List, Optional


@dataclass(frozen=True)
class Contact:
    """
    Value Object, представляющий контактную информацию продавца.
    Неизменяемый (immutable) объект.
    """
    name: Optional[str] = None
    phones: List[str] = None  # type: ignore
    email: Optional[str] = None

    def __init__(self, name: Optional[str] = None, phones: Optional[List[str]] = None, email: Optional[str] = None):
        object.__setattr__(self, 'name', name)
        object.__setattr__(self, 'phones', phones or [])

    def __post_init__(self):
        """Валидация после инициализации."""
        # Проверка, что хотя бы одно из полей заполнено
        if not any([self.name, self.phones, self.email]):
            raise ValueError("Должно быть указано хотя бы одно контактное поле")

        # Валидация телефонов
        if self.phones:
            # Преобразуем в список, если передана одна строка
            if isinstance(self.phones, str):
                object.__setattr__(self, 'phones', [self.phones])

            # Проверка формата телефонов
            for phone in self.phones:
                if not self._is_valid_phone(phone):
                    raise ValueError(f"Некорректный формат телефона: {phone}")

        # Валидация email
        if self.email and not self._is_valid_email(self.email):
            raise ValueError(f"Некорректный формат email: {self.email}")

    def _is_valid_phone(self, phone: str) -> bool:
        """Проверка валидности телефона."""
        # Проверка на заглушку с номером телефона
        if phone.strip() in ["Телефон скрыт", "Номер скрыт", "Скрыт", "Телефон прихований", "Номер прихований", "Прихований", "Телефон: ---"]:
            return True

        # Упрощенная проверка - телефон должен содержать только цифры, +, -, (, ), пробелы
        # и быть не короче 10 символов (с учетом форматирования)
        phone = phone.strip()
        if not phone:
            return False

        # Удаляем все символы форматирования
        digits_only = re.sub(r'[^\d]', '', phone)
        return len(digits_only) >= 10

    def _is_valid_email(self, email: str) -> bool:
        """Проверка валидности email."""
        # Упрощенная проверка формата email
        email_pattern = re.compile(r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$')
        return bool(email_pattern.match(email))

    def __str__(self) -> str:
        """Строковое представление."""
        parts = []
        if self.name:
            parts.append(f"Имя: {self.name}")
        if self.phones:
            parts.append(f"Телефон(ы): {', '.join(self.phones)}")

        return "; ".join(parts)

    @classmethod
    def from_dict(cls, data: dict) -> Optional['Contact']:
        """Создание объекта из словаря."""
        if not data:
            return None

        return cls(
            name=data.get('name'),
            phones=data.get('phones', []),
        )
