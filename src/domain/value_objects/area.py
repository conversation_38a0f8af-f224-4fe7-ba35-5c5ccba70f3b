"""
Value Object для площади объекта недвижимости.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class Area:
    """
    Value Object, представляющий площадь объекта недвижимости.
    Неизменяемый (immutable) объект.
    """
    total: float  # Общая площадь в квадратных метрах
    living: Optional[float] = None  # Жилая площадь
    kitchen: Optional[float] = None  # Площадь кухни
    land: Optional[float] = None  # Площадь участка (для домов)

    def __post_init__(self):
        """Валидация после инициализации."""
        if self.total <= 0:
            raise ValueError("Общая площадь должна быть положительной")

        if self.living is not None and self.living <= 0:
            raise ValueError("Жилая площадь должна быть положительной")

        if self.kitchen is not None and self.kitchen <= 0:
            raise ValueError("Площадь кухни должна быть положительной")

        if self.land is not None and self.land <= 0:
            raise ValueError("Площадь участка должна быть положительной")

        # Проверка, что жилая площадь не больше общей
        if self.living is not None and self.living > self.total:
            raise ValueError("Жилая площадь не может быть больше общей")

        # Проверка, что площадь кухни не больше общей
        if self.kitchen is not None and self.kitchen > self.total:
            raise ValueError("Площадь кухни не может быть больше общей")

    def __str__(self) -> str:
        """Строковое представление."""
        result = f"{self.total} м²"
        if self.living:
            result += f" (жилая: {self.living} м²)"
        if self.kitchen:
            result += f" (кухня: {self.kitchen} м²)"
        if self.land:
            result += f" (участок: {self.land} м²)"
        return result

    @classmethod
    def from_dict(cls, data: dict) -> Optional['Area']:
        """Создание объекта из словаря."""
        if not data or 'total' not in data:
            return None

        # Преобразование строк с единицами измерения в числа
        def extract_number(value):
            if value is None:
                return None
            if isinstance(value, (int, float)):
                return float(value)
            if isinstance(value, str):
                import re
                match = re.search(r'(\d+[.,]?\d*)', value)
                if match:
                    return float(match.group(1).replace(',', '.'))
            return None

        total = extract_number(data['total'])
        if total is None:
            return None

        return cls(
            total=total,
            living=extract_number(data['living']) if 'living' in data else None,
            kitchen=extract_number(data['kitchen']) if 'kitchen' in data else None,
            land=extract_number(data['land']) if 'land' in data else None
        )
