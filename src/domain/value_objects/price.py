"""
Value Object для цены объявления.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class Price:
    """
    Value Object, представляющий цену объекта недвижимости.
    Неизменяемый (immutable) объект.
    """
    amount: float
    currency: str

    def __post_init__(self):
        """Валидация после инициализации."""
        if self.amount < 0:
            raise ValueError("Цена не может быть отрицательной")
        
        if not self.currency:
            raise ValueError("Валюта не может быть пустой")
        
        # Можно добавить проверку допустимых валют
        valid_currencies = {"UAH", "USD", "EUR"}
        if self.currency not in valid_currencies:
            raise ValueError(f"Недопустимая валюта: {self.currency}. Допустимые валюты: {', '.join(valid_currencies)}")

    def __str__(self) -> str:
        """Строковое представление."""
        return f"{self.amount} {self.currency}"
    
    @property
    def is_free(self) -> bool:
        """Проверка, является ли объект бесплатным."""
        return self.amount == 0
    
    @classmethod
    def from_dict(cls, data: dict) -> Optional['Price']:
        """Создание объекта из словаря."""
        if not data or 'amount' not in data or 'currency' not in data:
            return None
        return cls(
            amount=float(data['amount']),
            currency=data['currency']
        )
