"""
Value Object для идентификатора объявления.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class AdId:
    """
    Value Object, представляющий уникальный идентификатор объявления OLX.
    Неизменяемый (immutable) объект.
    """
    value: str

    def __post_init__(self):
        """Валидация после инициализации."""
        if not self.value:
            raise ValueError("ID объявления не может быть пустым")

        if not isinstance(self.value, str):
            raise TypeError("ID объявления должен быть строкой")

        # Можно добавить дополнительные проверки формата ID, если известен формат OLX

    def __str__(self) -> str:
        """Строковое представление."""
        return self.value

    def __eq__(self, other) -> bool:
        """Сравнение с другим объектом."""
        if not isinstance(other, AdId):
            return False
        return self.value == other.value

    def __hash__(self) -> int:
        """Хеш-код для использования в словарях и множествах."""
        return hash(self.value)

    @classmethod
    def from_string(cls, value: str) -> 'AdId':
        """Создание объекта из строки."""
        return cls(value=value)

    @classmethod
    def from_dict(cls, data: dict) -> Optional['AdId']:
        """Создание объекта из словаря."""
        # Если есть поле ad_id, используем его
        if data and 'ad_id' in data:
            return cls(value=data['ad_id'])

        # Если есть поле id, используем его
        if data and 'id' in data:
            # Проверяем, является ли id числовым (из элемента span)
            if isinstance(data['id'], str) and data['id'].isdigit():
                return cls(value=data['id'])
            # Если id не числовой (из URL), используем его как есть
            return cls(value=data['id'])

        return None
