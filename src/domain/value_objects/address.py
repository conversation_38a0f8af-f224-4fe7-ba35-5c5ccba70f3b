"""
Value Object для адреса объекта недвижимости.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class Address:
    """
    Value Object, представляющий адрес объекта недвижимости.
    Неизменяемый (immutable) объект.
    """
    city: str
    district: Optional[str] = None
    street: Optional[str] = None
    house_number: Optional[str] = None
    full_address: Optional[str] = None

    def __post_init__(self):
        """Валидация после инициализации."""
        if not self.city:
            raise ValueError("Город не может быть пустым")
        
        # Если указан полный адрес, но не указаны другие поля, это допустимо
        if not self.full_address and not any([self.district, self.street, self.house_number]):
            # Можно добавить предупреждение в лог, что адрес неполный
            pass

    def __str__(self) -> str:
        """Строковое представление."""
        if self.full_address:
            return self.full_address
        
        parts = [self.city]
        if self.district:
            parts.append(self.district)
        if self.street:
            parts.append(self.street)
        if self.house_number:
            parts.append(self.house_number)
        
        return ", ".join(parts)
    
    @classmethod
    def from_dict(cls, data: dict) -> Optional['Address']:
        """Создание объекта из словаря."""
        if not data or 'city' not in data:
            return None
        
        return cls(
            city=data['city'],
            district=data.get('district'),
            street=data.get('street'),
            house_number=data.get('house_number'),
            full_address=data.get('full_address')
        )
