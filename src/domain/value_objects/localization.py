"""
Модели для локализации.
"""
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass(frozen=True)
class Language:
    """
    Модель языка.
    """
    code: str
    name: str
    flag: Optional[str] = None
    
    def __str__(self) -> str:
        """
        Строковое представление языка.
        
        Returns:
            str: Строковое представление
        """
        if self.flag:
            return f"{self.flag} {self.name}"
        return self.name


@dataclass(frozen=True)
class LocalizedString:
    """
    Модель локализованной строки.
    """
    key: str
    values: Dict[str, str]
    
    def get_value(self, language: str, default: Optional[str] = None) -> str:
        """
        Получает значение для указанного языка.
        
        Args:
            language: Код языка
            default: Значение по умолчанию, если язык не найден
            
        Returns:
            str: Локализованная строка
        """
        return self.values.get(language, default or self.key)
