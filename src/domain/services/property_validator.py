"""
Domain Service для валидации объекта недвижимости.
"""
from typing import List, Optional, Tuple

from src.domain.entities.property import Property


class PropertyValidator:
    """
    Доменный сервис для валидации объекта недвижимости.
    Проверяет соответствие объекта бизнес-правилам.
    """

    def validate(self, property_obj: Property) -> Tuple[bool, List[str]]:
        """
        Валидирует объект недвижимости на соответствие бизнес-правилам.

        Args:
            property_obj: Объект недвижимости для валидации

        Returns:
            Tuple[bool, List[str]]: (результат валидации, список ошибок)
        """
        errors = []

        # Проверка обязательных полей (уже выполняется в __post_init__ класса Property)
        # Здесь мы проверяем дополнительные бизнес-правила

        # Проверка цены (например, слишком низкая или высокая)
        price_errors = self._validate_price(property_obj)
        if price_errors:
            errors.extend(price_errors)

        # Проверка площади
        area_errors = self._validate_area(property_obj)
        if area_errors:
            errors.extend(area_errors)

        # Проверка этажности
        floor_errors = self._validate_floors(property_obj)
        if floor_errors:
            errors.extend(floor_errors)

        # Проверка контактной информации
        contact_errors = self._validate_contact(property_obj)
        if contact_errors:
            errors.extend(contact_errors)

        # Проверка URL
        url_errors = self._validate_url(property_obj)
        if url_errors:
            errors.extend(url_errors)

        return len(errors) == 0, errors

    def _validate_price(self, property_obj: Property) -> List[str]:
        """Валидация цены."""
        errors = []

        # Пример бизнес-правила: цена не может быть слишком низкой для данного типа недвижимости
        if property_obj.property_type == "квартира" and property_obj.price.amount < 5000 and property_obj.price.currency == "USD":
            errors.append("Подозрительно низкая цена для квартиры")

        # Пример бизнес-правила: цена за метр не может быть слишком низкой
        if property_obj.price_per_meter is not None:
            if property_obj.property_type == "квартира" and property_obj.price_per_meter < 200 and property_obj.price.currency == "USD":
                errors.append(f"Подозрительно низкая цена за метр: {property_obj.price_per_meter} {property_obj.price.currency}/м²")

        return errors

    def _validate_area(self, property_obj: Property) -> List[str]:
        """Валидация площади."""
        errors = []

        if not property_obj.area:
            return errors  # Площадь не указана, пропускаем проверку

        # Пример бизнес-правила: площадь квартиры не может быть слишком маленькой
        if property_obj.property_type == "квартира" and property_obj.area.total < 15:
            errors.append(f"Подозрительно маленькая площадь для квартиры: {property_obj.area.total} м²")

        # Пример бизнес-правила: жилая площадь не может быть больше общей
        if property_obj.area.living and property_obj.area.living > property_obj.area.total:
            errors.append(f"Жилая площадь ({property_obj.area.living} м²) больше общей ({property_obj.area.total} м²)")

        return errors

    def _validate_floors(self, property_obj: Property) -> List[str]:
        """Валидация этажности."""
        errors = []

        # Пропускаем проверку, если этаж не указан
        if property_obj.floor is None:
            return errors

        # Пример бизнес-правила: этаж не может быть больше общего количества этажей
        if property_obj.total_floors is not None and property_obj.floor > property_obj.total_floors:
            errors.append(f"Этаж ({property_obj.floor}) больше общего количества этажей ({property_obj.total_floors})")

        # Пример бизнес-правила: этаж не может быть отрицательным или нулевым
        if property_obj.floor <= 0:
            errors.append(f"Некорректный этаж: {property_obj.floor}")

        return errors

    def _validate_contact(self, property_obj: Property) -> List[str]:
        """Валидация контактной информации."""
        errors = []

        # Пропускаем проверку, если контакт не указан
        if not property_obj.contact:
            return errors

        # Пример бизнес-правила: должен быть указан хотя бы один телефон
        if not property_obj.contact.phones or len(property_obj.contact.phones) == 0:
            errors.append("Не указан телефон для связи")
        else:
            # Проверяем, что телефон не является только заглушкой "Телефон скрыт" или "Телефон: ---"
            # Если телефон скрыт, то это нормально для OLX
            has_only_hidden_phones = all(phone.strip() in ["Телефон скрыт", "Номер скрыт", "Скрыт", "Телефон прихований", "Номер прихований", "Прихований", "Телефон: ---"] for phone in property_obj.contact.phones)
            if has_only_hidden_phones:
                # Если все телефоны скрыты, добавляем предупреждение, но не ошибку
                # Это позволит продолжить обработку объявления
                property_obj.add_warning("Телефон скрыт на OLX, но объявление будет обработано")

        return errors

    def _validate_url(self, property_obj: Property) -> List[str]:
        """Валидация URL."""
        errors = []

        # Пример бизнес-правила: URL должен содержать домен olx
        if "olx" not in property_obj.url.lower():
            errors.append(f"URL не соответствует домену OLX: {property_obj.url}")

        return errors
