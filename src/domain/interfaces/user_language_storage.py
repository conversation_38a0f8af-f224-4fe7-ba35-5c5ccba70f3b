"""
Интерфейс хранилища языковых настроек пользователей.
"""
from abc import ABC, abstractmethod
from typing import Optional


class IUserLanguageStorage(ABC):
    """
    Интерфейс хранилища языковых настроек пользователей.
    """
    
    @abstractmethod
    def get_language(self, user_id: int) -> Optional[str]:
        """
        Возвращает язык пользователя.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Optional[str]: Код языка или None, если язык не установлен
        """
        pass
    
    @abstractmethod
    def set_language(self, user_id: int, language: str) -> None:
        """
        Устанавливает язык пользователя.
        
        Args:
            user_id: ID пользователя
            language: Код языка
        """
        pass
