"""
Интерфейс сервиса локализации.
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any


class ILocalizationService(ABC):
    """
    Интерфейс сервиса локализации.
    """
    
    @abstractmethod
    def get_text(self, key: str, language: str, default: Optional[str] = None) -> str:
        """
        Получает локализованный текст по ключу.
        
        Args:
            key: Ключ текста
            language: Код языка
            default: Значение по умолчанию, если текст не найден
            
        Returns:
            str: Локализованный текст
        """
        pass
    
    @abstractmethod
    def get_template(self, key: str, language: str, default: Optional[str] = None) -> str:
        """
        Получает локализованный шаблон по ключу.
        
        Args:
            key: Ключ шаблона
            language: Код языка
            default: Значение по умолчанию, если шаблон не найден
            
        Returns:
            str: Локализованный шаблон
        """
        pass
    
    @abstractmethod
    def format_template(self, template: str, params: Dict[str, Any]) -> str:
        """
        Форматирует шаблон с параметрами.
        
        Args:
            template: Шаблон
            params: Параметры для форматирования
            
        Returns:
            str: Отформатированный текст
        """
        pass
