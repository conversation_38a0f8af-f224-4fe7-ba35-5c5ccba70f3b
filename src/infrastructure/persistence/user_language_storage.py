"""
Хранилище языковых настроек пользователей.
"""
import os
import json
import logging
from typing import Dict, Optional


class UserLanguageStorage:
    """
    Хранилище языковых настроек пользователей.
    """

    def __init__(self, file_path: str, default_language: str = 'ru'):
        """
        Инициализирует хранилище языковых настроек пользователей.

        Args:
            file_path: Путь к файлу с настройками
            default_language: Язык по умолчанию
        """
        self._file_path = file_path
        self._default_language = default_language
        self._languages: Dict[int, str] = {}
        self._logger = logging.getLogger(__name__)

        # Загружаем настройки из файла
        self._load()

    def _load(self) -> None:
        """
        Загружает настройки из файла.
        """
        self._logger.info(f"Загрузка языковых настроек пользователей из файла: {self._file_path}")

        # Проверяем, что файл существует
        if not os.path.exists(self._file_path):
            # Создаем директорию, если ее нет
            os.makedirs(os.path.dirname(self._file_path), exist_ok=True)
            # Создаем пустой файл
            self._save()
            self._logger.info(f"Файл с языковыми настройками не существует, создан пустой файл: {self._file_path}")
            return

        # Загружаем настройки из файла
        try:
            with open(self._file_path, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()

            # Проверяем, что файл не пустой
            if not file_content:
                self._logger.warning(f"Файл с языковыми настройками пуст: {self._file_path}")
                self._languages = {}
                return

            # Загружаем JSON
            data = json.loads(file_content)

            # Преобразуем ключи из строк в числа
            self._languages = {int(user_id): language for user_id, language in data.items()}
            self._logger.info(f"Успешно загружены языковые настройки для {len(self._languages)} пользователей")
            self._logger.info(f"Текущие языковые настройки: {self._languages}")
        except json.JSONDecodeError as e:
            self._logger.error(f"Ошибка декодирования JSON в файле {self._file_path}: {e}")
            # Если файл содержит некорректный JSON, сохраняем текущие настройки
            self._save()
            self._logger.info(f"Сохранены текущие языковые настройки после ошибки декодирования")
        except FileNotFoundError as e:
            self._logger.error(f"Файл с языковыми настройками не найден: {self._file_path}, {e}")
            # Если файл не существует, создаем пустой словарь
            self._languages = {}
        except Exception as e:
            self._logger.error(f"Неожиданная ошибка при загрузке языковых настроек: {e}")
            # В случае других ошибок сохраняем текущие настройки
            self._languages = {}

    def _save(self) -> None:
        """
        Сохраняет настройки в файл.
        """
        self._logger.info(f"Сохранение языковых настроек пользователей в файл: {self._file_path}")

        # Создаем директорию, если ее нет
        try:
            os.makedirs(os.path.dirname(self._file_path), exist_ok=True)
        except Exception as e:
            self._logger.error(f"Ошибка при создании директории для файла с языковыми настройками: {e}")
            return

        # Сохраняем настройки в файл
        try:
            # Преобразуем ключи из чисел в строки
            data = {str(user_id): language for user_id, language in self._languages.items()}

            # Сохраняем в файл
            with open(self._file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)

            self._logger.info(f"Успешно сохранены языковые настройки для {len(self._languages)} пользователей")
            self._logger.info(f"Текущие языковые настройки: {self._languages}")
        except Exception as e:
            self._logger.error(f"Ошибка при сохранении языковых настроек в файл {self._file_path}: {e}")

    def get_language(self, user_id: int) -> Optional[str]:
        """
        Получает язык пользователя.

        Args:
            user_id: ID пользователя

        Returns:
            Optional[str]: Код языка или None, если язык не установлен
        """
        self._logger.info(f"Запрос языка для пользователя {user_id} из хранилища")
        language = self._languages.get(user_id)
        self._logger.info(f"Язык пользователя {user_id} из хранилища: {language}")
        return language

    def set_language(self, user_id: int, language: str) -> None:
        """
        Устанавливает язык пользователя.

        Args:
            user_id: ID пользователя
            language: Код языка
        """
        # Проверяем, изменился ли язык
        old_language = self._languages.get(user_id)
        if old_language == language:
            self._logger.info(f"Язык пользователя {user_id} не изменился: {language}")
            return

        # Устанавливаем новый язык
        self._logger.info(f"Установка языка для пользователя {user_id}: {language} (был: {old_language})")
        self._languages[user_id] = language

        # Сохраняем изменения
        self._save()
