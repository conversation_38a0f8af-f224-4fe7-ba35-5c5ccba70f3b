"""
Хранилище запросов на доступ к боту.
"""
import json
import logging
import os
from typing import Dict, List, Optional, Union, Any, cast

from src.config.paths import get_data_path


class AccessRequestStorage:
    """
    Хранилище запросов на доступ к боту.
    """

    def __init__(self, file_path: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        Инициализирует хранилище запросов на доступ.

        Args:
            file_path: Путь к файлу хранилища (опционально)
            logger: Логгер (опционально)
        """
        self.file_path = file_path or get_data_path("access_requests.json")
        self.logger = logger or logging.getLogger(__name__)
        # Используем Dict[Union[int, str], Any] для хранения запросов и специальных данных
        self.requests: Dict[Union[int, str], Any] = {}
        self._load()
        # Загружаем настройки уведомлений администраторов
        self._load_admin_notifications()

    def _load(self) -> None:
        """
        Загружает запросы на доступ из файла.
        """
        if os.path.exists(self.file_path):
            try:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Преобразуем ключи из строк в целые числа
                    self.requests = {int(k): v for k, v in data.items()}
                self.logger.debug(f"Загружено {len(self.requests)} запросов на доступ")
            except Exception as e:
                self.logger.error(f"Ошибка при загрузке запросов на доступ: {e}")
                self.requests = {}
        else:
            self.logger.debug("Файл с запросами на доступ не найден, создаем новый")
            self.requests = {}
            self._save()

    def _save(self) -> None:
        """
        Сохраняет запросы на доступ в файл.
        """
        try:
            # Создаем директорию, если она не существует
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)

            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self.requests, f, ensure_ascii=False, indent=2)
            self.logger.debug(f"Сохранено {len(self.requests)} запросов на доступ")
        except Exception as e:
            self.logger.error(f"Ошибка при сохранении запросов на доступ: {e}")

    def add_request(self, user_id: int, username: str, first_name: str) -> None:
        """
        Добавляет запрос на доступ.

        Args:
            user_id: ID пользователя
            username: Имя пользователя
            first_name: Имя пользователя
        """
        self.requests[user_id] = {
            "username": username,
            "first_name": first_name,
            "status": "pending"  # pending, approved, rejected
        }
        self._save()
        self.logger.info(f"Добавлен запрос на доступ от пользователя {user_id} ({username})")

    def get_request(self, user_id: int) -> Optional[Dict]:
        """
        Возвращает запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            Optional[Dict]: Запрос на доступ или None, если запрос не найден
        """
        return self.requests.get(user_id)

    def get_pending_requests(self) -> List[Dict]:
        """
        Возвращает список ожидающих запросов на доступ.

        Returns:
            List[Dict]: Список ожидающих запросов на доступ
        """
        return [
            {"user_id": user_id, **request}
            for user_id, request in self.requests.items()
            if request.get("status") == "pending"
        ]

    def approve_request(self, user_id: int) -> bool:
        """
        Подтверждает запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос подтвержден, иначе False
        """
        if user_id in self.requests:
            self.requests[user_id]["status"] = "approved"
            self._save()
            self.logger.info(f"Подтвержден запрос на доступ от пользователя {user_id}")
            return True
        return False

    def reject_request(self, user_id: int) -> bool:
        """
        Отклоняет запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос отклонен, иначе False
        """
        if user_id in self.requests:
            self.requests[user_id]["status"] = "rejected"
            self._save()
            self.logger.info(f"Отклонен запрос на доступ от пользователя {user_id}")
            return True
        return False

    def is_approved(self, user_id: int) -> bool:
        """
        Проверяет, подтвержден ли запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос подтвержден, иначе False
        """
        request = self.get_request(user_id)
        return request is not None and request.get("status") == "approved"

    def is_rejected(self, user_id: int) -> bool:
        """
        Проверяет, отклонен ли запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос отклонен, иначе False
        """
        request = self.get_request(user_id)
        return request is not None and request.get("status") == "rejected"

    def is_pending(self, user_id: int) -> bool:
        """
        Проверяет, ожидает ли запрос на доступ подтверждения.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос ожидает подтверждения, иначе False
        """
        request = self.get_request(user_id)
        return request is not None and request.get("status") == "pending"

    def remove_request(self, user_id: int) -> bool:
        """
        Удаляет запрос на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если запрос удален, иначе False
        """
        if user_id in self.requests:
            del self.requests[user_id]
            self._save()
            self.logger.info(f"Удален запрос на доступ от пользователя {user_id}")
            return True
        return False

    def _save_admin_notifications(self) -> None:
        """
        Сохраняет настройки уведомлений администраторов в отдельный файл.
        """
        try:
            # Путь к файлу с настройками уведомлений
            admin_notifications_path = get_data_path("admin_notifications.json")

            # Создаем директорию, если она не существует
            os.makedirs(os.path.dirname(admin_notifications_path), exist_ok=True)

            # Создаем словарь с настройками
            admin_notifications = {
                "notification_subscribers": self._notification_subscribers
            }

            # Сохраняем в файл
            with open(admin_notifications_path, 'w', encoding='utf-8') as f:
                json.dump(admin_notifications, f, ensure_ascii=False, indent=2)

            self.logger.debug(f"Сохранены настройки уведомлений администраторов")
        except Exception as e:
            self.logger.error(f"Ошибка при сохранении настроек уведомлений администраторов: {e}")

    def _load_admin_notifications(self) -> None:
        """
        Загружает настройки уведомлений администраторов из отдельного файла.
        """
        # Путь к файлу с настройками уведомлений
        admin_notifications_path = get_data_path("admin_notifications.json")

        # Создаем атрибут для хранения подписчиков
        self._notification_subscribers = []

        # Если в основном файле есть настройки уведомлений, переносим их в отдельный файл
        if "notification_subscribers" in self.requests:
            # Сохраняем список подписчиков
            self._notification_subscribers = self.requests["notification_subscribers"]
            # Удаляем из основного файла
            del self.requests["notification_subscribers"]
            # Сохраняем основной файл без настроек уведомлений
            self._save()
            # Сохраняем настройки в отдельный файл
            self._save_admin_notifications()
            self.logger.debug(f"Настройки уведомлений перенесены из основного файла в отдельный")

        # Загружаем настройки из отдельного файла, если он существует
        if os.path.exists(admin_notifications_path):
            try:
                with open(admin_notifications_path, 'r', encoding='utf-8') as f:
                    admin_notifications = json.load(f)

                # Загружаем список подписчиков
                self._notification_subscribers = admin_notifications.get("notification_subscribers", [])
                self.logger.debug(f"Загружены настройки уведомлений администраторов")
            except Exception as e:
                self.logger.error(f"Ошибка при загрузке настроек уведомлений администраторов: {e}")
        else:
            self.logger.debug("Файл с настройками уведомлений администраторов не найден, создаем новый")
            self._save_admin_notifications()

    def subscribe_to_notifications(self, user_id: int) -> bool:
        """
        Подписывает администратора на уведомления о новых запросах на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если подписка успешна, иначе False
        """
        # Проверяем, что пользователь еще не подписан
        if user_id not in self._notification_subscribers:
            self._notification_subscribers.append(user_id)
            # Сохраняем только в отдельный файл
            self._save_admin_notifications()
            self.logger.info(f"Пользователь {user_id} подписан на уведомления о новых запросах")
            return True
        return False

    def unsubscribe_from_notifications(self, user_id: int) -> bool:
        """
        Отписывает администратора от уведомлений о новых запросах на доступ.

        Args:
            user_id: ID пользователя

        Returns:
            bool: True, если отписка успешна, иначе False
        """
        # Проверяем, что пользователь подписан
        if user_id in self._notification_subscribers:
            self._notification_subscribers.remove(user_id)
            # Сохраняем только в отдельный файл
            self._save_admin_notifications()
            self.logger.info(f"Пользователь {user_id} отписан от уведомлений о новых запросах")
            return True
        return False

    def get_notification_subscribers(self) -> List[int]:
        """
        Возвращает список пользователей, подписанных на уведомления о новых запросах.

        Returns:
            List[int]: Список ID пользователей
        """
        return self._notification_subscribers

    def get_rejected_requests(self) -> List[Dict]:
        """
        Возвращает список отклоненных запросов на доступ (заблокированных пользователей).

        Returns:
            List[Dict]: Список отклоненных запросов на доступ
        """
        return [
            {"user_id": user_id, **request}
            for user_id, request in self.requests.items()
            if request.get("status") == "rejected"
        ]
