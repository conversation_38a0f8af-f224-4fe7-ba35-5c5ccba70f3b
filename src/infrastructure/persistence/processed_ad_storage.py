"""
Реализация хранилища обработанных ID объявлений.
"""
import logging
from typing import List, Optional

from src.application.interfaces.processed_ad_storage import IProcessedAdStorage
from src.domain.value_objects.ad_id import AdId
from src.infrastructure.storage.json_storage import JsonStorage
from src.infrastructure.storage.models.processed_ad import ProcessedAd


class DbProcessedAdStorage(IProcessedAdStorage):
    """
    Реализация хранилища обработанных ID объявлений с использованием JSON файла.
    """

    def __init__(self, json_storage: JsonStorage, logger: Optional[logging.Logger] = None):
        """
        Инициализирует хранилище.

        Args:
            json_storage: Экземпляр JsonStorage
            logger: Логгер (опционально)
        """
        self._storage = json_storage
        self._logger = logger or logging.getLogger(__name__)

    def add(self, ad_id: AdId, numeric_id: Optional[str] = None) -> None:
        """
        Добавляет ID объявления в хранилище обработанных.

        Args:
            ad_id: ID объявления для добавления
            numeric_id: Числовой ID объявления (опционально)
        """
        ad_id_str = str(ad_id)

        # Используем числовой ID, если он передан, иначе используем строковый ID
        numeric_id_str = numeric_id if numeric_id else ad_id_str

        self._logger.info(f"[Добавление ID] Попытка добавления числового ID {numeric_id_str} в хранилище")

        # Проверяем, существует ли ID уже в хранилище
        if self.exists(ad_id, numeric_id):
            self._logger.info(f"[Добавление ID] Числовой ID {numeric_id_str} уже существует в хранилище, пропускаем добавление")
            return

        # Создаем объект ProcessedAd с числовым ID и строковым ID
        processed_ad = ProcessedAd(ad_id=numeric_id_str, url_id=ad_id_str)
        success = self._storage.add(processed_ad)

        if success:
            self._logger.info(f"[Добавление ID] Числовой ID {numeric_id_str} успешно добавлен в хранилище")
        else:
            self._logger.error(f"[Добавление ID] Не удалось добавить числовой ID {numeric_id_str} в хранилище")
            raise RuntimeError(f"Не удалось добавить числовой ID {numeric_id_str} в хранилище")

    def exists(self, ad_id: AdId, numeric_id: Optional[str] = None) -> bool:
        """
        Проверяет, существует ли ID объявления в хранилище обработанных.

        Args:
            ad_id: ID объявления для проверки
            numeric_id: Числовой ID объявления (опционально)

        Returns:
            bool: True, если ID уже обработан, иначе False
        """
        ad_id_str = str(ad_id)

        # Используем числовой ID, если он передан, иначе используем строковый ID
        numeric_id_str = numeric_id if numeric_id else ad_id_str

        self._logger.debug(f"[Проверка существования ID] Проверка существования числового ID {numeric_id_str} в хранилище")

        # Проверяем по числовому ID
        result = self._storage.exists(numeric_id_str)

        # Если не нашли по числовому ID и он отличается от строкового, проверяем по строковому ID
        if not result and numeric_id and numeric_id != ad_id_str:
            self._logger.debug(f"[Проверка существования ID] Не найден по числовому ID, проверяем по строковому ID {ad_id_str}")
            result = self._storage.exists(ad_id_str)

        # Логируем результат только на уровне DEBUG
        self._logger.debug(f"[Проверка существования ID] Результат проверки существования ID {numeric_id_str}: {result}")
        return result

    def get_all(self) -> List[AdId]:
        """
        Возвращает список всех обработанных ID объявлений.

        Returns:
            List[AdId]: Список обработанных ID
        """
        processed_ads = self._storage.get_all()
        return [AdId(processed_ad.ad_id) for processed_ad in processed_ads]

    def remove_old_ads(self, max_storage_days: int = 30) -> int:
        """
        Удаляет старые записи об обработанных объявлениях.

        Args:
            max_storage_days: Максимальное время хранения записей в днях (по умолчанию 30)

        Returns:
            int: Количество удаленных записей
        """
        if hasattr(self._storage, 'remove_old_ads'):
            return self._storage.remove_old_ads(max_storage_days)
        else:
            self._logger.warning("Хранилище не поддерживает удаление старых записей")
            return 0
