"""
Пакет для работы с Zyte API.
"""
from src.infrastructure.external.zyte.api_client import ZyteApiClient
from src.infrastructure.external.zyte.exceptions import (
    ZyteAPIError, ZyteAuthenticationError, ZyteClientError, ZyteNetworkError,
    ZyteRateLimitError, ZyteServerError, ZyteTimeoutError, ZyteValidationError
)

__all__ = [
    'ZyteApiClient',
    'ZyteAPIError',
    'ZyteAuthenticationError',
    'ZyteClientError',
    'ZyteNetworkError',
    'ZyteRateLimitError',
    'ZyteServerError',
    'ZyteTimeoutError',
    'ZyteValidationError'
]