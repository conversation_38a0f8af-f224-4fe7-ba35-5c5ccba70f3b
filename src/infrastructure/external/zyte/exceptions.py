"""
Исключения для работы с Zyte API.
"""
from typing import Optional
from typing_extensions import override


class ZyteAPIError(Exception):
    """Базовое исключение для ошибок Zyte API."""

    def __init__(self, message: str, status_code: Optional[int] = None,
                 response: Optional[dict[str, object]] = None, url: Optional[str] = None,
                 retry_after: Optional[int] = None):
        """
        Инициализирует исключение ZyteAPIError.

        Args:
            message: Сообщение об ошибке
            status_code: HTTP-код ответа (опционально)
            response: Ответ API (опционально)
            url: URL запроса (опционально)
            retry_after: Рекомендуемое время ожидания перед повторной попыткой в секундах (опционально)
        """
        self.status_code: Optional[int] = status_code
        self.response: Optional[dict[str, object]] = response
        self.url: Optional[str] = url
        self.retry_after: Optional[int] = retry_after
        self.message: str = message
        super().__init__(self.message)

    @override
    def __str__(self) -> str:
        """Возвращает строковое представление исключения."""
        parts = [self.message]

        if self.status_code:
            parts.append(f"Код ответа: {self.status_code}")

        if self.url:
            parts.append(f"URL: {self.url}")

        if self.retry_after:
            parts.append(f"Повторить через: {self.retry_after} сек.")

        return " | ".join(parts)


class ZyteRateLimitError(ZyteAPIError):
    """Исключение для ошибок превышения лимита запросов."""
    pass


class ZyteTimeoutError(ZyteAPIError):
    """Исключение для ошибок таймаута."""
    pass


class ZyteAuthenticationError(ZyteAPIError):
    """Исключение для ошибок аутентификации."""
    pass


class ZyteServerError(ZyteAPIError):
    """Исключение для серверных ошибок."""
    pass


class ZyteClientError(ZyteAPIError):
    """Исключение для клиентских ошибок."""
    pass


class ZyteNetworkError(ZyteAPIError):
    """Исключение для сетевых ошибок."""
    pass


class ZyteValidationError(ZyteAPIError):
    """Исключение для ошибок валидации."""
    pass
