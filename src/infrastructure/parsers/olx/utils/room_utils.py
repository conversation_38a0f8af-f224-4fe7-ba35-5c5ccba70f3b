"""
Обработка комнат OLX.
Включает функции для извлечения и валидации:
- количества комнат
- типа планировки (смежные/раздельные)
"""
import logging
import re
from typing import Dict, Optional, Any

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text

# Настройка логгера
logger = logging.getLogger(__name__)

# Шаблоны для поиска комнат
ROOMS_PATTERNS = [
    r'(\d+)\s*-?\s*(?:комнатн|кімнатн)',  # 2-комнатная, 2 комнатная
    r'(\d+)\s*к(?:омн|імн)?',  # 2к, 2комн
    r'(\d+)\s*кк',  # 2кк
    r'комнат\s*[:-]\s*(\d+)',  # комнат: 2
    r'кімнат\s*[:-]\s*(\d+)',  # кімнат: 2
    r'(\d+)[\s-]*(?:комнат|кімнат)',  # 2 комнат, 2-комнат
    r'(?:комнат|кімнат)[\s:-]*(\d+)',  # комнат 2, комнат:2
    r'(?:комнаты|кімнати)[\s:-]*(\d+)',  # комнаты 2, комнаты:2
    r'(\d+)(?:-комнатная|-комн\.|-к\.|-комн|-к)'  # Старый паттерн
]

# Шаблоны для поиска студий
STUDIO_PATTERNS = [
    r'студия',
    r'студія',
    r'квартира-студия',
    r'квартира-студія',
    r'студио'
]

# Шаблоны для поиска типа планировки
LAYOUT_PATTERNS = {
    'separate': [
        r'раздельн',
        r'окрем',
        r'изолирован',
        r'ізольован'
    ],
    'adjacent': [
        r'смежн',
        r'сміжн',
        r'проходн',
        r'прохідн'
    ]
}


class RoomUtils:
    """
    Класс для обработки комнат OLX.
    """

    @staticmethod
    def extract_rooms_info_from_text(text: str) -> Dict[str, Any]:
        """
        Извлекает информацию о комнатах из текста.

        Args:
            text: Текст для анализа

        Returns:
            Dict с ключами:
            - rooms_count: количество комнат
            - is_studio: является ли квартира студией
            - is_separate_rooms: раздельные ли комнаты
        """
        result: Dict[str, Any] = {
            'rooms_count': None,
            'is_studio': None,
            'is_separate_rooms': None
        }

        if not text:
            return result

        # Очищаем текст
        text = clean_text(text)
        text_lower = text.lower()

        # Проверяем является ли квартира студией
        is_studio = any(re.search(pattern, text_lower, re.IGNORECASE) for pattern in STUDIO_PATTERNS)
        if is_studio:
            result['rooms_count'] = 1
            result['is_studio'] = True
            return result

        # Ищем количество комнат
        for pattern in ROOMS_PATTERNS:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    rooms_count = int(match.group(1))
                    # Проверяем, что количество комнат разумное (1-10)
                    if 1 <= rooms_count <= 10:
                        result['rooms_count'] = rooms_count
                        result['is_studio'] = False
                        break
                except (ValueError, IndexError):
                    continue

        # Определяем тип планировки
        if result['rooms_count'] is not None and result['rooms_count'] > 1:
            # Проверяем на раздельные комнаты
            is_separate = any(re.search(pattern, text_lower, re.IGNORECASE) for pattern in LAYOUT_PATTERNS['separate'])
            if is_separate:
                result['is_separate_rooms'] = True
            else:
                # Проверяем на смежные комнаты
                is_adjacent = any(re.search(pattern, text_lower, re.IGNORECASE) for pattern in LAYOUT_PATTERNS['adjacent'])
                if is_adjacent:
                    result['is_separate_rooms'] = False

        return result

    @staticmethod
    def extract_rooms_count_from_text(text: str) -> Optional[int]:
        """
        Извлекает количество комнат из текста.

        Args:
            text: Текст с количеством комнат

        Returns:
            Optional[int]: Количество комнат или None, если количество комнат не найдено
        """
        # Используем новый метод для извлечения информации о комнатах
        rooms_info = RoomUtils.extract_rooms_info_from_text(text)
        return rooms_info['rooms_count']

    @staticmethod
    def extract_rooms_count_from_parameters(params: Dict[str, str]) -> Optional[int]:
        """
        Извлекает количество комнат из параметров объявления.

        Args:
            params: Словарь с параметрами объявления

        Returns:
            Optional[int]: Количество комнат или None, если количество комнат не найдено
        """
        try:
            # Проверяем наличие параметра "Количество комнат"
            if 'Количество комнат' in params:
                try:
                    # Пробуем напрямую преобразовать в число
                    return int(params['Количество комнат'])
                except ValueError:
                    # Если не получилось, используем метод извлечения из текста
                    return RoomUtils.extract_rooms_count_from_text(params['Количество комнат'])

            # Проверяем все параметры на наличие информации о комнатах
            for param_name, param_value in params.items():
                # Проверяем, содержит ли название параметра слово "комнат"
                if 'комнат' in param_name.lower() or 'кімнат' in param_name.lower():
                    try:
                        # Пробуем напрямую преобразовать в число
                        return int(param_value)
                    except ValueError:
                        # Если не получилось, используем метод извлечения из текста
                        return RoomUtils.extract_rooms_count_from_text(param_value)
        except Exception as e:
            logger.error(f"Ошибка при извлечении количества комнат из параметров: {e}")

        return None

    @staticmethod
    def extract_rooms_count_from_soup(soup: BeautifulSoup) -> Optional[int]:
        """
        Извлекает количество комнат из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Optional[int]: Количество комнат или None, если количество комнат не найдено
        """
        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Извлекаем количество комнат из параметров
            rooms_count = RoomUtils.extract_rooms_count_from_parameters(params)
            if rooms_count is not None:
                return rooms_count

            # Если не нашли в параметрах, пробуем извлечь из заголовка
            from src.infrastructure.parsers.olx.utils.html_utils import get_title
            title = get_title(soup)
            if title:
                # Используем новый метод для извлечения информации о комнатах
                rooms_info = RoomUtils.extract_rooms_info_from_text(title)
                if rooms_info['rooms_count'] is not None:
                    return rooms_info['rooms_count']

            # Если не нашли в заголовке, пробуем извлечь из описания
            from src.infrastructure.parsers.olx.utils.html_utils import get_description
            description = get_description(soup)
            if description:
                # Ищем в первых 100 символах описания, чтобы избежать ложных совпадений
                short_desc = description[:100] if len(description) > 100 else description
                rooms_info = RoomUtils.extract_rooms_info_from_text(short_desc)
                if rooms_info['rooms_count'] is not None:
                    return rooms_info['rooms_count']

                # Если не нашли в коротком описании, пробуем в полном
                rooms_info = RoomUtils.extract_rooms_info_from_text(description)
                if rooms_info['rooms_count'] is not None:
                    return rooms_info['rooms_count']

        except Exception as e:
            logger.error(f"Ошибка при извлечении количества комнат из объекта BeautifulSoup: {e}")

        return None

    @staticmethod
    def extract_rooms_count_from_title(title: str) -> Optional[int]:
        """
        Извлекает количество комнат из заголовка объявления.

        Args:
            title: Заголовок объявления

        Returns:
            Optional[int]: Количество комнат или None, если количество комнат не найдено
        """
        if not title:
            return None

        # Очищаем текст
        title = clean_text(title)

        # Ищем количество комнат в тексте
        rooms_match = re.search(r'(\d+)(?:-комнатная|-комн\.|-к\.|-комн|-к)', title)
        if not rooms_match:
            # Проверяем на наличие слова "студия"
            if 'студия' in title.lower() or 'студио' in title.lower():
                return 1
            return None

        try:
            return int(rooms_match.group(1))
        except ValueError:
            return None

    @staticmethod
    def format_rooms_count(rooms_count: int) -> str:
        """
        Форматирует количество комнат.

        Args:
            rooms_count: Количество комнат

        Returns:
            str: Отформатированное количество комнат
        """
        if rooms_count == 1:
            return "1-комнатная"
        elif rooms_count == 2:
            return "2-комнатная"
        elif rooms_count == 3:
            return "3-комнатная"
        elif rooms_count == 4:
            return "4-комнатная"
        else:
            return f"{rooms_count}-комнатная"

    @staticmethod
    def validate_rooms_info(
        rooms_count: Optional[int],
        is_studio: Optional[bool],
        total_area: Optional[float] = None
    ) -> bool:
        """
        Проверяет корректность информации о комнатах.

        Args:
            rooms_count: Количество комнат
            is_studio: Является ли квартира студией
            total_area: Общая площадь (опционально)

        Returns:
            True если данные корректны, False иначе
        """
        # Проверяем что количество комнат положительное
        if rooms_count is not None and rooms_count <= 0:
            return False

        # Проверяем что количество комнат в разумных пределах
        if rooms_count is not None and rooms_count > 10:  # Максимум 10 комнат
            return False

        # Если это студия, количество комнат должно быть 1
        if is_studio and rooms_count != 1:
            return False

        # Если указана общая площадь, проверяем что на каждую комнату
        # приходится хотя бы 8 квадратных метров (минимальный размер комнаты)
        if total_area is not None and rooms_count is not None and rooms_count > 0:
            if total_area / rooms_count < 8:
                return False

        return True

    @staticmethod
    def extract_rooms_info_from_soup(soup: BeautifulSoup) -> Dict[str, Any]:
        """
        Извлекает информацию о комнатах из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Dict с ключами 'rooms_count', 'is_studio', 'is_separate_rooms'
        """
        result: Dict[str, Any] = {
            'rooms_count': None,
            'is_studio': None,
            'is_separate_rooms': None
        }

        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Извлекаем количество комнат из параметров
            rooms_count = RoomUtils.extract_rooms_count_from_parameters(params)
            if rooms_count is not None:
                result['rooms_count'] = rooms_count

                # Проверяем на студию
                if rooms_count == 1 and any(param.lower() in ["студия", "студія"] for param in params.values()):
                    result['is_studio'] = True
                else:
                    result['is_studio'] = False

                # Проверяем тип планировки
                for _, param_value in params.items():  # Используем _ для неиспользуемой переменной
                    param_value_lower = param_value.lower()

                    # Проверяем на раздельные комнаты
                    if any(pattern in param_value_lower for pattern in LAYOUT_PATTERNS['separate']):
                        result['is_separate_rooms'] = True
                        break

                    # Проверяем на смежные комнаты
                    if any(pattern in param_value_lower for pattern in LAYOUT_PATTERNS['adjacent']):
                        result['is_separate_rooms'] = False
                        break

            # Если не нашли в параметрах, пробуем извлечь из заголовка
            if result['rooms_count'] is None:
                from src.infrastructure.parsers.olx.utils.html_utils import get_title
                title = get_title(soup)
                if title:
                    # Используем новый метод для извлечения информации о комнатах
                    title_info = RoomUtils.extract_rooms_info_from_text(title)
                    # Обновляем только незаполненные поля
                    for key, value in title_info.items():
                        if value is not None and result[key] is None:
                            result[key] = value

            # Если не нашли в заголовке, пробуем извлечь из описания
            if result['rooms_count'] is None or result['is_separate_rooms'] is None:
                from src.infrastructure.parsers.olx.utils.html_utils import get_description
                description = get_description(soup)
                if description:
                    # Ищем в первых 100 символах описания, чтобы избежать ложных совпадений
                    short_desc = description[:100] if len(description) > 100 else description
                    desc_info = RoomUtils.extract_rooms_info_from_text(short_desc)
                    # Обновляем только незаполненные поля
                    for key, value in desc_info.items():
                        if value is not None and result[key] is None:
                            result[key] = value

                    # Если все еще не нашли всю информацию, пробуем в полном описании
                    if result['rooms_count'] is None or result['is_separate_rooms'] is None:
                        full_desc_info = RoomUtils.extract_rooms_info_from_text(description)
                        # Обновляем только незаполненные поля
                        for key, value in full_desc_info.items():
                            if value is not None and result[key] is None:
                                result[key] = value

        except Exception as e:
            logger.error(f"Ошибка при извлечении информации о комнатах из объекта BeautifulSoup: {e}")

        return result

    @staticmethod
    def normalize_rooms_count(rooms_count: Optional[int]) -> Optional[int]:
        """
        Нормализует количество комнат.

        Args:
            rooms_count: Количество комнат для нормализации

        Returns:
            Нормализованное количество комнат или None
        """
        if rooms_count is None:
            return None

        # Количество комнат должно быть целым числом
        return int(rooms_count)
