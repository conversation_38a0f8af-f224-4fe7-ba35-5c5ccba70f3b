"""
Утилиты для работы с HTML.
"""
import re
import logging
from typing import Dict, List, Optional, Tuple
from urllib.parse import parse_qs, urljoin, urlparse

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.error_handling import safe_extraction


def clean_text(text: Optional[str]) -> str:
    """
    Очищает текст от лишних пробелов и переносов строк.

    Args:
        text: Исходный текст

    Returns:
        str: Очищенный текст
    """
    if not text:
        return ""

    # Заменяем множественные пробелы и переносы строк на один пробел
    text = re.sub(r'\s+', ' ', text)

    # Удаляем пробелы в начале и конце строки
    return text.strip()


def extract_number(text: Optional[str]) -> Optional[float]:
    """
    Извлекает число из текста.

    Args:
        text: Исходный текст

    Returns:
        Optional[float]: Число или None, если число не найдено
    """
    if not text:
        return None

    # Ищем число в тексте
    match = re.search(r'([\d\s.,]+)', text)
    if not match:
        return None

    # Очищаем число от пробелов и заменяем запятую на точку
    number_str = match.group(1).strip().replace(' ', '').replace(',', '.')

    try:
        return float(number_str)
    except ValueError:
        return None

def extract_url_param(url: Optional[str], param: str) -> Optional[str]:
    """
    Извлекает значение параметра из URL.

    Args:
        url: URL
        param: Имя параметра

    Returns:
        Optional[str]: Значение параметра или None, если параметр не найден
    """
    if not url:
        return None

    # Парсим URL
    parsed_url = urlparse(url)

    # Извлекаем параметры
    query_params = parse_qs(parsed_url.query)

    # Возвращаем значение параметра
    return query_params.get(param, [None])[0]


def extract_url_path_segment(url: Optional[str], segment_index: int) -> Optional[str]:
    """
    Извлекает сегмент пути из URL.

    Args:
        url: URL
        segment_index: Индекс сегмента (0-based)

    Returns:
        Optional[str]: Сегмент пути или None, если сегмент не найден
    """
    if not url:
        return None

    # Парсим URL
    parsed_url = urlparse(url)

    # Разбиваем путь на сегменты
    path_segments = [segment for segment in parsed_url.path.split('/') if segment]

    # Возвращаем сегмент пути
    if 0 <= segment_index < len(path_segments):
        return path_segments[segment_index]

    return None


def extract_meta_property(soup: BeautifulSoup, property_name: str) -> Optional[str]:
    """
    Извлекает значение мета-тега с указанным property.

    Args:
        soup: Объект BeautifulSoup
        property_name: Имя property

    Returns:
        Optional[str]: Значение мета-тега или None, если мета-тег не найден
    """
    meta_tag = soup.find('meta', property=property_name)
    if meta_tag:
        return meta_tag.get('content')
    return None

def make_absolute_url(base_url: str, relative_url: str) -> str:
    """
    Преобразует относительный URL в абсолютный.

    Args:
        base_url: Базовый URL
        relative_url: Относительный URL

    Returns:
        str: Абсолютный URL
    """
    return urljoin(base_url, relative_url)


def parse_html(html: str) -> BeautifulSoup:
    """
    Парсит HTML и возвращает объект BeautifulSoup.

    Args:
        html: HTML строка

    Returns:
        BeautifulSoup: Объект BeautifulSoup
    """
    return BeautifulSoup(html, 'html.parser')


def get_title(soup: BeautifulSoup) -> Optional[str]:
    """
    Извлекает заголовок объявления.

    Args:
        soup: Объект BeautifulSoup

    Returns:
        Optional[str]: Заголовок объявления или None, если заголовок не найден
    """
    # Пробуем разные селекторы, так как OLX может менять структуру страницы
    selectors = [
        'h4.css-10ofhqw',  # Новый селектор из примера
        'div[data-cy="ad_title"] h4',  # Селектор по data-атрибуту
        'div[data-testid="ad_title"] h4',  # Селектор по data-testid
        'h1.css-1soizd2',  # Старый селектор
        'h1.css-1juynto',  # Старый селектор
        'h1[data-cy="ad_title"]',  # Селектор по data-атрибуту
        'h4.css-*',  # Любой h4 с классом, начинающимся с css-
        'h1.css-*',  # Любой h1 с классом, начинающимся с css-
        'h1',  # Любой h1 на странице
        'h4'  # Любой h4 на странице
    ]

    for selector in selectors:
        try:
            title_element = soup.select_one(selector)
            if title_element:
                return clean_text(title_element.text)
        except Exception:
            continue

    # Если не нашли по селекторам, пробуем найти через мета-теги
    meta_title = extract_meta_property(soup, 'og:title')
    if meta_title:
        return clean_text(meta_title)

    return None


def get_description(soup: BeautifulSoup) -> Optional[str]:
    """
    Извлекает описание объявления.

    Args:
        soup: Объект BeautifulSoup

    Returns:
        Optional[str]: Описание объявления или None, если описание не найдено
    """
    # Пробуем разные селекторы для описания
    selectors = [
        'div.css-19duwlz',  # Новый селектор из примера
        'div[data-cy="ad_description"] div.css-19duwlz',  # Новый селектор с data-cy
        'div[data-testid="ad_description"] div.css-19duwlz',  # Новый селектор с data-testid
        'div[data-cy="ad_description"] div',  # Старый селектор
        'div[data-testid="ad_description"] div',  # Селектор с data-testid
        'div.css-*[data-cy="ad_description"]',  # Любой div с классом, начинающимся с css- и data-cy
        'div.css-*[data-testid="ad_description"]',  # Любой div с классом, начинающимся с css- и data-testid
    ]

    for selector in selectors:
        try:
            description_element = soup.select_one(selector)
            if description_element:
                return clean_text(description_element.text)
        except Exception:
            continue

    # Если не нашли по селекторам, пробуем найти через мета-теги
    meta_description = extract_meta_property(soup, 'og:description')
    if meta_description:
        return clean_text(meta_description)

    return None


@safe_extraction(default_value={})
def get_parameters(soup: BeautifulSoup) -> Dict[str, str]:
    """
    Извлекает параметры объявления.

    Args:
        soup: Объект BeautifulSoup

    Returns:
        Dict[str, str]: Словарь с параметрами объявления
    """
    params = {}
    logger = logging.getLogger(__name__)

    try:
        # Новый основной селектор для блока параметров
        params_container = soup.select_one('div[data-testid="ad-parameters-container"]')
        if params_container:
            logger.debug("Найден блок параметров по селектору div[data-testid=\"ad-parameters-container\"]")
            # Ищем все параграфы с параметрами
            param_elements = params_container.select('p')
            logger.debug(f"Найдено {len(param_elements)} параметров в блоке")

            for param_element in param_elements:
                param_text = clean_text(param_element.text)
                logger.debug(f"Текст параметра: {param_text}")

                # Проверяем на наличие двоеточия или разделителя
                if ':' in param_text:
                    key, value = param_text.split(':', 1)
                    params[key.strip()] = value.strip()
                    logger.debug(f"Добавлен параметр с двоеточием: {key.strip()} = {value.strip()}")
                elif ': ' in param_text:
                    key, value = param_text.split(': ', 1)
                    params[key.strip()] = value.strip()
                    logger.debug(f"Добавлен параметр с двоеточием и пробелом: {key.strip()} = {value.strip()}")
                # Проверяем на наличие пробела и цифры (например, "Поверх 3")
                elif ' ' in param_text:
                    parts = param_text.split(' ', 1)
                    if len(parts) == 2 and parts[1].strip().isdigit():
                        key, value = parts
                        params[key.strip()] = value.strip()
                        logger.debug(f"Добавлен параметр с пробелом: {key.strip()} = {value.strip()}")
                # Если нет разделителя, добавляем как тег без значения
                else:
                    params[param_text] = "true"
                    logger.debug(f"Добавлен параметр без значения: {param_text} = true")

        # Если не нашли параметры через основной селектор, пробуем альтернативные
        if not params:
            logger.debug("Пробуем альтернативные селекторы для параметров")
            selectors = [
                'div[data-cy="ad-parameters-container"]',  # Альтернативный селектор с data-cy
                'div.css-41yf00 p',  # Селектор по классу
                'div.css-41yf00',  # Селектор по классу для блока параметров
                'div[data-cy="ad_parameters"] p',  # Селектор по data-атрибуту
                'div[data-testid="ad_parameters"] p',  # Селектор по data-testid
                'button.css-1cju8pu, div.css-ae1s7g',  # Старые селекторы для кнопок с параметрами
                'div.css-1epmoz1 p',  # Старый селектор
                'div[data-cy="ad-details-params"] div',  # Селектор по data-атрибуту
                'div[data-testid="ad-details-params"] div',  # Селектор по data-testid
                'div.css-1epmoz1',  # Старый селектор для блока параметров
                'div.css-1qvf6gm',  # Еще один возможный селектор для блока параметров
                'div[data-cy="ad_parameters"] div',  # Еще один селектор по data-атрибуту
                'div[data-testid="ad_parameters"] div',  # Еще один селектор по data-testid
                'div[data-testid="ad-parameters-container"] div.css-ae1s7g p',  # Новый селектор для параметров
                'div.css-ae1s7g p.css-z0m36u',  # Еще один новый селектор
            ]

            for selector in selectors:
                params_elements = soup.select(selector)
                logger.debug(f"Найдено {len(params_elements)} элементов по селектору {selector}")
                if params_elements:
                    for param_element in params_elements:
                        param_text = clean_text(param_element.text)
                        if ':' in param_text:
                            key, value = param_text.split(':', 1)
                            params[key.strip()] = value.strip()
                            logger.debug(f"Добавлен параметр: {key.strip()} = {value.strip()}")
                        elif ': ' in param_text:
                            key, value = param_text.split(': ', 1)
                            params[key.strip()] = value.strip()
                            logger.debug(f"Добавлен параметр с двоеточием и пробелом: {key.strip()} = {value.strip()}")

                    # Если нашли хотя бы один параметр, прекращаем поиск
                    if params:
                        break
    except Exception as e:
        print(f"Ошибка при извлечении параметров объявления: {e}")

    return params


def get_images(soup: BeautifulSoup, max_images: int = 10) -> List[str]:
    """
    Извлекает URL изображений.

    Args:
        soup: Объект BeautifulSoup
        max_images: Максимальное количество изображений

    Returns:
        List[str]: Список URL изображений
    """
    images = []

    try:
        image_elements = soup.select('div.swiper-zoom-container img')
        for image_element in image_elements[:max_images]:
            image_url = image_element.get('src')
            if image_url:
                images.append(image_url)
    except Exception as e:
        print(f"Ошибка при извлечении изображений: {e}")

    return images

def get_pagination_info(soup: BeautifulSoup) -> Tuple[int, int]:
    """
    Извлекает информацию о пагинации.

    Args:
        soup: Объект BeautifulSoup

    Returns:
        Tuple[int, int]: Текущая страница и общее количество страниц
    """
    current_page = 1
    total_pages = 1

    try:
        pagination_element = soup.select_one('ul.pagination-list')
        if pagination_element:
            # Текущая страница
            current_page_element = pagination_element.select_one('li.active span')
            if current_page_element:
                current_page_text = clean_text(current_page_element.text)
                try:
                    current_page = int(current_page_text)
                except ValueError:
                    pass

            # Общее количество страниц
            page_elements = pagination_element.select('li a')
            if page_elements:
                page_numbers = []
                for page_element in page_elements:
                    page_text = clean_text(page_element.text)
                    try:
                        page_number = int(page_text)
                        page_numbers.append(page_number)
                    except ValueError:
                        pass

                if page_numbers:
                    total_pages = max(page_numbers)

    except Exception as e:
        print(f"Ошибка при извлечении информации о пагинации: {e}")

    return current_page, total_pages
