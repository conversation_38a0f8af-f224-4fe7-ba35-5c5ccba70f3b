"""
Обработка локаций OLX.
"""
import re
from typing import Dict, List, Optional, Tuple, Any

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text
from src.infrastructure.logging.logger import setup_logger

# Настройка логгера
logger = setup_logger('olx.location_utils')

# Шаблоны для поиска местоположения
LOCATION_PATTERNS = [
    re.compile(r'Местоположение[:\s-]+(.+?)(?:\n|$)'),
    re.compile(r'Адрес[:\s-]+(.+?)(?:\n|$)'),
    re.compile(r'Расположение[:\s-]+(.+?)(?:\n|$)'),
    re.compile(r'Локация[:\s-]+(.+?)(?:\n|$)')
]

# Скомпилированные шаблоны для поиска компонентов адреса
COMPILED_PATTERNS = {
    'city': [
        re.compile(r'(?:^|,\s*)([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)(?:,|$)'),  # Киев, Харьков, Днепр
        re.compile(r'(?:г\.?|город)\s+([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)'),  # г. Киев, город Харьков
        re.compile(r'([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)\s+(?:обл\.?|область)')  # Киевская обл.
    ],
    'district': [
        re.compile(r'(?:,\s*)([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)\s+(?:р-н|район)'),  # Дарницкий р-н
        re.compile(r'(?:,\s*)([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)\s+(?:р\.?|район)'),  # Дарницкий р.
        re.compile(r'(?:,\s*)([А-ЯЁ][а-яё]+(?:-[А-ЯЁ][а-яё]+)*)')  # , Дарницкий
    ],
    'street': [
        re.compile(r'(?:ул\.?|улица)\s+([А-ЯЁ][а-яё]+(?:\s+[А-ЯЁ][а-яё]+)*)'),  # ул. Крещатик
        re.compile(r'(?:пр\.?|проспект)\s+([А-ЯЁ][а-яё]+(?:\s+[А-ЯЁ][а-яё]+)*)'),  # пр. Победы
        re.compile(r'(?:,\s*)([А-ЯЁ][а-яё]+(?:\s+[А-ЯЁ][а-яё]+)*)\s+(?:ул\.?|улица)'),  # , Крещатик ул.
        re.compile(r'(?:,\s*)([А-ЯЁ][а-яё]+(?:\s+[А-ЯЁ][а-яё]+)*)')  # , Крещатик
    ],
    'house_number': [
        re.compile(r'(?:д\.?|дом)\s+(\d+(?:\s*[а-яА-Я])?(?:/\d+)?)'),  # д. 10, дом 10а
        re.compile(r'(?:,\s*)(\d+(?:\s*[а-яА-Я])?(?:/\d+)?)'),  # , 10, 10а, 10/2
        re.compile(r'(\d+(?:\s*[а-яА-Я])?(?:/\d+)?)')  # 10, 10а, 10/2
    ]
}


class LocationUtils:
    """
    Класс для обработки локаций OLX.
    """

    @staticmethod
    def extract_location_info(text: str) -> Dict[str, Any]:
        """
        Извлечение полной информации о местоположении

        Args:
            text: Текст для анализа

        Returns:
            Dict[str, Any]: Словарь с информацией о местоположении:
                - location (str): Полный адрес
                - city (str|None): Город
                - district (str|None): Район
                - street (str|None): Улица
                - house_number (str|None): Номер дома

        Examples:
            >>> extract_location_info("Киев, Дарницкий")
            {
                'location': 'Киев, Дарницкий',
                'city': 'Киев',
                'district': 'Дарницкий',
                'street': None,
                'house_number': None
            }

            >>> extract_location_info("Львов, ул. Франко 25")
            {
                'location': 'Львов, ул. Франко 25',
                'city': 'Львов',
                'district': None,
                'street': 'Франко',
                'house_number': '25'
            }
        """
        result = {
            'location': text.strip() if text else None,
            'city': None,
            'district': None,
            'street': None,
            'house_number': None
        }

        if not text:
            return result

        text = text.strip()

        try:
            # Поиск местоположения через LOCATION_PATTERNS
            for pattern in LOCATION_PATTERNS:
                if match := pattern.search(text):
                    result['location'] = match.group(1).strip()
                    break

            # Поиск города
            for pattern in COMPILED_PATTERNS['city']:
                if match := pattern.search(text):
                    result['city'] = match.group(1)
                    break

            # Поиск района
            for pattern in COMPILED_PATTERNS['district']:
                if match := pattern.search(text):
                    result['district'] = match.group(1)
                    break

            # Поиск улицы
            for pattern in COMPILED_PATTERNS['street']:
                if match := pattern.search(text):
                    result['street'] = match.group(1)
                    break

            # Поиск дома
            for pattern in COMPILED_PATTERNS['house_number']:
                if match := pattern.search(text):
                    result['house_number'] = match.group(1)
                    break

            # Формируем полный адрес, если не был найден через шаблоны
            if not result['location'] or result['location'] == text:
                parts = [result['city'], result['district'], result['street'], result['house_number']]
                formatted_location = ', '.join(filter(None, parts))
                if formatted_location:
                    result['location'] = formatted_location

        except Exception as e:
            logger.error(f"Ошибка при разборе местоположения '{text}': {str(e)}")

        return result

    @staticmethod
    def extract_location_from_text(text: Optional[str]) -> Dict[str, str]:
        """
        Извлекает информацию о местоположении из текста.

        Args:
            text: Текст с информацией о местоположении

        Returns:
            Dict[str, str]: Словарь с информацией о местоположении
        """
        # Для обратной совместимости с тестами используем старый метод
        if not text:
            return {}

        # Очищаем текст
        text = clean_text(text)

        # Разбиваем текст на части
        parts = [part.strip() for part in re.split(r'[,;]', text) if part.strip()]

        result = {
            'full_address': text
        }

        if len(parts) >= 1:
            result['city'] = parts[0]

        if len(parts) >= 2:
            result['district'] = parts[1]

        if len(parts) >= 3:
            result['street'] = parts[2]

        # Для логирования
        logger.debug(f"Извлечена информация о местоположении: {result}")

        return result

    @staticmethod
    def _extract_city_from_location_blocks(soup: BeautifulSoup) -> Optional[str]:
        """
        Вспомогательный метод для извлечения города из блоков местоположения.
        Пробует как новую, так и старую структуру OLX.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Optional[str]: Название города или None, если не найдено
        """
        # Пробуем новую структуру
        try:
            location_container = soup.select_one('div.css-1dp6pbg')
            if location_container:
                location_block = location_container.select_one('div.css-17dk4rn')
                if location_block:
                    city_element = location_block.select_one('p.css-7wnksb')
                    if city_element:
                        city_text = clean_text(city_element.text)
                        city = re.sub(r',.*$', '', city_text).strip()
                        logger.debug(f"Извлечен город из новой структуры: {city}")
                        return city
        except Exception as e:
            logger.debug(f"Ошибка при извлечении города из новой структуры: {e}")

        # Пробуем старую структуру
        try:
            location_block = soup.select_one('div[data-testid="map-aside-section"]')
            if location_block:
                city_element = location_block.select_one('p.css-7wnksb')
                if city_element:
                    city_text = clean_text(city_element.text)
                    city = re.sub(r',.*$', '', city_text).strip()
                    logger.debug(f"Извлечен город из старой структуры: {city}")
                    return city
        except Exception as e:
            logger.debug(f"Ошибка при извлечении города из старой структуры: {e}")

        return None

    @staticmethod
    def extract_location_from_soup(soup: BeautifulSoup) -> Dict[str, str]:
        """
        Извлекает информацию о местоположении из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Dict[str, str]: Словарь с информацией о местоположении
        """
        # Пробуем найти местоположение в новой структуре OLX
        try:
            # Сначала ищем блок div.css-17dk4rn независимо от контейнера
            location_block = soup.select_one('div.css-17dk4rn')
            if location_block:
                logger.debug("Найден блок местоположения div.css-17dk4rn")

                # Ищем город и область
                city_element = location_block.select_one('p.css-7wnksb')
                region_element = location_block.select_one('p.css-z0m36u')

                if city_element:
                    city_text = clean_text(city_element.text)
                    # Удаляем запятую и все после нее, если она есть
                    city = re.sub(r',.*$', '', city_text).strip()
                    logger.debug(f"Извлечен город из блока div.css-17dk4rn: {city}")

                    result = {'city': city, 'full_address': city}

                    # Сохраняем область в словаре, но не добавляем в full_address
                    if region_element:
                        region = clean_text(region_element.text)
                        logger.debug(f"Извлечен регион из блока div.css-17dk4rn: {region}, но не добавляем его в адрес")
                        result['region'] = region

                    return result

            # Если блок div.css-17dk4rn не найден, ищем в контейнере div.css-1dp6pbg
            location_container = soup.select_one('div.css-1dp6pbg')
            if location_container:
                logger.debug("Найден основной контейнер местоположения div.css-1dp6pbg")

                # Ищем город напрямую в контейнере
                city_element = location_container.select_one('p.css-7wnksb')
                if city_element:
                    city_text = clean_text(city_element.text)
                    city = re.sub(r',.*$', '', city_text).strip()
                    logger.debug(f"Извлечен город из контейнера div.css-1dp6pbg: {city}")

                    result = {'city': city, 'full_address': city}

                    # Ищем область
                    region_element = location_container.select_one('p.css-z0m36u')
                    if region_element:
                        region = clean_text(region_element.text)
                        logger.debug(f"Извлечен регион из контейнера div.css-1dp6pbg: {region}, но не добавляем его в адрес")
                        result['region'] = region

                    return result
        except Exception as e:
            logger.error(f"Ошибка при извлечении местоположения из новой структуры: {e}")

        # Пробуем найти местоположение в старой структуре с data-testid="map-aside-section"
        try:
            location_block = soup.select_one('div[data-testid="map-aside-section"]')

            if location_block:
                logger.debug("Найден блок местоположения div[data-testid=\"map-aside-section\"] (старая структура)")
                # Ищем город и область
                city_element = location_block.select_one('p.css-7wnksb')
                region_element = location_block.select_one('p.css-z0m36u')

                if city_element:
                    city_text = clean_text(city_element.text)
                    # Удаляем запятую и все после нее, если она есть
                    city = re.sub(r',.*$', '', city_text).strip()
                    logger.debug(f"Извлечен город из старой структуры: {city}")

                    result = {'city': city, 'full_address': city}

                    # Сохраняем область в словаре, но не добавляем в full_address
                    if region_element:
                        region = clean_text(region_element.text)
                        logger.debug(f"Извлечен регион из старой структуры: {region}, но не добавляем его в адрес")
                        result['region'] = region

                    return result
        except Exception as e:
            logger.error(f"Ошибка при извлечении местоположения из старой структуры: {e}")
            # Для совместимости с тестами
            print(f"Ошибка при извлечении местоположения из старой структуры: {e}")

        # Запасные селекторы для местоположения
        selectors = [
            'div.css-17dk4rn p.css-7wnksb',  # Новая структура - город в блоке местоположения
            'div.css-1dp6pbg p.css-7wnksb',  # Новая структура - город в основном контейнере
            'div.css-1deibjd p.css-7wnksb',  # Альтернативный селектор для города
            'a[href*="/nedvizhimost"] p',  # Старый селектор
            'p.css-*[data-testid="location-date"]',  # Новый селектор с data-атрибутом
            'div[data-cy="ad-footer"] p',  # Селектор по data-атрибуту
            'div.offer-bottombar__item p',  # Старый формат
            'p.css-7wnksb',  # Прямой поиск города по классу
            'p.css-*',  # Любой параграф с классом, начинающимся с css-
        ]

        logger.debug("Пробуем использовать запасные селекторы для поиска местоположения")
        for selector in selectors:
            try:
                location_element = soup.select_one(selector)
                if location_element:
                    location_text = clean_text(location_element.text)
                    logger.debug(f"Найден элемент по селектору '{selector}': {location_text}")

                    # Удаляем дату из текста (часто встречается в формате "Киев, Дарницкий район - Сегодня 12:30")
                    location_text = re.sub(r'\s*-\s*\d{1,2}:\d{2}$', '', location_text)
                    location_text = re.sub(r'\s*-\s*\d{1,2}\s+\w+\s+\d{1,2}:\d{2}$', '', location_text)
                    location_text = re.sub(r'\s*-\s*\w+\s+\d{1,2}:\d{2}$', '', location_text)

                    # Если текст содержит город, возвращаем результат
                    if re.search(r'\b(Киев|Харьков|Одесса|Днепр|Львов|Чернигов)\b', location_text, re.IGNORECASE):
                        logger.debug(f"Найден город в тексте: {location_text}")
                        # Используем новый метод для более точного извлечения информации
                        return LocationUtils.extract_location_from_text(location_text)
            except Exception as e:
                logger.debug(f"Ошибка при обработке селектора '{selector}': {e}")
                continue

        # Если не нашли по селекторам, пробуем найти через мета-теги
        try:
            from src.infrastructure.parsers.olx.utils.html_utils import extract_meta_property
            logger.debug("Пробуем извлечь местоположение из мета-тегов")
            meta_location = extract_meta_property(soup, 'og:locality')
            if meta_location:
                logger.debug(f"Найдено местоположение в мета-тегах: {meta_location}")
                # Используем новый метод для более точного извлечения информации
                return LocationUtils.extract_location_from_text(meta_location)
        except Exception as e:
            logger.error(f"Ошибка при извлечении местоположения из мета-тегов: {e}")

        # Пробуем извлечь информацию о местоположении из описания
        try:
            logger.debug("Пробуем извлечь местоположение из описания")
            description_element = soup.select_one('div[data-cy="ad_description"] div')
            if description_element and description_element.text:
                description = description_element.text.strip()
                logger.debug(f"Найдено описание: {description[:100]}...")

                # Ищем упоминания района в описании
                district_patterns = [
                    r'район[\s:]+(\w+(?:[\s-]\w+)*)',  # район Название
                    r'в районе[\s:]+(\w+(?:[\s-]\w+)*)',  # в районе Название
                    r'район[\s:]+(\w+(?:[\s-]\w+)*)',  # район Название
                    r'р-н[\s:]+(\w+(?:[\s-]\w+)*)',  # р-н Название
                    r'район (\w+(?:[\s-]\w+)*)',  # район Название
                    r'район\s+([\w\s\-\'\"\(\)]+)\s',  # район Название с возможными скобками и кавычками
                    r'мікрорайон\s+([\w\s\-\'\"\(\)]+)\s',  # мікрорайон Название
                    r'микрорайон\s+([\w\s\-\'\"\(\)]+)\s',  # микрорайон Название
                    r'мкр\.?\s+([\w\s\-\'\"\(\)]+)\s',  # мкр. Название
                ]

                # Ищем упоминания улиц в описании
                street_patterns = [
                    r'ул(?:ица|\.)?\s+([\w\s\-\'\"\(\)]+)\s',  # улица/ул. Название
                    r'вул(?:иця|\.)?\s+([\w\s\-\'\"\(\)]+)\s',  # вулиця/вул. Название
                    r'пр(?:оспект|\.)?\s+([\w\s\-\'\"\(\)]+)\s',  # проспект/пр. Название
                    r'бульв(?:ар|\.)?\s+([\w\s\-\'\"\(\)]+)\s',  # бульвар/бульв. Название
                ]

                # Ищем упоминания конкретных мест в описании
                location_patterns = [
                    r'возле\s+([\w\s\-\'\"\(\)]+)\s',  # возле Место
                    r'близ\s+([\w\s\-\'\"\(\)]+)\s',  # близ Место
                    r'рядом\s+с\s+([\w\s\-\'\"\(\)]+)\s',  # рядом с Место
                    r'недалеко\s+от\s+([\w\s\-\'\"\(\)]+)\s',  # недалеко от Место
                    r'в районе\s+([\w\s\-\'\"\(\)]+)\s',  # в районе Место
                    r'возле\s+([\w\s\-\'\"\(\)]+)',  # возле Место (без пробела в конце)
                    r'близ\s+([\w\s\-\'\"\(\)]+)',  # близ Место (без пробела в конце)
                    r'район\s+([\w\s\-\'\"\(\)]+)',  # район Место
                    r'возле\s+(\d+)\s+школы',  # возле 4 школы
                    r'возле\s+школы\s+(\d+)',  # возле школы 4
                    r'возле\s+школы\s+№\s*(\d+)',  # возле школы № 4
                    r'(\d+)\s+школы',  # 4 школы
                    r'Болденой\s+горы',  # Болденой горы
                    r'Болдиной\s+горы',  # Болдиной горы (вариант написания)
                ]

                # Сначала ищем район
                for pattern in district_patterns:
                    district_match = re.search(pattern, description, re.IGNORECASE)
                    if district_match:
                        district = district_match.group(1).strip()
                        logger.debug(f"Найден район в описании: {district}")

                        # Пробуем найти город в блоке местоположения (новая структура)
                        city = LocationUtils._extract_city_from_location_blocks(soup)
                        if city:
                            logger.debug(f"Извлечен город из блока местоположения: {city}")
                            # Возвращаем только город в full_address
                            return {'city': city, 'district': district, 'full_address': city}

                        # Если не нашли город, используем значение по умолчанию
                        return {'city': 'Не указан', 'district': district, 'full_address': f'Не указан, {district}'}

                # Затем ищем улицы
                for pattern in street_patterns:
                    street_match = re.search(pattern, description, re.IGNORECASE)
                    if street_match:
                        street = street_match.group(1).strip()
                        logger.debug(f"Найдена улица в описании: {street}")

                        # Пробуем найти город в блоке местоположения (новая структура)
                        city = LocationUtils._extract_city_from_location_blocks(soup)
                        if city:
                            logger.debug(f"Извлечен город из блока местоположения: {city}")
                            # Возвращаем только город в full_address
                            return {'city': city, 'street': street, 'full_address': city}

                        # Если не нашли город, используем значение по умолчанию
                        return {'city': 'Не указан', 'street': street, 'full_address': f'Не указан, ул. {street}'}

                # Если район и улица не найдены, ищем конкретные места
                for pattern in location_patterns:
                    location_match = re.search(pattern, description, re.IGNORECASE)
                    if location_match:
                        # Проверяем специальные шаблоны без групп захвата
                        if pattern == r'Болденой\s+горы' or pattern == r'Болдиной\s+горы':
                            location_detail = 'Болденая гора'
                            logger.debug(f"Найдено конкретное место в описании: {location_detail}")

                            # Пробуем найти город в блоке местоположения (новая структура)
                            city = LocationUtils._extract_city_from_location_blocks(soup)
                            if city:
                                logger.debug(f"Извлечен город из блока местоположения: {city}")
                                return {'city': city, 'district': location_detail, 'full_address': f'{city}, {location_detail}'}

                            # Если не нашли город, используем значение по умолчанию
                            return {'city': 'Не указан', 'district': location_detail, 'full_address': f'Не указан, {location_detail}'}

                        # Для шаблонов с группами захвата
                        try:
                            location_detail = location_match.group(1).strip()

                            # Специальная обработка для школы
                            if pattern.find('школы') != -1 and location_detail.isdigit():
                                location_detail = f'Школа №{location_detail}'

                            logger.debug(f"Найдено конкретное место в описании: {location_detail}")

                            # Пробуем найти город в блоке местоположения (новая структура)
                            city = LocationUtils._extract_city_from_location_blocks(soup)
                            if city:
                                logger.debug(f"Извлечен город из блока местоположения: {city}")
                                return {'city': city, 'district': location_detail, 'full_address': f'{city}, {location_detail}'}

                            # Если не нашли город, используем значение по умолчанию
                            return {'city': 'Не указан', 'district': location_detail, 'full_address': f'Не указан, {location_detail}'}
                        except (IndexError, AttributeError):
                            # Если нет группы захвата, продолжаем поиск
                            continue
        except Exception as e:
            logger.error(f"Ошибка при извлечении местоположения из описания: {e}")

        # Если все методы не сработали, пробуем найти город в блоке местоположения (новая и старая структура)
        try:
            city = LocationUtils._extract_city_from_location_blocks(soup)
            if city:
                logger.debug(f"Извлечен город из блока местоположения: {city}")

                # Пробуем найти регион в новой структуре
                try:
                    location_container = soup.select_one('div.css-1dp6pbg')
                    if location_container:
                        location_block = location_container.select_one('div.css-17dk4rn')
                        if location_block:
                            region_element = location_block.select_one('p.css-z0m36u')
                            if region_element:
                                region = clean_text(region_element.text)
                                logger.debug(f"Извлечен регион из новой структуры: {region}, но не добавляем его в адрес")
                                return {'city': city, 'region': region, 'full_address': city}
                except Exception:
                    pass

                # Пробуем найти регион в старой структуре
                try:
                    location_block = soup.select_one('div[data-testid="map-aside-section"]')
                    if location_block:
                        region_element = location_block.select_one('p.css-z0m36u')
                        if region_element:
                            region = clean_text(region_element.text)
                            logger.debug(f"Извлечен регион из старой структуры: {region}, но не добавляем его в адрес")
                            return {'city': city, 'region': region, 'full_address': city}
                except Exception:
                    pass

                return {'city': city, 'full_address': city}
        except Exception as e:
            logger.error(f"Ошибка при извлечении местоположения из блока: {e}")

        # Если все методы не сработали, возвращаем заглушку с пустым городом
        logger.warning("Не удалось извлечь местоположение, используем значение по умолчанию: Не указан")
        return {'city': 'Не указан', 'full_address': 'Не указан'}

    @staticmethod
    def extract_coordinates_from_soup(soup: BeautifulSoup) -> Tuple[Optional[float], Optional[float]]:
        """
        Извлекает координаты из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Tuple[Optional[float], Optional[float]]: Широта и долгота или (None, None), если координаты не найдены
        """
        try:
            logger.debug("Поиск координат в скриптах страницы")
            # Ищем скрипт с координатами
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and 'window.__PRERENDERED_STATE__' in script.string:
                    logger.debug("Найден скрипт с предварительно отрендеренным состоянием")
                    # Ищем координаты в скрипте
                    lat_match = re.search(r'"lat":\s*([\d.]+)', script.string)
                    lng_match = re.search(r'"lon":\s*([\d.]+)', script.string)

                    if lat_match and lng_match:
                        try:
                            lat = float(lat_match.group(1))
                            lng = float(lng_match.group(1))
                            logger.debug(f"Извлечены координаты: широта {lat}, долгота {lng}")
                            return lat, lng
                        except ValueError as ve:
                            logger.error(f"Ошибка при преобразовании координат в числа: {ve}")

        except Exception as e:
            logger.error(f"Ошибка при извлечении координат из объекта BeautifulSoup: {e}")
            # Для совместимости с тестами
            print(f"Ошибка при извлечении координат из объекта BeautifulSoup: {e}")

        logger.debug("Координаты не найдены")
        return None, None

    @staticmethod
    def normalize_city(city: Optional[str]) -> Optional[str]:
        """
        Нормализует название города.

        Args:
            city: Название города

        Returns:
            Optional[str]: Нормализованное название города
        """
        if not city:
            logger.debug("Невозможно нормализовать пустое название города")
            return city

        # Словарь соответствий
        city_map = {
            'киев': 'Киев',
            'харьков': 'Харьков',
            'одесса': 'Одесса',
            'днепр': 'Днепр',
            'запорожье': 'Запорожье',
            'львов': 'Львов',
            'кривой рог': 'Кривой Рог',
            'николаев': 'Николаев',
            'мариуполь': 'Мариуполь',
            'луганск': 'Луганск',
            'винница': 'Винница',
            'макеевка': 'Макеевка',
            'севастополь': 'Севастополь',
            'симферополь': 'Симферополь',
            'херсон': 'Херсон',
            'полтава': 'Полтава',
            'чернигов': 'Чернигов',
            'черкассы': 'Черкассы',
            'житомир': 'Житомир',
            'сумы': 'Сумы'
        }

        normalized_city = city_map.get(city.lower(), city)
        if normalized_city != city:
            logger.debug(f"Нормализовано название города: '{city}' -> '{normalized_city}'")

        return normalized_city

    @staticmethod
    def normalize_district(district: Optional[str]) -> Optional[str]:
        """
        Нормализует название района.

        Args:
            district: Название района

        Returns:
            Optional[str]: Нормализованное название района
        """
        if not district:
            logger.debug("Невозможно нормализовать пустое название района")
            return district

        original_district = district

        # Удаляем слово "район" и лишние пробелы
        district = re.sub(r'\s+район\s*$', '', district, flags=re.IGNORECASE)
        district = district.strip()

        if district != original_district:
            logger.debug(f"Нормализовано название района: '{original_district}' -> '{district}'")

        return district

    @staticmethod
    def format_location(location: Dict[str, str]) -> str:
        """
        Форматирует информацию о местоположении.

        Args:
            location: Словарь с информацией о местоположении

        Returns:
            str: Отформатированная информация о местоположении
        """
        # Если есть готовый адрес, используем его
        if 'full_address' in location and location['full_address']:
            logger.debug(f"Используем готовый адрес: {location['full_address']}")
            return location['full_address']

        # Иначе возвращаем только город, без области
        if 'city' in location and location['city']:
            logger.debug(f"Возвращаем только город: {location['city']}")
            return location['city']

        # Если нет города, возвращаем универсальное значение "---"
        logger.debug("Город не указан, возвращаем универсальное значение '---'")
        return "---"

    @staticmethod
    def get_nearby_cities(city: str, radius: int = 50) -> List[str]:
        """
        Возвращает список ближайших городов.

        Args:
            city: Название города
            radius: Радиус поиска в километрах (в текущей реализации не используется)

        Returns:
            List[str]: Список ближайших городов
        """
        # Словарь с ближайшими городами
        nearby_cities = {
            'Киев': ['Бровары', 'Борисполь', 'Вишневое', 'Ирпень', 'Буча', 'Вышгород', 'Обухов', 'Украинка', 'Васильков'],
            'Харьков': ['Дергачи', 'Чугуев', 'Люботин', 'Мерефа', 'Пивденное', 'Богодухов'],
            'Одесса': ['Черноморск', 'Южный', 'Теплодар', 'Беляевка', 'Овидиополь'],
            'Днепр': ['Каменское', 'Новомосковск', 'Подгородное', 'Синельниково'],
            'Запорожье': ['Вольнянск', 'Днепрорудное', 'Васильевка'],
            'Львов': ['Городок', 'Пустомыты', 'Дубляны', 'Жолква', 'Новояворовск'],
            'Чернигов': ['Славутич', 'Нежин', 'Прилуки', 'Бахмач', 'Козелец', 'Сновск']
        }

        # Нормализуем название города
        normalized_city = LocationUtils.normalize_city(city)

        # Получаем список ближайших городов
        if normalized_city is None:
            logger.debug(f"Не удалось нормализовать название города: {city}")
            return []

        result = nearby_cities.get(normalized_city, [])

        # В будущем можно добавить фильтрацию по радиусу
        # Для этого потребуется база данных с координатами городов
        # Здесь можно будет использовать параметр radius
        _ = radius  # Используем параметр, чтобы избежать предупреждения

        if result:
            logger.debug(f"Найдено {len(result)} ближайших городов для {normalized_city}")
        else:
            logger.debug(f"Не найдено ближайших городов для {city}")

        return result
