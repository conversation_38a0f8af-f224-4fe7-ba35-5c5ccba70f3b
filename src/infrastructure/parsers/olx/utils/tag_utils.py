"""
Обработка тегов OLX.
"""
import re
import logging
from typing import Dict, List, Optional

from bs4 import BeautifulSoup, Tag

from src.infrastructure.parsers.olx.utils.html_utils import clean_text, get_parameters
from src.infrastructure.parsers.olx.utils.error_handling import safe_extraction
from src.infrastructure.parsers.config.tag_mapping import TAG_TO_FIELD_MAPPING


class TagExtractor:
    """
    Класс для извлечения тегов из HTML-страницы объявления.
    """
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    @safe_extraction(default_value=[])
    def extract_tags(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Извлекает теги из HTML-страницы объявления.

        Args:
            soup: BeautifulSoup объект с HTML-страницей

        Returns:
            List[Dict[str, str]]: Список уникальных тегов в формате [{"key": "название", "value": "значение"}]
        """
        self.logger.info("[HTML Tags] ================== НАЧАЛО ИЗВЛЕЧЕНИЯ ТЕГОВ ==================")
        tags = []
        seen_tags = set()

        # Ищем блок по новому селектору data-testid="ad-parameters-container"
        details_block = soup.find('div', {'data-testid': 'ad-parameters-container'})

        if not details_block:
            self.logger.info("[HTML Tags] Пробуем найти блок по data-cy")
            details_block = soup.find('div', {'data-cy': 'ad-parameters-container'})

        if not details_block:
            self.logger.info("[HTML Tags] Пробуем найти блок по классу css-41yf00")
            details_block = soup.find('div', class_='css-41yf00')

        if not details_block:
            self.logger.info("[HTML Tags] Пробуем найти блок по старому селектору data-cy=ad_parameters")
            details_block = soup.find('div', {'data-cy': 'ad_parameters'})

        if not details_block:
            self.logger.warning("[HTML Tags] Блок параметров не найден")
            return []

        # Ищем все параметры внутри блока - сначала ищем параграфы
        parameter_items = details_block.find_all('p', recursive=True)
        self.logger.info(f"[HTML Tags] Найдено {len(parameter_items)} параграфов с параметрами")

        # Если параграфов нет, ищем любые элементы с текстом
        if not parameter_items:
            parameter_items = details_block.find_all(['div', 'p', 'span'], recursive=True)
            self.logger.info(f"[HTML Tags] Найдено {len(parameter_items)} элементов с параметрами")

        for item in parameter_items:
            try:
                text = item.get_text(strip=True)
                if not text:
                    continue

                # Проверяем разные форматы разделителей
                if ':' in text:
                    label, value = text.split(':', 1)
                    label = label.strip()
                    value = value.strip()
                    tag_key = f"{label}:{value}"

                    if label and value and tag_key not in seen_tags:
                        seen_tags.add(tag_key)
                        tags.append({
                            'key': label,
                            'value': value
                        })
                elif ': ' in text:
                    label, value = text.split(': ', 1)
                    label = label.strip()
                    value = value.strip()
                    tag_key = f"{label}:{value}"

                    if label and value and tag_key not in seen_tags:
                        seen_tags.add(tag_key)
                        tags.append({
                            'key': label,
                            'value': value
                        })
            except Exception as e:
                self.logger.debug(f"Ошибка при обработке параметра: {str(e)}")

        self.logger.info(f"[HTML Tags] Извлечено {len(tags)} уникальных тегов")
        return tags

    @safe_extraction(default_value={})
    def extract_mapped_tags(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Извлекает теги и преобразует их в соответствии с маппингом.

        Args:
            soup: BeautifulSoup объект с HTML-страницей

        Returns:
            Dict[str, str]: Словарь с маппингом тегов на поля объекта Property
        """
        tags = self.extract_tags(soup)
        mapped_tags = {}

        for tag in tags:
            key = tag['key']
            value = tag['value']

            # Проверяем, есть ли ключ в маппинге
            if key in TAG_TO_FIELD_MAPPING:
                field_name = TAG_TO_FIELD_MAPPING[key]
                mapped_tags[field_name] = value
                self.logger.debug(f"[HTML Tags] Маппинг тега: {key} -> {field_name} = {value}")

        return mapped_tags


class TagUtils:
    """
    Класс для обработки тегов OLX.
    """

    @staticmethod
    @safe_extraction(default_value=[])
    def extract_tags_from_soup(soup: BeautifulSoup) -> List[str]:
        """
        Извлекает теги из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            List[str]: Список тегов
        """
        logger = logging.getLogger(__name__)
        tags = []

        # Ищем теги в кнопках параметров
        tag_buttons = soup.select('button.css-1cju8pu')
        logger.debug(f"Найдено {len(tag_buttons)} кнопок с параметрами")

        for button in tag_buttons:
            button_text = clean_text(button.text)
            logger.debug(f"Текст кнопки: {button_text}")

            # Проверяем на наличие двоеточия
            if ':' in button_text:
                key, value = button_text.split(':', 1)
                key = key.strip()
                value = value.strip()

                # Добавляем ключ и значение как теги
                if key and key not in tags:
                    tags.append(key)
                    logger.debug(f"Добавлен тег из ключа: {key}")

                if value and value not in tags:
                    tags.append(value)
                    logger.debug(f"Добавлен тег из значения: {value}")
            # Проверяем на наличие пробела
            elif ' ' in button_text:
                parts = button_text.split(' ', 1)
                if len(parts) == 2:
                    key, value = parts
                    key = key.strip()
                    value = value.strip()

                    # Добавляем ключ и значение как теги
                    if key and key not in tags:
                        tags.append(key)
                        logger.debug(f"Добавлен тег из ключа: {key}")

                    if value and value not in tags:
                        tags.append(value)
                        logger.debug(f"Добавлен тег из значения: {value}")
            # Если нет разделителя, добавляем весь текст как тег
            else:
                if button_text and button_text not in tags:
                    tags.append(button_text)
                    logger.debug(f"Добавлен тег из кнопки: {button_text}")

        # Если не нашли теги в кнопках, пробуем старые селекторы
        if not tags:
            # Ищем теги в параметрах объявления
            params_elements = soup.select('div.css-1epmoz1 p')
            logger.debug(f"Найдено {len(params_elements)} элементов параметров")

            # Если не нашли параметры по основному селектору, пробуем альтернативный
            if not params_elements:
                params_elements = soup.select('div[data-cy="ad_parameters"] p')
                logger.debug(f"Найдено {len(params_elements)} элементов параметров по альтернативному селектору")

            for param_element in params_elements:
                param_text = clean_text(param_element.text)
                if ':' in param_text:
                    key, value = param_text.split(':', 1)
                    key = key.strip()
                    value = value.strip()

                    # Добавляем ключ и значение как теги
                    if key and key not in tags:
                        tags.append(key)
                        logger.debug(f"Добавлен тег из ключа: {key}")

                    if value and value not in tags:
                        tags.append(value)
                        logger.debug(f"Добавлен тег из значения: {value}")

        # Ищем теги в заголовке
        title_element = soup.select_one('h1.css-1soizd2')
        if title_element:
            title_text = clean_text(title_element.text)
            logger.debug(f"Найден заголовок: {title_text}")
            title_words = title_text.split()
            for word in title_words:
                if len(word) > 3 and word not in tags:
                    tags.append(word)
                    logger.debug(f"Добавлен тег из заголовка: {word}")

            # Ищем количество комнат в заголовке
            import re
            room_match = re.search(r'(\d+)[- ](\u043a[\u043e\u0456]\u043c\u043d\u0430\u0442)', title_text.lower())
            if room_match:
                rooms_count = room_match.group(1)
                logger.debug(f"Найдено количество комнат в заголовке: {rooms_count}")
                tags.append(f"Количество комнат: {rooms_count}")

            # Ищем тип дома в заголовке
            if 'новостро' in title_text.lower() or 'новобуд' in title_text.lower():
                logger.debug(f"Найден тип дома в заголовке: Новострой")
                tags.append("Тип дома: Новострой")

        logger.debug(f"Всего извлечено {len(tags)} тегов")
        return tags

    @staticmethod
    @safe_extraction(default_value={})
    def extract_property_tags(soup: BeautifulSoup) -> Dict[str, str]:
        """
        Извлекает теги недвижимости из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Dict[str, str]: Словарь с тегами недвижимости
        """
        logger = logging.getLogger(__name__)
        tags = {}

        # Извлекаем параметры с помощью функции get_parameters
        params = get_parameters(soup)
        logger.debug(f"Извлечено {len(params)} параметров объявления")

        # Логируем все параметры для отладки
        for key, value in params.items():
            logger.debug(f"Параметр: {key} = {value}")

        # Используем маппинг для преобразования ключей
        for key, value in params.items():
            if key in TAG_TO_FIELD_MAPPING:
                field_name = TAG_TO_FIELD_MAPPING[key]
                tags[field_name] = value
                logger.debug(f"Маппинг параметра: {key} -> {field_name} = {value}")

        # Дополнительная обработка для обратной совместимости
        # Тип недвижимости
        if 'Тип недвижимости' in params and 'property_type' not in tags:
            tags['property_type'] = params['Тип недвижимости']

        # Тип операции
        if 'Тип операции' in params and 'operation_type' not in tags:
            tags['operation_type'] = params['Тип операции']

        # Тип строения / Тип дома
        if 'Тип строения' in params and 'building_type' not in tags:
            tags['building_type'] = params['Тип строения']
        elif 'Тип дома' in params and 'building_type' not in tags:
            tags['building_type'] = params['Тип дома']
        elif 'Тип будинку' in params and 'building_type' not in tags:
            tags['building_type'] = params['Тип будинку']
        # Тип стен как тип дома
        elif 'Тип стен' in params and 'building_type' not in tags:
            tags['building_type'] = params['Тип стен']
        elif 'Тип стін' in params and 'building_type' not in tags:
            tags['building_type'] = params['Тип стін']

        # Количество комнат
        if 'Количество комнат' in params and 'rooms' not in tags:
            # Извлекаем число из строки вида "2 комнаты"
            room_value = params['Количество комнат']
            room_match = re.search(r'(\d+)', room_value)
            if room_match:
                tags['rooms'] = room_match.group(1)
                logger.debug(f"Извлечено количество комнат из строки: {room_value} -> {room_match.group(1)}")
            else:
                tags['rooms'] = room_value
                logger.debug(f"Не удалось извлечь число из строки: {room_value}")
        elif 'Кількість кімнат' in params and 'rooms' not in tags:
            # Извлекаем число из строки вида "2 кімнати"
            room_value = params['Кількість кімнат']
            room_match = re.search(r'(\d+)', room_value)
            if room_match:
                tags['rooms'] = room_match.group(1)
                logger.debug(f"Извлечено количество комнат из строки: {room_value} -> {room_match.group(1)}")
            else:
                tags['rooms'] = room_value
                logger.debug(f"Не удалось извлечь число из строки: {room_value}")

        # Этаж
        if 'Этаж' in params and 'floor' not in tags:
            floor_value = params['Этаж']
            floor_match = re.search(r'(\d+)', floor_value)
            if floor_match:
                tags['floor'] = floor_match.group(1)
                logger.debug(f"Извлечен этаж из строки: {floor_value} -> {floor_match.group(1)}")
            else:
                tags['floor'] = floor_value
                logger.debug(f"Не удалось извлечь число из строки: {floor_value}")
        elif 'Поверх' in params and 'floor' not in tags:
            floor_value = params['Поверх']
            floor_match = re.search(r'(\d+)', floor_value)
            if floor_match:
                tags['floor'] = floor_match.group(1)
                logger.debug(f"Извлечен этаж из строки: {floor_value} -> {floor_match.group(1)}")
            else:
                tags['floor'] = floor_value
                logger.debug(f"Не удалось извлечь число из строки: {floor_value}")

        # Этажность
        if 'Этажность' in params and 'total_floors' not in tags:
            floors_value = params['Этажность']
            floors_match = re.search(r'(\d+)', floors_value)
            if floors_match:
                tags['total_floors'] = floors_match.group(1)
                logger.debug(f"Извлечена этажность из строки: {floors_value} -> {floors_match.group(1)}")
            else:
                tags['total_floors'] = floors_value
                logger.debug(f"Не удалось извлечь число из строки: {floors_value}")
        elif 'Поверховість' in params and 'total_floors' not in tags:
            floors_value = params['Поверховість']
            floors_match = re.search(r'(\d+)', floors_value)
            if floors_match:
                tags['total_floors'] = floors_match.group(1)
                logger.debug(f"Извлечена этажность из строки: {floors_value} -> {floors_match.group(1)}")
            else:
                tags['total_floors'] = floors_value
                logger.debug(f"Не удалось извлечь число из строки: {floors_value}")

        # Общая площадь
        if 'Общая площадь' in params and 'total_area' not in tags:
            area_value = params['Общая площадь']
            area_match = re.search(r'(\d+(?:[.,]\d+)?)', area_value)
            if area_match:
                tags['total_area'] = area_match.group(1).replace(',', '.')
                logger.debug(f"Извлечена общая площадь из строки: {area_value} -> {area_match.group(1)}")
            else:
                tags['total_area'] = area_value
                logger.debug(f"Не удалось извлечь число из строки: {area_value}")
        elif 'Загальна площа' in params and 'total_area' not in tags:
            area_value = params['Загальна площа']
            area_match = re.search(r'(\d+(?:[.,]\d+)?)', area_value)
            if area_match:
                tags['total_area'] = area_match.group(1).replace(',', '.')
                logger.debug(f"Извлечена общая площадь из строки: {area_value} -> {area_match.group(1)}")
            else:
                tags['total_area'] = area_value
                logger.debug(f"Не удалось извлечь число из строки: {area_value}")

        # Площадь кухни
        if 'Площадь кухни' in params and 'kitchen_area' not in tags:
            area_value = params['Площадь кухни']
            area_match = re.search(r'(\d+(?:[.,]\d+)?)', area_value)
            if area_match:
                tags['kitchen_area'] = area_match.group(1).replace(',', '.')
                logger.debug(f"Извлечена площадь кухни из строки: {area_value} -> {area_match.group(1)}")
            else:
                tags['kitchen_area'] = area_value
                logger.debug(f"Не удалось извлечь число из строки: {area_value}")
        elif 'Площа кухні' in params and 'kitchen_area' not in tags:
            area_value = params['Площа кухні']
            area_match = re.search(r'(\d+(?:[.,]\d+)?)', area_value)
            if area_match:
                tags['kitchen_area'] = area_match.group(1).replace(',', '.')
                logger.debug(f"Извлечена площадь кухни из строки: {area_value} -> {area_match.group(1)}")
            else:
                tags['kitchen_area'] = area_value
                logger.debug(f"Не удалось извлечь число из строки: {area_value}")

        logger.debug(f"Извлечено {len(tags)} тегов недвижимости")
        return tags

    @staticmethod
    @safe_extraction(default_value={})
    def extract_meta_tags(soup: BeautifulSoup) -> Dict[str, str]:
        """
        Извлекает мета-теги из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Dict[str, str]: Словарь с мета-тегами
        """
        logger = logging.getLogger(__name__)
        meta_tags = {}

        # Извлекаем мета-теги
        meta_elements = soup.find_all('meta')
        logger.debug(f"Найдено {len(meta_elements)} meta-элементов")

        important_meta_tags = ['og:title', 'og:description', 'og:url', 'og:image', 'og:type',
                             'og:site_name', 'og:locale', 'og:price:amount', 'og:price:currency']

        for meta_element in meta_elements:
            name = meta_element.get('name') or meta_element.get('property')
            content = meta_element.get('content')

            if name and content:
                meta_tags[name] = content
                if name in important_meta_tags:
                    logger.debug(f"Извлечен важный meta-тег: {name} = {content}")

        logger.debug(f"Извлечено {len(meta_tags)} meta-тегов")
        return meta_tags

    @staticmethod
    @safe_extraction(default_value=None)
    def find_element_by_text(soup: BeautifulSoup, text: str, tag: Optional[str] = None) -> Optional[Tag]:
        """
        Ищет элемент по тексту.

        Args:
            soup: Объект BeautifulSoup
            text: Текст для поиска
            tag: Тег для поиска (опционально)

        Returns:
            Optional[Tag]: Найденный элемент или None, если элемент не найден
        """
        logger = logging.getLogger(__name__)
        logger.debug(f"Поиск элемента с текстом '{text}' в теге '{tag if tag else 'any'}'")

        # Компилируем регулярное выражение для поиска
        pattern = re.compile(text, re.IGNORECASE)

        # Ищем элемент
        if tag:
            elements = soup.find_all(tag)
        else:
            elements = soup.find_all()

        logger.debug(f"Найдено {len(elements)} элементов для поиска")

        for element in elements:
            if element.string and pattern.search(element.string):
                logger.debug(f"Найден элемент {element.name} с текстом '{element.string}'")
                return element

        logger.debug(f"Элемент с текстом '{text}' не найден")
        return None

    @staticmethod
    @safe_extraction(default_value=[])
    def find_elements_by_class(soup: BeautifulSoup, class_name: str) -> List[Tag]:
        """
        Ищет элементы по классу.

        Args:
            soup: Объект BeautifulSoup
            class_name: Имя класса

        Returns:
            List[Tag]: Список найденных элементов
        """
        logger = logging.getLogger(__name__)
        logger.debug(f"Поиск элементов по классу '{class_name}'")
        elements = soup.find_all(class_=class_name)
        logger.debug(f"Найдено {len(elements)} элементов с классом '{class_name}'")
        return elements

    @staticmethod
    @safe_extraction(default_value=None)
    def find_element_by_id(soup: BeautifulSoup, id_name: str) -> Optional[Tag]:
        """
        Ищет элемент по ID.

        Args:
            soup: Объект BeautifulSoup
            id_name: Имя ID

        Returns:
            Optional[Tag]: Найденный элемент или None, если элемент не найден
        """
        logger = logging.getLogger(__name__)
        logger.debug(f"Поиск элемента по ID '{id_name}'")
        element = soup.find(id=id_name)
        if element:
            logger.debug(f"Найден элемент {element.name} с ID '{id_name}'")
        else:
            logger.debug(f"Элемент с ID '{id_name}' не найден")
        return element
