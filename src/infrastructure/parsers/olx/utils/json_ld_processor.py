"""
Обработка JSON-LD OLX.
"""
import json
import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from bs4 import BeautifulSoup

from src.domain.entities.property import AdId
from src.infrastructure.parsers.olx.utils.date_utils import DateUtils


class JsonLdProcessor:
    """
    Класс для обработки JSON-LD данных OLX.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Инициализирует процессор JSON-LD.

        Args:
            logger: Логгер (опционально)
        """
        self.logger = logger or logging.getLogger(__name__)

    def extract_json_ld(self, html: str) -> List[Dict[str, Any]]:
        """
        Извлекает JSON-LD данные из HTML.

        Args:
            html: HTML строка

        Returns:
            List[Dict[str, Any]]: Список JSON-LD объектов
        """
        result = []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            json_ld_scripts = soup.find_all('script', type='application/ld+json')

            for script in json_ld_scripts:
                try:
                    data = json.loads(script.string)
                    result.append(data)
                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.warning(f"Ошибка при парсинге JSON-LD: {e}")

        except Exception as e:
            self.logger.error(f"Ошибка при извлечении JSON-LD: {e}")

        return result

    def extract_property_data(self, json_ld_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Извлекает данные о недвижимости из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Optional[Dict[str, Any]]: Данные о недвижимости или None, если данные не найдены
        """
        for data in json_ld_data:
            # Проверяем, что это объект недвижимости
            if data.get('@type') in ['Apartment', 'House', 'SingleFamilyResidence', 'Product']:
                return data

        return None

    def extract_price(self, json_ld_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Извлекает данные о цене из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Optional[Dict[str, Any]]: Данные о цене или None, если данные не найдены
        """
        for data in json_ld_data:
            # Проверяем наличие данных о цене
            if 'offers' in data:
                offers = data['offers']
                self.logger.debug(f"Найдены offers в JSON-LD: {json.dumps(offers, ensure_ascii=False)}")

                # Проверяем что offers это словарь
                if not isinstance(offers, dict):
                    # Если это список, берем первый элемент
                    if isinstance(offers, list):
                        self.logger.debug("Найден список offers, берем первый элемент")
                        if not offers:
                            self.logger.warning("Список offers пуст")
                            continue
                        offers = offers[0]
                        if not isinstance(offers, dict):
                            self.logger.warning(f"Некорректный формат offers[0]: {type(offers)}")
                            continue
                    else:
                        self.logger.warning(f"Некорректный формат offers: {type(offers)}")
                        continue

                if 'price' not in offers:
                    self.logger.debug("Нет поля 'price' в offers")
                    continue

                try:
                    price_raw = offers['price']
                    self.logger.debug(f"Найдена цена в JSON-LD (тип {type(price_raw)}): {price_raw}")

                    # Если это число, сразу преобразуем в float
                    if isinstance(price_raw, (int, float)):
                        price = float(price_raw)
                    else:
                        # Если это строка, очищаем от всех символов кроме цифр и точки
                        price_str = ''.join(c for c in str(price_raw) if c.isdigit() or c in '.,')
                        price = float(price_str.replace(',', '.'))

                    # Получаем валюту
                    currency = "UAH"  # Значение по умолчанию
                    if 'priceCurrency' in offers:
                        currency_raw = offers['priceCurrency']
                        if isinstance(currency_raw, str):
                            currency = currency_raw.upper()
                        else:
                            self.logger.warning(f"Некорректный формат валюты: {type(currency_raw)}")
                    else:
                        self.logger.debug("Нет поля 'priceCurrency' в offers, используем UAH")

                    self.logger.debug(f"Извлечена цена из JSON-LD: {price} {currency}")

                    return {
                        'price': price,
                        'currency': currency
                    }

                except (ValueError, TypeError) as e:
                    self.logger.error(f"Ошибка при извлечении цены из JSON-LD: {str(e)}")

        return None

    def extract_location(self, json_ld_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Извлекает данные о местоположении из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Optional[Dict[str, Any]]: Данные о местоположении или None, если данные не найдены
        """
        for data in json_ld_data:
            # Проверяем наличие данных о местоположении
            if 'address' in data and isinstance(data['address'], dict):
                address = data['address']
                result = {}

                if '@type' in address:
                    result['type'] = address['@type']

                if 'addressLocality' in address:
                    result['locality'] = address['addressLocality']

                if 'addressRegion' in address:
                    result['region'] = address['addressRegion']

                if 'postalCode' in address:
                    result['postal_code'] = address['postalCode']

                if 'streetAddress' in address:
                    result['street'] = address['streetAddress']

                return result

        return None

    def extract_images(self, json_ld_data: List[Dict[str, Any]]) -> List[str]:
        """
        Извлекает URL изображений из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            List[str]: Список URL изображений
        """
        all_images = []

        for data in json_ld_data:
            # Проверяем наличие изображений
            if 'image' in data:
                images = data['image']
                if isinstance(images, list):
                    # Добавляем все изображения из списка
                    for img in images:
                        if isinstance(img, str) and img not in all_images:
                            all_images.append(img)
                elif isinstance(images, str) and images not in all_images:
                    # Добавляем одиночное изображение
                    all_images.append(images)

        # Если нашли изображения, логируем их количество
        if all_images:
            self.logger.debug(f"Извлечено {len(all_images)} изображений из JSON-LD")

        return all_images

    def extract_description(self, json_ld_data: List[Dict[str, Any]]) -> Optional[str]:
        """
        Извлекает описание из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Optional[str]: Описание или None, если описание не найдено
        """
        for data in json_ld_data:
            # Проверяем наличие описания
            if 'description' in data and isinstance(data['description'], str):
                return data['description']

        return None

    def extract_property_details(self, json_ld_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Извлекает детали о недвижимости из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Dict[str, Any]: Детали о недвижимости
        """
        result = {}

        # Извлекаем данные о недвижимости
        property_data = self.extract_property_data(json_ld_data)
        if property_data:
            # Название
            if 'name' in property_data:
                result['title'] = property_data['name']

            # Тип недвижимости
            if '@type' in property_data:
                result['type'] = property_data['@type']

            # Извлекаем ID объявления
            ad_id = self.extract_ad_id(property_data)
            if ad_id:
                result['id'] = ad_id

            # Определяем тип недвижимости и операции
            self._determine_property_and_operation_type(property_data, result)

        # Извлекаем данные о цене
        price_data = self.extract_price(json_ld_data)
        if price_data:
            result['price'] = price_data

        # Извлекаем данные о местоположении
        location_data = self.extract_location(json_ld_data)
        if location_data:
            result['location'] = location_data

        # Извлекаем изображения
        images = self.extract_images(json_ld_data)
        if images:
            result['images'] = images

        # Извлекаем описание
        description = self.extract_description(json_ld_data)
        if description:
            result['description'] = description

        # Извлекаем дату публикации
        published_date = self.extract_published_date(json_ld_data)
        if published_date:
            result['published_at'] = published_date

        return result

    def extract_ad_id(self, json_ld: Dict[str, Any]) -> Optional[str]:
        """
        Извлекает ID объявления из JSON-LD.

        Args:
            json_ld: JSON-LD объект

        Returns:
            Optional[str]: ID объявления или None, если ID не найден
        """
        # Проверяем разные поля, где может быть ID
        id_fields = ['sku', 'productID', 'identifier', '@id', 'id']

        for field in id_fields:
            if field in json_ld and json_ld[field]:
                # Если это числовой ID
                if isinstance(json_ld[field], (int, str)) and str(json_ld[field]).isdigit():
                    try:
                        # Создаем AdId из JSON-LD
                        ad_id = AdId(str(json_ld[field]))
                        self.logger.debug(f"Создан AdId: {ad_id.value}")
                        return ad_id.value
                    except Exception as e:
                        self.logger.error(f"Ошибка при создании AdId из {field} {json_ld[field]}: {e}")

                # Если это URL или строка с ID
                if isinstance(json_ld[field], str):
                    # Ищем ID в формате "ID: 123456"
                    id_match = re.search(r'ID[:\s]*(\d+)', json_ld[field])
                    if id_match:
                        return id_match.group(1)

                    # Ищем ID в формате "ID123456"
                    id_match = re.search(r'ID(\d+)', json_ld[field])
                    if id_match:
                        return id_match.group(1)

        return None

    def _determine_property_and_operation_type(self, json_ld: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        Определяет тип недвижимости и операции на основе JSON-LD.

        Args:
            json_ld: JSON-LD объект
            result: Словарь для сохранения результатов
        """
        # Определяем тип недвижимости и операции по категории
        if "category" in json_ld:
            category_url = json_ld["category"]
            if isinstance(category_url, str):
                # Определяем тип операции
                if "prodazha" in category_url.lower() or "sale" in category_url.lower():
                    result["operation_type"] = "продажа"
                elif "arenda" in category_url.lower() or "rent" in category_url.lower():
                    result["operation_type"] = "аренда"

                # Определяем тип недвижимости
                if "kvartir" in category_url.lower() or "apartment" in category_url.lower():
                    result["property_type"] = "квартира"
                elif "domov" in category_url.lower() or "house" in category_url.lower():
                    result["property_type"] = "дом"
                elif "komnat" in category_url.lower() or "room" in category_url.lower():
                    result["property_type"] = "комната"
                elif "kommercheskoy" in category_url.lower() or "commercial" in category_url.lower():
                    result["property_type"] = "коммерческая недвижимость"
                elif "zemel" in category_url.lower() or "land" in category_url.lower():
                    result["property_type"] = "земельный участок"

        # Устанавливаем значения по умолчанию, если не удалось определить
        if "property_type" not in result:
            # Проверяем тип объекта в JSON-LD
            if "@type" in json_ld:
                if json_ld["@type"] == "Apartment":
                    result["property_type"] = "квартира"
                elif json_ld["@type"] == "House" or json_ld["@type"] == "SingleFamilyResidence":
                    result["property_type"] = "дом"
                else:
                    result["property_type"] = "недвижимость"
            else:
                result["property_type"] = "недвижимость"

        if "operation_type" not in result:
            result["operation_type"] = "продажа"

    def extract_published_date(self, json_ld_data: List[Dict[str, Any]]) -> Optional[datetime]:
        """
        Извлекает дату публикации из JSON-LD.

        Args:
            json_ld_data: Список JSON-LD объектов

        Returns:
            Optional[str]: Дата публикации или None, если дата не найдена
        """
        # Проверяем все объекты JSON-LD
        for json_ld in json_ld_data:
            # Проверяем все возможные поля с датой
            date_fields = ['dateCreated', 'datePublished', 'dateModified']

            for field in date_fields:
                if field in json_ld:
                    try:
                        # Получаем строку с датой
                        date_str = json_ld[field]
                        self.logger.debug(f"Найдена дата в JSON-LD {field}: {date_str}")

                        # Используем класс DateUtils для обработки даты
                        parsed_date = DateUtils.extract_date_from_text(date_str)

                        if parsed_date:
                            self.logger.debug(f"Извлечена дата из JSON-LD {field}: {parsed_date}")
                            return parsed_date

                        # Если DateFormatter не смог обработать дату, пробуем стандартный подход
                        try:
                            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            self.logger.debug(f"Извлечена дата из JSON-LD {field} через fromisoformat: {date_obj}")
                            return date_obj
                        except (ValueError, TypeError, AttributeError) as e:
                            self.logger.warning(f"Не удалось распарсить дату из {field} через fromisoformat: {e}")
                            continue

                    except (ValueError, TypeError, AttributeError) as e:
                        self.logger.warning(f"Не удалось распарсить дату из {field}: {e}")
                        continue

        return None
