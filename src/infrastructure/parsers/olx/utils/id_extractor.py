"""
Извлечение ID OLX.
"""
import json
import logging
import re
from typing import List, Optional

from bs4 import BeautifulSoup, Comment

from src.infrastructure.parsers.olx.utils.json_ld_processor import JsonLdProcessor


class IdExtractor:
    """
    Класс для извлечения ID объявлений OLX.
    """

    # Логгер для класса
    logger = logging.getLogger('olx.id_extractor')

    @classmethod
    def extract_id_from_url(cls, url: str) -> Optional[str]:
        """
        Извлекает ID объявления из URL.

        Args:
            url: URL объявления

        Returns:
            Optional[str]: ID объявления или None, если ID не найден
        """
        if not url:
            cls.logger.debug("URL пустой, невозможно извлечь ID")
            return None

        # Ищем ID в URL
        match = re.search(r'/obyavlenie/([^.]+)\.html', url)
        if match:
            ad_id = match.group(1)
            cls.logger.debug(f"Извлечен ID из URL: {ad_id}")
            return ad_id

        cls.logger.debug(f"Не удалось извлечь ID из URL: {url}")
        return None

    @staticmethod
    def extract_id_from_html(html: str) -> Optional[str]:
        """
        Извлекает ID объявления из HTML.

        Args:
            html: HTML объявления

        Returns:
            Optional[str]: ID объявления или None, если ID не найден
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Шаблоны для поиска ID в тексте
            id_patterns = [
                r'ID[:№]\s*<!--.*?-->(\d{5,})',  # ID с HTML комментарием
                r'ID[:№]\s*(\d{5,})',            # Простой ID
                r'<!--.*?-->(\d{5,})',           # ID в комментарии
                r'<!--\s*-->(\d{5,})',           # ID после пустого комментария
                r'ID[^\d]*(\d{5,})',             # ID после слова "ID"
                r'(\d{5,})',                     # Просто числовой ID
                r'ID\s*:\s*(\d+)',              # ID в формате "ID: 123456"
                r'ID(\d+)',                      # ID в формате "ID123456"
            ]

            # 1. Ищем ID в специальных элементах span с классом css-w85dhy или других похожих селекторах
            id_selectors = [
                'span.css-w85dhy',                # Основной селектор для ID
                'span[data-testid="ad-id"]',      # Селектор по data-testid
                'div.css-*:contains("ID")',       # Любой div с классом, начинающимся с css- и содержащий "ID"
                'span.css-*:contains("ID")',      # Любой span с классом, начинающимся с css- и содержащий "ID"
            ]

            for selector in id_selectors:
                try:
                    id_element = soup.select_one(selector)
                    if id_element and id_element.text:
                        # Проверяем все шаблоны для текста элемента
                        for pattern in id_patterns:
                            id_match = re.search(pattern, id_element.text)
                            if id_match:
                                return id_match.group(1)
                except Exception:
                    # Игнорируем ошибки в селекторах и продолжаем поиск
                    pass

            # 2. Ищем ID в JSON-LD разметке
            try:
                json_ld_processor = JsonLdProcessor(logger=logging.getLogger(__name__))
                json_ld_data = json_ld_processor.extract_json_ld(html)

                if json_ld_data:
                    # Проверяем каждый JSON-LD объект
                    for json_data in json_ld_data:
                        # Расширенный список полей, где может быть ID
                        id_fields = ['productID', 'identifier', 'sku', '@id', 'id', 'mainEntityOfPage', 'url', 'offers.sku', 'offers.itemOffered.productID']

                        # Проверяем прямые поля
                        for field in id_fields:
                            # Проверяем вложенные поля (с точкой)
                            if '.' in field:
                                parts = field.split('.')
                                value = json_data
                                for part in parts:
                                    if isinstance(value, dict) and part in value:
                                        value = value[part]
                                    else:
                                        value = None
                                        break

                                if value:
                                    # Проверяем все шаблоны для значения поля
                                    for pattern in id_patterns:
                                        id_match = re.search(pattern, str(value))
                                        if id_match:
                                            return id_match.group(1)

                                    # Если это числовой ID
                                    if isinstance(value, (int, str)) and str(value).isdigit():
                                        return str(value)
                            elif field in json_data and json_data[field]:
                                # Проверяем все шаблоны для значения поля
                                for pattern in id_patterns:
                                    id_match = re.search(pattern, str(json_data[field]))
                                    if id_match:
                                        return id_match.group(1)

                                # Если это числовой ID
                                if isinstance(json_data[field], (int, str)) and str(json_data[field]).isdigit():
                                    return str(json_data[field])
            except Exception:
                # Игнорируем ошибки при работе с JSON-LD и продолжаем поиск другими способами
                pass

            # 3. Ищем ID в скриптах с состоянием приложения и других скриптах
            scripts = soup.find_all('script')
            for script in scripts:
                if not script.string:
                    continue

                # Проверяем все шаблоны для текста скрипта
                for pattern in id_patterns:
                    id_match = re.search(pattern, script.string)
                    if id_match:
                        return id_match.group(1)

                # Ищем в скрипте с состоянием
                if 'window.__PRERENDERED_STATE__' in script.string:
                    try:
                        # Извлекаем JSON из скрипта
                        state_match = re.search(r'window\.__PRERENDERED_STATE__\s*=\s*({.*?});', script.string)
                        if state_match:
                            state_text = state_match.group(1)
                            # Ищем ID в тексте состояния
                            for pattern in id_patterns:
                                id_match = re.search(pattern, state_text)
                                if id_match:
                                    return id_match.group(1)

                            # Если не нашли по шаблонам, пробуем парсить JSON
                            try:
                                state = json.loads(state_text)
                                # Ищем ID в разных местах состояния
                                id_paths = [
                                    ['id'],
                                    ['ad', 'id'],
                                    ['listing', 'id'],
                                    ['props', 'pageProps', 'ad', 'id'],
                                    ['props', 'pageProps', 'data', 'id'],
                                    ['props', 'initialState', 'ad', 'id'],
                                    ['data', 'id'],
                                    ['item', 'id']
                                ]

                                for path in id_paths:
                                    value = state
                                    try:
                                        for key in path:
                                            if isinstance(value, dict) and key in value:
                                                value = value[key]
                                            else:
                                                value = None
                                                break

                                        if value and (isinstance(value, (int, str))):
                                            return str(value)
                                    except Exception:
                                        continue
                            except json.JSONDecodeError:
                                pass
                    except Exception:
                        pass

                # Ищем ID в других форматах в скрипте
                id_patterns_in_script = [
                    r'"id":\s*"([^"]+)"',           # "id": "123456"
                    r'"id":\s*(\d+)',                # "id": 123456
                    r'"adId":\s*"([^"]+)"',        # "adId": "123456"
                    r'"adId":\s*(\d+)',             # "adId": 123456
                    r'"ad_id":\s*"([^"]+)"',       # "ad_id": "123456"
                    r'"ad_id":\s*(\d+)',            # "ad_id": 123456
                    r'"productID":\s*"([^"]+)"',    # "productID": "123456"
                    r'"sku":\s*"([^"]+)"',         # "sku": "123456"
                ]

                for pattern in id_patterns_in_script:
                    id_match = re.search(pattern, script.string)
                    if id_match:
                        return id_match.group(1)

            # 4. Ищем ID в HTML-комментариях
            comments = soup.find_all(string=lambda text: isinstance(text, Comment))
            for comment in comments:
                for pattern in id_patterns:
                    id_match = re.search(pattern, comment)
                    if id_match:
                        return id_match.group(1)

            # 4. В крайнем случае, если не нашли ID другими способами, пробуем извлечь из URL
            meta_id = soup.find('meta', property='og:url')
            if meta_id:
                url = meta_id.get('content')
                if url:
                    # Извлекаем ID из URL только если не нашли другими способами
                    print(f"Внимание: ID не найден в элементах страницы, используем ID из URL: {url}")
                    return IdExtractor.extract_id_from_url(url)

        except Exception as e:
            print(f"Ошибка при извлечении ID объявления из HTML: {e}")

        return None

    @classmethod
    def extract_ids_from_listing_html(cls, html: str) -> List[str]:
        """
        Извлекает ID объявлений из HTML страницы листинга.

        Args:
            html: HTML страницы листинга

        Returns:
            List[str]: Список ID объявлений
        """
        ids = []

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Ищем все объявления на странице по разным селекторам
            ad_selectors = [
                'div[data-cy="l-card"]',           # Основной селектор для карточек объявлений
                'div.css-1sw7q4x',                 # Альтернативный селектор по классу
                'a[href*="/obyavlenie/"]',        # Поиск по ссылкам на объявления
                'div.offer-wrapper',               # Старый формат карточек
            ]

            for selector in ad_selectors:
                ad_elements = soup.select(selector)

                for ad_element in ad_elements:
                    # Пробуем разные способы извлечения ID

                    # 1. Извлекаем ID из атрибута data-id
                    ad_id = ad_element.get('data-id')
                    if ad_id and cls.is_valid_id(ad_id):
                        if ad_id not in ids:
                            ids.append(ad_id)
                        continue

                    # 2. Ищем ID в тексте элемента
                    id_patterns = [
                        r'ID[:№]\s*(\d{5,})',            # Простой ID
                        r'ID[^\d]*(\d{5,})',             # ID после слова "ID"
                        r'(\d{5,})',                     # Просто числовой ID
                    ]

                    for pattern in id_patterns:
                        id_match = re.search(pattern, ad_element.text)
                        if id_match:
                            ad_id = id_match.group(1)
                            if ad_id and cls.is_valid_id(ad_id) and ad_id not in ids:
                                ids.append(ad_id)
                                break

                    # Если нашли ID, переходим к следующему элементу
                    if ad_id and ad_id in ids:
                        continue

                    # 3. Извлекаем ID из URL
                    link_element = ad_element.select_one('a[href*="/obyavlenie/"]')
                    if link_element:
                        href = link_element.get('href', '')
                        ad_id = cls.extract_id_from_url(href)
                        if ad_id and cls.is_valid_id(ad_id) and ad_id not in ids:
                            ids.append(ad_id)

        except Exception as e:
            print(f"Ошибка при извлечении ID объявлений из HTML страницы листинга: {e}")

        return ids

    @classmethod
    def extract_ids_from_soup(cls, soup: BeautifulSoup) -> List[str]:
        """
        Извлекает ID объявлений из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            List[str]: Список ID объявлений
        """
        ids = []

        try:
            # Ищем все объявления на странице по разным селекторам
            ad_selectors = [
                'div[data-cy="l-card"]',           # Основной селектор для карточек объявлений
                'div.css-1sw7q4x',                 # Альтернативный селектор по классу
                'a[href*="/obyavlenie/"]',        # Поиск по ссылкам на объявления
                'div.offer-wrapper',               # Старый формат карточек
            ]

            for selector in ad_selectors:
                ad_elements = soup.select(selector)

                for ad_element in ad_elements:
                    # Пробуем разные способы извлечения ID

                    # 1. Извлекаем ID из атрибута data-id
                    ad_id = ad_element.get('data-id')
                    if ad_id and cls.is_valid_id(ad_id):
                        if ad_id not in ids:
                            ids.append(ad_id)
                        continue

                    # 2. Ищем ID в тексте элемента
                    id_patterns = [
                        r'ID[:№]\s*(\d{5,})',            # Простой ID
                        r'ID[^\d]*(\d{5,})',             # ID после слова "ID"
                        r'(\d{5,})',                     # Просто числовой ID
                    ]

                    for pattern in id_patterns:
                        id_match = re.search(pattern, ad_element.text)
                        if id_match:
                            ad_id = id_match.group(1)
                            if ad_id and cls.is_valid_id(ad_id) and ad_id not in ids:
                                ids.append(ad_id)
                                break

                    # Если нашли ID, переходим к следующему элементу
                    if ad_id and ad_id in ids:
                        continue

                    # 3. Извлекаем ID из URL
                    link_element = ad_element.select_one('a[href*="/obyavlenie/"]')
                    if link_element:
                        href = link_element.get('href', '')
                        ad_id = cls.extract_id_from_url(href)
                        if ad_id and cls.is_valid_id(ad_id) and ad_id not in ids:
                            ids.append(ad_id)

        except Exception as e:
            print(f"Ошибка при извлечении ID объявлений из объекта BeautifulSoup: {e}")

        return ids

    @classmethod
    def is_valid_id(cls, ad_id: str) -> bool:
        """
        Проверяет, является ли ID объявления валидным.

        Args:
            ad_id: ID объявления

        Returns:
            bool: True, если ID валидный, иначе False
        """
        if not ad_id:
            return False

        # Проверяем разные форматы ID

        # Проверка на буквенно-цифровой ID с допустимыми символами
        if re.match(r'^[a-zA-Z0-9_-]+$', ad_id):
            return True

        # Проверка на числовой ID
        if ad_id.isdigit() and len(ad_id) >= 5:
            return True

        # Проверка на ID в формате "ID123456"
        if re.match(r'^ID\d{5,}$', ad_id):
            return True

        # Проверка на ID в формате "название-ID123456"
        if re.match(r'^[\w-]+-ID\d{5,}$', ad_id):
            return True

        return False
