"""
Обработка дат OLX.
"""
import json
import logging
import re
from datetime import datetime, timedelta, timezone
from typing import Optional, Union, Dict, Any, List

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text

# Проверяем наличие модуля zoneinfo или pytz для работы с часовыми поясами
try:
    from zoneinfo import ZoneInfo
    HAVE_ZONEINFO = True
except ImportError:
    HAVE_ZONEINFO = False
    ZoneInfo = None  # Для типизации
    try:
        import pytz
        HAVE_PYTZ = True
    except ImportError:
        HAVE_PYTZ = False
        pytz = None  # Для типизации


class DateUtils:
    """
    Класс для обработки дат OLX.
    """

    # Логгер для класса
    logger = logging.getLogger('olx.date_utils')

    @staticmethod
    def extract_date_from_text(text: str) -> Optional[datetime]:
        """
        Извлекает дату из текста.

        Args:
            text: Текст с датой

        Returns:
            Optional[datetime]: Дата или None, если дата не найдена
        """
        if not text:
            return None

        # Очищаем текст
        text = clean_text(text).lower()

        # Текущая дата
        now = datetime.now()

        # Проверяем на "сегодня" или "сьогодні"
        if 'сегодня' in text or 'сьогодні' in text:
            time_match = re.search(r'(\d{1,2}):(\d{2})', text)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2))
                return now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Проверяем на "вчера" или "вчора"
        if 'вчера' in text or 'вчора' in text:
            yesterday = now - timedelta(days=1)
            time_match = re.search(r'(\d{1,2}):(\d{2})', text)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2))
                return yesterday.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return yesterday.replace(hour=0, minute=0, second=0, microsecond=0)

        # Проверяем на дату в формате "день месяц"
        date_match = re.search(r'(\d{1,2})\s+([а-яА-Яіїєґ]+)', text)
        if date_match:
            day = int(date_match.group(1))
            month_name = date_match.group(2).lower()

            # Словарь соответствий месяцев (русский и украинский)
            month_map = {
                # Русские названия
                'янв': 1, 'января': 1, 'январь': 1,
                'фев': 2, 'февраля': 2, 'февраль': 2,
                'мар': 3, 'марта': 3, 'март': 3,
                'апр': 4, 'апреля': 4, 'апрель': 4,
                'май': 5, 'мая': 5,
                'июн': 6, 'июня': 6, 'июнь': 6,
                'июл': 7, 'июля': 7, 'июль': 7,
                'авг': 8, 'августа': 8, 'август': 8,
                'сен': 9, 'сентября': 9, 'сентябрь': 9,
                'окт': 10, 'октября': 10, 'октябрь': 10,
                'ноя': 11, 'ноября': 11, 'ноябрь': 11,
                'дек': 12, 'декабря': 12, 'декабрь': 12,

                # Украинские названия
                'січ': 1, 'січня': 1, 'січень': 1,
                'лют': 2, 'лютого': 2, 'лютий': 2,
                'бер': 3, 'березня': 3, 'березень': 3,
                'квіт': 4, 'квітня': 4, 'квітень': 4,
                'трав': 5, 'травня': 5, 'травень': 5,
                'черв': 6, 'червня': 6, 'червень': 6,
                'лип': 7, 'липня': 7, 'липень': 7,
                'серп': 8, 'серпня': 8, 'серпень': 8,
                'вер': 9, 'вересня': 9, 'вересень': 9,
                'жовт': 10, 'жовтня': 10, 'жовтень': 10,
                'лист': 11, 'листопада': 11, 'листопад': 11,
                'груд': 12, 'грудня': 12, 'грудень': 12
            }

            # Ищем месяц в словаре
            month = None
            for key, value in month_map.items():
                if key in month_name:
                    month = value
                    break

            if month:
                # Определяем год
                year = now.year
                if month > now.month or (month == now.month and day > now.day):
                    year -= 1

                # Извлекаем время
                time_match = re.search(r'(\d{1,2}):(\d{2})', text)
                if time_match:
                    hour = int(time_match.group(1))
                    minute = int(time_match.group(2))
                    return datetime(year, month, day, hour, minute, 0)

                return datetime(year, month, day, 0, 0, 0)

        # Проверяем на дату в формате "день.месяц.год" или "день месяц год"
        date_match = re.search(r'(\d{1,2})[\.\s](\d{1,2}|[а-яА-Яіїєґ]+)[\.\s](\d{2,4})', text)
        if date_match:
            day = int(date_match.group(1))
            month_value = date_match.group(2)
            year = int(date_match.group(3))

            # Определяем месяц
            try:
                # Если месяц числовой
                month = int(month_value)
            except ValueError:
                # Если месяц текстовый
                month_name = month_value.lower()
                month = None

                # Словарь соответствий месяцев (русский и украинский)
                month_map = {
                    # Русские названия
                    'янв': 1, 'января': 1, 'январь': 1,
                    'фев': 2, 'февраля': 2, 'февраль': 2,
                    'мар': 3, 'марта': 3, 'март': 3,
                    'апр': 4, 'апреля': 4, 'апрель': 4,
                    'май': 5, 'мая': 5,
                    'июн': 6, 'июня': 6, 'июнь': 6,
                    'июл': 7, 'июля': 7, 'июль': 7,
                    'авг': 8, 'августа': 8, 'август': 8,
                    'сен': 9, 'сентября': 9, 'сентябрь': 9,
                    'окт': 10, 'октября': 10, 'октябрь': 10,
                    'ноя': 11, 'ноября': 11, 'ноябрь': 11,
                    'дек': 12, 'декабря': 12, 'декабрь': 12,

                    # Украинские названия
                    'січ': 1, 'січня': 1, 'січень': 1,
                    'лют': 2, 'лютого': 2, 'лютий': 2,
                    'бер': 3, 'березня': 3, 'березень': 3,
                    'квіт': 4, 'квітня': 4, 'квітень': 4,
                    'трав': 5, 'травня': 5, 'травень': 5,
                    'черв': 6, 'червня': 6, 'червень': 6,
                    'лип': 7, 'липня': 7, 'липень': 7,
                    'серп': 8, 'серпня': 8, 'серпень': 8,
                    'вер': 9, 'вересня': 9, 'вересень': 9,
                    'жовт': 10, 'жовтня': 10, 'жовтень': 10,
                    'лист': 11, 'листопада': 11, 'листопад': 11,
                    'груд': 12, 'грудня': 12, 'грудень': 12
                }

                for key, value in month_map.items():
                    if key in month_name:
                        month = value
                        break
                if month is None:
                    return None  # Не удалось определить месяц

            # Если год двузначный, добавляем 2000
            if year < 100:
                year += 2000

            # Извлекаем время
            time_match = re.search(r'(\d{1,2}):(\d{2})', text)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2))
                return datetime(year, month, day, hour, minute, 0)

            return datetime(year, month, day, 0, 0, 0)

        return None

    @staticmethod
    def extract_date_from_soup(soup: BeautifulSoup) -> Optional[datetime]:
        """
        Извлекает дату публикации из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Optional[datetime]: Дата публикации или None, если дата не найдена
        """
        logger = DateUtils.logger

        try:
            # 1. Сначала пробуем извлечь дату из JSON-LD
            try:
                # Извлекаем JSON-LD данные
                json_ld_data = []
                json_ld_scripts = soup.find_all('script', type='application/ld+json')

                for script in json_ld_scripts:
                    try:
                        if script.string:
                            data = json.loads(script.string)
                            json_ld_data.append(data)
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Ошибка при парсинге JSON-LD: {e}")

                # Извлекаем дату из JSON-LD
                if json_ld_data:
                    date = DateUtils.extract_date_from_json_ld(json_ld_data)
                    if date:
                        logger.debug(f"Извлечена дата из JSON-LD: {date}")
                        return date
            except Exception as e:
                logger.warning(f"Ошибка при извлечении даты из JSON-LD: {e}")

            # 2. Если не нашли в JSON-LD, пробуем разные селекторы для даты публикации
            selectors = [
                'span[data-cy="ad-posted-at"]',  # Новый селектор из примера
                'span.css-pz2ytp',  # Еще один вариант нового селектора
                'span.css-19yf5ek',  # Старый селектор
                'span.css-*[data-cy="ad-posted-at"]',  # Любой span с классом, начинающимся с css- и data-cy
                'span.css-1eaxltp span',  # Селектор для вложенного span
            ]

            for selector in selectors:
                date_element = soup.select_one(selector)
                if date_element:
                    date_text = clean_text(date_element.text)
                    date = DateUtils.extract_date_from_text(date_text)
                    if date:
                        logger.debug(f"Извлечена дата из селектора {selector}: {date}")
                        return date

            # 3. Если не нашли по селекторам, пробуем найти в тексте страницы
            date_patterns = [
                # Украинский формат "Сьогодні о 10:30"
                re.compile(r'\b(Сьогодні|Вчора|\d{1,2}\s+[\w]+)\s+(?:о|\u043e|\u0432|\u0432)\s+(\d{1,2}:\d{2})\b'),
                # Русский формат "Сегодня в 10:30"
                re.compile(r'\b(Сегодня|Вчера|\d{1,2}\s+[\w]+)\s+(?:в|\u0432)\s+(\d{1,2}:\d{2})\b'),
                # Формат "Опубликовано Сегодня в 10:30"
                re.compile(r'Опубликовано\s+(Сегодня|Вчера|\d{1,2}\s+[\w]+)\s+(?:в|\u0432)\s+(\d{1,2}:\d{2})'),
                # Формат "Опубліковано Сьогодні о 10:30"
                re.compile(r'Опубліковано\s+(Сьогодні|Вчора|\d{1,2}\s+[\w]+)\s+(?:о|\u043e|\u0432|\u0432)\s+(\d{1,2}:\d{2})'),
                # Формат даты в виде "15.01.2023 10:30"
                re.compile(r'\b(\d{1,2}\.\d{1,2}\.\d{2,4})\s+(\d{1,2}:\d{2})\b'),
                # Формат даты в виде "15 января 2023 10:30"
                re.compile(r'\b(\d{1,2}\s+[\w]+\s+\d{4})\s+(\d{1,2}:\d{2})\b')
            ]

            page_text = soup.get_text()
            for pattern in date_patterns:
                date_match = pattern.search(page_text)
                if date_match:
                    date_text = date_match.group(0)
                    date = DateUtils.extract_date_from_text(date_text)
                    if date:
                        logger.debug(f"Извлечена дата из текста страницы: {date}")
                        return date

        except Exception as e:
            logger.error(f"Ошибка при извлечении даты публикации из объекта BeautifulSoup: {e}")

        logger.warning("Не удалось извлечь дату публикации из страницы")
        return None

    @staticmethod
    def format_date(date: datetime) -> str:
        """
        Форматирует дату.

        Args:
            date: Дата

        Returns:
            str: Отформатированная дата
        """
        now = datetime.now()

        # Если дата сегодня
        if date.date() == now.date():
            return f"Сегодня, {date.strftime('%H:%M')}"

        # Если дата вчера
        yesterday = now - timedelta(days=1)
        if date.date() == yesterday.date():
            return f"Вчера, {date.strftime('%H:%M')}"

        # Если дата в этом году
        if date.year == now.year:
            return date.strftime('%d %B, %H:%M')

        # Если дата в другом году
        return date.strftime('%d %B %Y, %H:%M')

    @staticmethod
    def get_relative_date(date: datetime) -> str:
        """
        Возвращает относительную дату.

        Args:
            date: Дата

        Returns:
            str: Относительная дата
        """
        now = datetime.now()
        diff = now - date

        # Если разница меньше часа
        if diff < timedelta(hours=1):
            minutes = diff.seconds // 60
            if minutes == 0:
                return "Только что"
            elif minutes == 1:
                return "1 минуту назад"
            elif minutes < 5:
                return f"{minutes} минуты назад"
            else:
                return f"{minutes} минут назад"

        # Если разница меньше суток
        if diff < timedelta(days=1):
            hours = diff.seconds // 3600
            if hours == 1:
                return "1 час назад"
            elif hours < 5:
                return f"{hours} часа назад"
            else:
                return f"{hours} часов назад"

        # Если разница меньше недели
        if diff < timedelta(days=7):
            days = diff.days
            if days == 1:
                return "1 день назад"
            else:
                return f"{days} дня назад"

        # Если разница меньше месяца
        if diff < timedelta(days=30):
            weeks = diff.days // 7
            if weeks == 1:
                return "1 неделю назад"
            elif weeks < 5:
                return f"{weeks} недели назад"
            else:
                return f"{weeks} недель назад"

        # Если разница меньше года
        if diff < timedelta(days=365):
            months = diff.days // 30
            if months == 1:
                return "1 месяц назад"
            elif months < 5:
                return f"{months} месяца назад"
            else:
                return f"{months} месяцев назад"

        # Если разница больше года
        years = diff.days // 365
        if years == 1:
            return "1 год назад"
        elif years < 5:
            return f"{years} года назад"
        else:
            return f"{years} лет назад"

    @staticmethod
    def is_ad_too_old(publication_date: Union[str, datetime, None],
                     max_age_hours: Optional[int] = 24,
                     current_time: Optional[datetime] = None,
                     ad_id: Optional[str] = None) -> bool:
        """
        Проверка, является ли объявление слишком старым.

        Функция учитывает разные форматы даты и проверяет:
        1. Если дата отсутствует, считаем объявление устаревшим
        2. Если дата в будущем, считаем объявление устаревшим
        3. Если дата только дата (без времени), проверяем по дням (макс. указанное значение или 24 часа)
        4. Если дата с временем, проверяем по часам (макс. указанное значение или 24 часа)

        Args:
            publication_date: Дата публикации (строка, объект datetime или None)
            max_age_hours: Максимальный возраст объявления в часах (по умолчанию 24)
            current_time: Текущее время (по умолчанию datetime.now())
            ad_id: ID объявления для логирования (опционально)

        Returns:
            bool: True если объявление слишком старое, иначе False
        """
        logger = DateUtils.logger

        # Устанавливаем значение по умолчанию для max_age_hours, если оно None
        effective_max_age_hours = 24 if max_age_hours is None else max_age_hours

        # Если дата не указана, считаем объявление устаревшим
        if publication_date is None:
            if ad_id:
                logger.debug(f"Объявление {ad_id} считается устаревшим: дата публикации не указана")
            return True

        # Устанавливаем текущее время, если не указано
        if current_time is None:
            current_time = datetime.now()

        # Преобразуем строку в datetime, если нужно
        parsed_date: Optional[datetime] = None
        if isinstance(publication_date, str):
            try:
                # Пробуем разные форматы даты
                try:
                    # Стандартный формат ISO
                    parsed_date = datetime.fromisoformat(publication_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    # Пробуем формат YYYY-MM-DD HH:MM:SS
                    try:
                        parsed_date = datetime.strptime(publication_date, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # Пробуем формат DD.MM.YYYY HH:MM
                        try:
                            parsed_date = datetime.strptime(publication_date, "%d.%m.%Y %H:%M")
                        except ValueError:
                            # Пробуем извлечь дату из текста
                            parsed_date = DateUtils.extract_date_from_text(publication_date)
            except Exception as e:
                if ad_id:
                    logger.error(f"Ошибка при обработке даты публикации для объявления {ad_id}: {e}")
                return True
            if parsed_date is None:
                if ad_id:
                    logger.warning(f"Не удалось распарсить дату публикации для объявления {ad_id}: {publication_date}")
                return True
        publication_date = parsed_date if isinstance(publication_date, str) else publication_date

        # Теперь publication_date должен быть datetime или None
        # Проверяем, что publication_date это datetime (после всех преобразований)
        # Используем проверку по имени класса вместо isinstance, чтобы избежать проблем с моками в тестах
        if not hasattr(publication_date, 'year') or not hasattr(publication_date, 'month'):
            if ad_id:
                logger.error(f"Некорректный тип даты публикации для объявления {ad_id}: {type(publication_date)}")
            return True

        # Проверяем, не в будущем ли дата
        # Используем метод is_future_date для проверки
        if DateUtils.is_future_date(publication_date, current_time):
            if ad_id:
                logger.warning(f"Объявление {ad_id} имеет дату публикации в будущем: {publication_date}")
            return True

        # На этом этапе publication_date точно не None, т.к. мы проверили это выше
        # Вычисляем разницу во времени
        assert publication_date is not None  # Для анализатора типов
        time_diff = current_time - publication_date

        # Проверяем, является ли дата только датой (без времени)
        is_date_only = DateUtils.is_date_only(publication_date)

        # Для дат без времени используем дни вместо часов
        if is_date_only:
            # Преобразуем часы в дни (округляем вверх)
            max_age_days = (effective_max_age_hours + 23) // 24  # Округление вверх
            if time_diff.days > max_age_days:
                if ad_id:
                    logger.debug(f"Объявление {ad_id} устарело: {time_diff.days} дней > {max_age_days} дней")
                return True
        else:
            # Для дат с временем используем часы
            hours_diff = time_diff.total_seconds() / 3600
            if hours_diff > effective_max_age_hours:
                if ad_id:
                    logger.debug(f"Объявление {ad_id} устарело: {hours_diff:.1f} часов > {effective_max_age_hours} часов")
                return True

        # Если объявление не устарело
        return False

    @staticmethod
    def extract_date_from_json_ld(json_ld_data: List[Dict[str, Any]]) -> Optional[datetime]:
        """
        Извлечение даты публикации из JSON-LD данных.

        Args:
            json_ld_data: JSON-LD данные

        Returns:
            Optional[datetime]: Дата публикации или None, если дата не найдена
        """
        logger = DateUtils.logger

        if not json_ld_data:
            return None

        # Проверяем все объекты JSON-LD
        for json_ld in json_ld_data:
            # Проверяем все возможные поля с датой
            date_fields = ['dateCreated', 'datePublished', 'dateModified', 'uploadDate', 'datePosted']

            for field in date_fields:
                if field in json_ld:
                    try:
                        # Получаем строку с датой
                        date_str = json_ld[field]
                        logger.debug(f"Найдена дата в JSON-LD {field}: {date_str}")

                        # Используем extract_date_from_text для обработки даты
                        parsed_date = DateUtils.extract_date_from_text(date_str)

                        if parsed_date:
                            logger.debug(f"Извлечена дата из JSON-LD {field}: {parsed_date}")
                            return parsed_date

                        # Если extract_date_from_text не смог обработать дату, пробуем стандартный подход
                        try:
                            # ISO 8601 формат
                            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            logger.debug(f"Извлечена дата из JSON-LD {field} через fromisoformat: {date_obj}")
                            return date_obj
                        except (ValueError, TypeError, AttributeError):
                            # Другие форматы
                            try:
                                date_obj = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S")
                                logger.debug(f"Извлечена дата из JSON-LD {field} через strptime: {date_obj}")
                                return date_obj
                            except ValueError:
                                logger.warning(f"Не удалось распарсить дату из {field} через strptime")
                                continue

                    except Exception as e:
                        logger.warning(f"Ошибка при извлечении даты из JSON-LD {field}: {e}")
                        continue

        return None

    @staticmethod
    def convert_to_timezone(date_value: datetime, target_timezone: str = "Europe/Kiev") -> datetime:
        """
        Конвертация даты в заданный часовой пояс.

        Функция принимает дату и целевой часовой пояс и возвращает
        дату, преобразованную в указанный часовой пояс.

        Args:
            date_value: Дата для конвертации (объект datetime)
            target_timezone: Целевой часовой пояс (например, 'Europe/Kiev')

        Returns:
            datetime: Дата в указанном часовом поясе

        Raises:
            ImportError: Если не установлены необходимые библиотеки (pytz или zoneinfo)
            ValueError: Если указан неверный часовой пояс
        """
        # Упрощенная реализация для избежания проблем с типами
        try:
            # Если дата уже имеет информацию о часовом поясе
            if date_value.tzinfo is not None:
                # Если доступен zoneinfo
                if HAVE_ZONEINFO:
                    # Используем zoneinfo
                    from zoneinfo import ZoneInfo
                    target_tz = ZoneInfo(target_timezone)
                    return date_value.astimezone(target_tz)
                # Если доступен pytz
                elif HAVE_PYTZ:
                    # Используем pytz
                    import pytz
                    target_tz = pytz.timezone(target_timezone)
                    return date_value.astimezone(target_tz)
                else:
                    # Если нет ни zoneinfo, ни pytz
                    raise ImportError("Для работы с часовыми поясами необходимо установить pytz или использовать Python 3.9+ с zoneinfo")

            # Если дата не имеет информации о часовом поясе
            # Если доступен zoneinfo
            if HAVE_ZONEINFO:
                # Используем zoneinfo
                from zoneinfo import ZoneInfo
                utc_tz = ZoneInfo("UTC")
                target_tz = ZoneInfo(target_timezone)
                date_with_tz = date_value.replace(tzinfo=utc_tz)
                return date_with_tz.astimezone(target_tz)
            # Если доступен pytz
            elif HAVE_PYTZ:
                # Используем pytz
                import pytz
                # Создаем новый datetime без часового пояса
                naive_date = datetime(date_value.year, date_value.month, date_value.day,
                                      date_value.hour, date_value.minute, date_value.second,
                                      date_value.microsecond)
                # Применяем UTC часовой пояс
                # В разных версиях pytz метод localize может быть разным
                try:
                    # Пробуем использовать localize для timezone объекта
                    # pytz.UTC не имеет метода localize, но pytz.timezone('UTC') имеет
                    utc_tz = pytz.timezone('UTC')
                    utc_date = utc_tz.localize(naive_date)
                except (AttributeError, TypeError):
                    # Фоллбэк: просто добавляем часовой пояс через replace
                    # Используем timezone из datetime для создания UTC зоны
                    utc_date = naive_date.replace(tzinfo=timezone.utc)
                # Конвертируем в целевой часовой пояс
                target_tz = pytz.timezone(target_timezone)
                return utc_date.astimezone(target_tz)
            else:
                # Если нет ни zoneinfo, ни pytz
                raise ImportError("Для работы с часовыми поясами необходимо установить pytz или использовать Python 3.9+ с zoneinfo")
        except Exception as e:
            # В случае ошибки возвращаем исходную дату
            DateUtils.logger.error(f"Ошибка при конвертации даты в часовой пояс: {e}")
            return date_value

    @staticmethod
    def is_date_only(date_value: Union[datetime, None]) -> bool:
        """
        Проверка, содержит ли дата только дату без времени (время 00:00:00).

        Args:
            date_value: Дата для проверки (объект datetime или None)

        Returns:
            bool: True если дата без времени (00:00:00), иначе False
        """
        # Проверяем, что date_value не None и имеет необходимые атрибуты
        if date_value is None or not hasattr(date_value, 'hour') or not hasattr(date_value, 'minute') or not hasattr(date_value, 'second'):
            return False
        return date_value.hour == 0 and date_value.minute == 0 and date_value.second == 0

    @staticmethod
    def compute_ad_age(publication_date: Union[str, datetime, None],
                      current_time: Optional[datetime] = None,
                      unit: str = "days") -> Union[timedelta, float]:
        """
        Вычисление возраста объявления.

        Функция принимает дату публикации и возвращает возраст объявления
        в виде timedelta или числового значения в указанных единицах измерения.

        Args:
            publication_date: Дата публикации (строка, объект datetime или None)
            current_time: Текущее время (по умолчанию datetime.now())
            unit: Единица измерения для числового значения ('seconds', 'minutes', 'hours', 'days', 'weeks')

        Returns:
            Union[timedelta, float]: Возраст объявления в виде timedelta или числового значения
        """
        # Если дата публикации не указана, возвращаем максимальное значение
        if publication_date is None:
            if unit == "timedelta":
                return timedelta.max
            else:
                return float('inf')

        # Устанавливаем текущее время, если не указано
        if current_time is None:
            current_time = datetime.now()

        # Преобразуем строку в datetime, если нужно
        if isinstance(publication_date, str):
            try:
                # Пробуем разные форматы даты
                try:
                    # Стандартный формат ISO
                    publication_date = datetime.fromisoformat(publication_date.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    # Пробуем формат YYYY-MM-DD HH:MM:SS
                    try:
                        publication_date = datetime.strptime(publication_date, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # Пробуем формат DD.MM.YYYY HH:MM
                        try:
                            publication_date = datetime.strptime(publication_date, "%d.%m.%Y %H:%M")
                        except ValueError:
                            # Пробуем извлечь дату из текста
                            publication_date = DateUtils.extract_date_from_text(publication_date)
                            if publication_date is None:
                                if unit == "timedelta":
                                    return timedelta.max
                                else:
                                    return float('inf')
            except Exception:
                if unit == "timedelta":
                    return timedelta.max
                else:
                    return float('inf')

        # Вычисляем разницу во времени
        time_diff = current_time - publication_date

        # Возвращаем результат в зависимости от указанной единицы измерения
        if unit == "timedelta":
            return time_diff
        elif unit == "seconds":
            return time_diff.total_seconds()
        elif unit == "minutes":
            return time_diff.total_seconds() / 60
        elif unit == "hours":
            return time_diff.total_seconds() / 3600
        elif unit == "days":
            return time_diff.total_seconds() / 86400
        elif unit == "weeks":
            return time_diff.total_seconds() / 604800
        else:
            raise ValueError(f"Неизвестная единица измерения: {unit}")

    @staticmethod
    def extract_date_from_card(card_element: BeautifulSoup) -> Optional[datetime]:
        """
        Извлекает дату публикации из карточки объявления в списке категории.

        Args:
            card_element: Элемент BeautifulSoup с карточкой объявления

        Returns:
            Optional[datetime]: Дата публикации или None, если дата не найдена
        """
        logger = DateUtils.logger

        try:
            # Селекторы для поиска даты в карточке объявления
            date_selectors = [
                'p[data-testid="location-date"]',  # Новый селектор для даты
                'p.css-veheph',                  # Альтернативный селектор по классу
                'p.css-*:contains("Сегодня")',  # Поиск по содержимому
                'p.css-*:contains("Вчера")',
                'span.css-*:contains("Сегодня")',
                'span.css-*:contains("Вчера")',
                'span[data-cy="ad-posted-at"]',
                'span.css-pz2ytp',
                'span.css-19yf5ek',
            ]

            for date_selector in date_selectors:
                try:
                    date_element = card_element.select_one(date_selector)
                    if date_element and date_element.text.strip():
                        date_text = date_element.text.strip()
                        logger.debug(f"Найден элемент с датой: {date_text}")
                        date = DateUtils.extract_date_from_text(date_text)
                        if date:
                            logger.debug(f"Извлечена дата из карточки объявления: {date}")
                            return date
                except Exception as e:
                    logger.debug(f"Ошибка при извлечении даты из селектора {date_selector}: {e}")

            # Если не нашли дату по селекторам, пробуем найти в тексте карточки
            card_text = card_element.get_text()
            date = DateUtils.extract_date_from_text(card_text)
            if date:
                logger.debug(f"Извлечена дата из текста карточки: {date}")
                return date

        except Exception as e:
            logger.error(f"Ошибка при извлечении даты из карточки объявления: {e}")

        return None

    @staticmethod
    def is_future_date(date_value: Union[str, datetime, None], current_time: Optional[datetime] = None) -> bool:
        """
        Проверка, является ли дата будущей.

        Args:
            date_value: Дата для проверки (строка или объект datetime)
            current_time: Текущее время (по умолчанию datetime.now())

        Returns:
            bool: True если дата в будущем, иначе False
        """
        # Если date_value не указано, возвращаем False
        if date_value is None:
            return False

        # Устанавливаем текущее время, если не указано
        if current_time is None:
            current_time = datetime.now()

        # Преобразуем строку в datetime, если нужно
        parsed_date: Optional[datetime] = None
        if isinstance(date_value, str):
            try:
                # Пробуем разные форматы даты
                try:
                    # Стандартный формат ISO
                    parsed_date = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    # Пробуем формат YYYY-MM-DD HH:MM:SS
                    try:
                        parsed_date = datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # Пробуем формат DD.MM.YYYY HH:MM
                        try:
                            parsed_date = datetime.strptime(date_value, "%d.%m.%Y %H:%M")
                        except ValueError:
                            # Пробуем извлечь дату из текста
                            parsed_date = DateUtils.extract_date_from_text(date_value)
            except Exception:
                return False

            # Если не удалось распарсить дату, возвращаем False
            if parsed_date is None:
                return False

            # Используем распарсенную дату
            return parsed_date > current_time
        else:
            # Если date_value не строка, проверяем, что это datetime-подобный объект
            # Используем проверку по атрибутам вместо isinstance, чтобы избежать проблем с моками в тестах
            if hasattr(date_value, 'year') and hasattr(date_value, 'month') and hasattr(date_value, 'day'):
                return date_value > current_time
            else:
                # Если date_value не строка и не datetime-подобный объект, возвращаем False
                return False
