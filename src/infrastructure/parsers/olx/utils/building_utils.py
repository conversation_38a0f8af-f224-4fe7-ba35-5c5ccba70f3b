"""
Информация о здании OLX.
Включает функции для извлечения и валидации:
- типа здания (панельный, кирпичный и т.д.)
- этажа
- общего количества этажей
- материала стен
"""
import re
import logging
from typing import Dict, Optional, Tuple

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text

# Получаем логгер для модуля
logger = logging.getLogger(__name__)

# Шаблоны для поиска информации об этажах
FLOOR_PATTERNS = [
    r'(\d+)\s*(?:из|/)\s*(\d+)\s*этаж',  # 3 из 9 этаж, 3/9 этаж
    r'(\d+)\s*этаж\s*(?:из|/)\s*(\d+)',  # 3 этаж из 9, 3 этаж/9
    r'этаж\s*[:-]?\s*(\d+)\s*(?:из|/)\s*(\d+)',  # этаж: 3 из 9, этаж 3/9
    r'этаж\s*[:-]?\s*(\d+)',  # этаж: 3, этаж 3
    r'(\d+)\s*этаж',  # 3 этаж
    r'(\d+)\s*эт',  # 3 эт
    r'(\d+)[/-](\d+)',  # 3/9, 3-9
    r'этажность\s*[:-]?\s*(\d+)',  # этажность: 9, этажность 9
    r'(\d+)\s*этажный',  # 9 этажный
    r'(\d+)\s*этажное'  # 9 этажное
]

# Типы зданий
BUILDING_TYPES = [
    'Панельный',
    'Кирпичный',
    'Монолитный',
    'Монолитно-кирпичный',
    'Блочный',
    'Деревянный',
    'Сталинский',
    'Хрущевский',
    'Брежневский',
    'Новостройка',
    'Вторичное жилье'
]

# Шаблоны для поиска типов зданий
BUILDING_TYPE_PATTERNS = {
    'Панельный': [r'панельн', r'панель'],
    'Кирпичный': [r'кирпичн', r'кирпич'],
    'Монолитный': [r'монолитн', r'монолит'],
    'Монолитно-кирпичный': [r'монолитно-кирпичн', r'монолитно кирпичн'],
    'Блочный': [r'блочн', r'блок'],
    'Деревянный': [r'деревянн', r'дерево'],
    'Сталинский': [r'сталинск', r'сталинк'],
    'Хрущевский': [r'хрущевск', r'хрущевк'],
    'Брежневский': [r'брежневск', r'брежневк'],
    'Новостройка': [r'новостройк'],
    'Вторичное жилье': [r'вторичное жилье', r'вторичк']
}

# Шаблоны для поиска материалов стен
BUILDING_MATERIAL_PATTERNS = {
    r'кирпич': 'Кирпич',
    r'панель': 'Панель',
    r'монолит': 'Монолит',
    r'дерево': 'Дерево',
    r'бетон': 'Бетон',
    r'газобетон': 'Газобетон',
    r'пеноблок': 'Пеноблок',
    r'шлакоблок': 'Шлакоблок'
}


class BuildingUtils:
    """
    Класс для обработки информации о здании OLX.
    """

    @staticmethod
    def extract_floor_info(text: str) -> Dict[str, Optional[int]]:
        """
        Извлекает информацию об этаже и количестве этажей из текста.

        Args:
            text: Текст для анализа

        Returns:
            Dict с ключами 'floor' и 'floors_count', значения могут быть None
        """
        logger.debug(f"Извлечение информации об этажах из текста: '{text}'")
        result: Dict[str, Optional[int]] = {'floor': None, 'floors_count': None}  # Используем floors_count для совместимости с остальным кодом

        if not text:
            return result

        # Очищаем текст
        text = clean_text(text)

        # Проверяем формат "3/9"
        if '/' in text:
            parts = text.strip().split('/')
            if len(parts) == 2:
                try:
                    floor = int(parts[0])
                    floors_count = int(parts[1])
                    result['floor'] = floor
                    result['floors_count'] = floors_count
                    logger.debug(f"Найден формат 'этаж/всего': {floor}/{floors_count}")
                    return result
                except ValueError:
                    logger.debug(f"Не удалось преобразовать части '{parts}' в числа")
                    pass  # Если не удалось преобразовать в числа, продолжаем обработку

        # Поиск информации об этажах по шаблонам
        for pattern in FLOOR_PATTERNS:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                numbers = [int(n) for n in match.groups() if n]
                if len(numbers) == 2:
                    # Если найдены два числа (например, 3/5), первое - этаж, второе - всего этажей
                    result['floor'] = numbers[0]
                    result['floors_count'] = numbers[1]
                    logger.debug(f"Найдены этажи по шаблону '{pattern}': этаж {numbers[0]}, всего {numbers[1]}")
                    return result
                elif len(numbers) == 1:
                    # Если найдено только одно число, определяем по контексту
                    if 'этаж' in match.group().lower() and 'этажн' not in match.group().lower():
                        result['floor'] = numbers[0]
                        logger.debug(f"Найден этаж: {numbers[0]}")
                        # Если явно указан этаж, возвращаем результат сразу
                        if pattern in [r'(\d+)\s*этаж', r'(\d+)\s*эт', r'этаж\s*[:-]?\s*(\d+)']:
                            return result
                    else:
                        result['floors_count'] = numbers[0]
                        logger.debug(f"Найдено общее количество этажей: {numbers[0]}")

        logger.debug(f"Результат извлечения этажей: {result}")
        return result

    @staticmethod
    def extract_floor_info_from_text(text: str) -> Tuple[Optional[int], Optional[int]]:
        """
        Извлекает информацию о этаже и этажности из текста.

        Args:
            text: Текст с информацией о этаже

        Returns:
            Tuple[Optional[int], Optional[int]]: Этаж и этажность или (None, None), если информация не найдена
        """
        # Используем новый метод для совместимости
        floor_info = BuildingUtils.extract_floor_info(text)
        return floor_info['floor'], floor_info['floors_count']

    @staticmethod
    def extract_floor_info_from_parameters(params: Dict[str, str]) -> Tuple[Optional[int], Optional[int]]:
        """
        Извлекает информацию о этаже и этажности из параметров объявления.

        Args:
            params: Словарь с параметрами объявления

        Returns:
            Tuple[Optional[int], Optional[int]]: Этаж и этажность или (None, None), если информация не найдена
        """
        floor = None
        total_floors = None

        # Проверяем наличие параметра "Этаж"
        if 'Этаж' in params:
            floor_text = params['Этаж']
            floor_match = re.search(r'(\d+)', floor_text)
            if floor_match:
                try:
                    floor = int(floor_match.group(1))
                except ValueError:
                    pass

        # Проверяем наличие параметра "Этажность"
        if 'Этажность' in params:
            total_floors_text = params['Этажность']
            total_floors_match = re.search(r'(\d+)', total_floors_text)
            if total_floors_match:
                try:
                    total_floors = int(total_floors_match.group(1))
                except ValueError:
                    pass

        return floor, total_floors

    @staticmethod
    def extract_floor_info_from_soup(soup: BeautifulSoup) -> Tuple[Optional[int], Optional[int]]:
        """
        Извлекает информацию о этаже и этажности из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Tuple[Optional[int], Optional[int]]: Этаж и этажность или (None, None), если информация не найдена
        """
        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Извлекаем информацию о этаже и этажности из параметров
            floor, total_floors = BuildingUtils.extract_floor_info_from_parameters(params)
            if floor is not None or total_floors is not None:
                return floor, total_floors

            # Если не нашли в параметрах, пробуем извлечь из заголовка
            from src.infrastructure.parsers.olx.utils.html_utils import get_title
            title = get_title(soup)
            if title:
                # Ищем в заголовке формат "этаж/этажность"
                floor_match = re.search(r'(\d+)\s*[/\\]\s*(\d+)\s*эт', title, re.IGNORECASE)
                if floor_match:
                    try:
                        floor = int(floor_match.group(1))
                        total_floors = int(floor_match.group(2))
                        return floor, total_floors
                    except ValueError:
                        pass

                # Ищем только этаж
                floor_match = re.search(r'(\d+)\s*этаж', title, re.IGNORECASE)
                if floor_match:
                    try:
                        floor = int(floor_match.group(1))
                        return floor, None
                    except ValueError:
                        pass

            # Если не нашли в заголовке, пробуем извлечь из описания
            from src.infrastructure.parsers.olx.utils.html_utils import get_description
            description = get_description(soup)
            if description:
                # Ищем в описании формат "этаж/этажность"
                floor_match = re.search(r'(\d+)\s*[/\\]\s*(\d+)\s*эт', description, re.IGNORECASE)
                if floor_match:
                    try:
                        floor = int(floor_match.group(1))
                        total_floors = int(floor_match.group(2))
                        return floor, total_floors
                    except ValueError:
                        pass

                # Ищем только этаж
                floor_match = re.search(r'(\d+)\s*этаж', description, re.IGNORECASE)
                if floor_match:
                    try:
                        floor = int(floor_match.group(1))
                        return floor, None
                    except ValueError:
                        pass

        except Exception as e:
            print(f"Ошибка при извлечении информации о этаже из объекта BeautifulSoup: {e}")

        return None, None

    @staticmethod
    def validate_floor_info(floor: Optional[int], total_floors: Optional[int]) -> bool:
        """
        Проверяет корректность информации об этажах.

        Args:
            floor: Номер этажа
            total_floors: Общее количество этажей

        Returns:
            True если данные корректны, False иначе
        """
        logger.debug(f"Проверка корректности этажей: этаж={floor}, всего={total_floors}")

        if floor is None and total_floors is None:
            logger.debug("Информация об этажах отсутствует")
            return True

        if floor is not None and (floor <= 0 or floor > 100):
            logger.warning(f"Некорректный номер этажа: {floor}")
            return False

        if total_floors is not None and (total_floors <= 0 or total_floors > 100):
            logger.warning(f"Некорректное общее количество этажей: {total_floors}")
            return False

        if floor is not None and total_floors is not None and floor > total_floors:
            logger.warning(f"Этаж ({floor}) больше общего количества этажей ({total_floors})")
            return False

        logger.debug("Информация об этажах корректна")
        return True

    @staticmethod
    def extract_building_type(text: str) -> Optional[str]:
        """
        Определяет тип здания из текста.

        Args:
            text: Текст для анализа

        Returns:
            Нормализованное название типа здания или None
        """
        logger.debug(f"Определение типа здания из текста: '{text}'")

        if not text:
            logger.debug("Пустой текст для определения типа здания")
            return None

        # Поиск по шаблонам
        for building_type, patterns in BUILDING_TYPE_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    logger.debug(f"Найден тип здания '{building_type}' по шаблону '{pattern}'")
                    return building_type

        logger.debug("Тип здания не определен")
        return None

    @staticmethod
    def extract_building_type_from_parameters(params: Dict[str, str]) -> Optional[str]:
        """
        Извлекает тип здания из параметров объявления.

        Args:
            params: Словарь с параметрами объявления

        Returns:
            Optional[str]: Тип здания или None, если тип здания не найден
        """
        # Проверяем наличие параметра "Тип строения"
        if 'Тип строения' in params:
            return params['Тип строения']

        return None

    @staticmethod
    def extract_wall_material(text: str) -> Optional[str]:
        """
        Извлекает материал стен из текста

        Args:
            text: Текст для анализа

        Returns:
            Нормализованное название материала стен или None
        """
        logger.debug(f"Извлечение материала стен из текста: '{text}'")

        if not text:
            logger.debug("Пустой текст для извлечения материала стен")
            return None

        # Поиск по шаблонам
        for pattern, material in BUILDING_MATERIAL_PATTERNS.items():
            if re.search(pattern, text, re.IGNORECASE):
                logger.debug(f"Найден материал стен '{material}' по шаблону '{pattern}'")
                return material

        logger.debug("Материал стен не определен")
        return None

    @staticmethod
    def extract_building_type_from_soup(soup: BeautifulSoup) -> Optional[str]:
        """
        Извлекает тип здания из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Optional[str]: Тип здания или None, если тип здания не найден
        """
        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Извлекаем тип здания из параметров
            building_type = BuildingUtils.extract_building_type_from_parameters(params)
            if building_type:
                return building_type

            # Если не нашли в параметрах, пробуем извлечь из описания
            from src.infrastructure.parsers.olx.utils.html_utils import get_description
            description = get_description(soup)
            if description:
                # Ищем типы зданий в описании
                building_types = [
                    'панельный', 'кирпичный', 'монолитный', 'монолитно-кирпичный',
                    'хрущевка', 'сталинка', 'чешка', 'новостройка', 'старый фонд',
                    # Украинские названия
                    'цегляний', 'панельний', 'монолітний', 'монолітно-цегляний',
                    'хрущовка', 'сталінка', 'чеський', 'новобудова'
                ]

                for building_type in building_types:
                    if building_type in description.lower():
                        return building_type

        except Exception as e:
            print(f"Ошибка при извлечении типа здания из объекта BeautifulSoup: {e}")

        return None

    @staticmethod
    def validate_wall_material(wall_material: Optional[str]) -> bool:
        """
        Проверяет корректность материала стен

        Args:
            wall_material: Материал стен для проверки

        Returns:
            True если материал корректный или отсутствует, False если некорректный
        """
        logger.debug(f"Проверка корректности материала стен: '{wall_material}'")

        if wall_material is None:
            logger.debug("Материал стен отсутствует")
            return True

        # Проверка на наличие в списке допустимых материалов
        for _, material in BUILDING_MATERIAL_PATTERNS.items():
            if wall_material.lower() == material.lower():
                logger.debug(f"Материал стен '{wall_material}' корректен")
                return True

        logger.warning(f"Некорректный материал стен: '{wall_material}'")
        return False

    @staticmethod
    def normalize_building_type(building_type: str) -> str:
        """
        Нормализует тип здания.

        Args:
            building_type: Тип здания

        Returns:
            str: Нормализованный тип здания
        """
        if not building_type:
            return building_type

        building_type = building_type.lower()

        # Словарь соответствий
        building_type_map = {
            'панельный': 'Панельный',
            'панель': 'Панельный',
            'кирпичный': 'Кирпичный',
            'кирпич': 'Кирпичный',
            'монолитный': 'Монолитный',
            'монолит': 'Монолитный',
            'монолитно-кирпичный': 'Монолитно-кирпичный',
            'монолитно кирпичный': 'Монолитно-кирпичный',
            'блочный': 'Блочный',
            'блок': 'Блочный',
            'деревянный': 'Деревянный',
            'дерево': 'Деревянный',
            'сталинский': 'Сталинский',
            'сталинка': 'Сталинский',
            'хрущевский': 'Хрущевский',
            'хрущевка': 'Хрущевский',
            'брежневский': 'Брежневский',
            'брежневка': 'Брежневский',
            'новостройка': 'Новостройка',
            'вторичное жилье': 'Вторичное жилье',
            'вторичка': 'Вторичное жилье'
        }

        return building_type_map.get(building_type, building_type.capitalize())

    @staticmethod
    def extract_wall_material_from_soup(soup: BeautifulSoup) -> Optional[str]:
        """
        Извлекает материал стен из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Optional[str]: Материал стен или None, если материал стен не найден
        """
        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Ищем материал стен в параметрах
            for param_name, param_value in params.items():
                if 'материал' in param_name.lower() or 'стен' in param_name.lower():
                    # Используем метод для извлечения материала стен
                    return BuildingUtils.extract_wall_material(param_value)

            # Если не нашли в параметрах, пробуем извлечь из описания
            from src.infrastructure.parsers.olx.utils.html_utils import get_description
            description = get_description(soup)
            if description:
                # Используем метод для извлечения материала стен
                return BuildingUtils.extract_wall_material(description)

        except Exception as e:
            logger.error(f"Ошибка при извлечении материала стен из объекта BeautifulSoup: {e}")

        return None

    @staticmethod
    def format_floor_info(floor: Optional[int], total_floors: Optional[int]) -> str:
        """
        Форматирует информацию о этаже и этажности.

        Args:
            floor: Этаж
            total_floors: Этажность

        Returns:
            str: Отформатированная информация о этаже и этажности
        """
        if floor is None:
            return ""

        if total_floors is None:
            return f"{floor} этаж"

        return f"{floor}/{total_floors} этаж"
