"""
Специфичная обработка текста OLX.
"""
import re
from typing import List, Optional, Tuple

from src.infrastructure.parsers.olx.utils.html_utils import clean_text


class TextProcessors:
    """
    Класс для обработки текста OLX.
    """

    @staticmethod
    def clean_title(title: str) -> str:
        """
        Очищает заголовок объявления.

        Args:
            title: Заголовок объявления

        Returns:
            str: Очищенный заголовок объявления
        """
        if not title:
            return ""

        # Очищаем текст
        title = clean_text(title)

        # Удаляем лишние символы
        title = re.sub(r'[!@#$%^&*()_+=\[\]{}|\\:;<>?/~`]', ' ', title)

        # Удаляем множественные пробелы
        title = re.sub(r'\s+', ' ', title)

        return title.strip()

    @staticmethod
    def clean_description(description: str) -> str:
        """
        Очищает описание объявления.

        Args:
            description: Описание объявления

        Returns:
            str: Очищенное описание объявления
        """
        if not description:
            return ""

        # Очищаем текст
        description = clean_text(description)

        # Удаляем HTML-теги
        description = re.sub(r'<[^>]+>', ' ', description)

        # Удаляем множественные пробелы
        description = re.sub(r'\s+', ' ', description)

        return description.strip()

    @staticmethod
    def extract_keywords_from_title(title: str) -> List[str]:
        """
        Извлекает ключевые слова из заголовка объявления.

        Args:
            title: Заголовок объявления

        Returns:
            List[str]: Список ключевых слов
        """
        if not title:
            return []

        # Очищаем заголовок
        title = TextProcessors.clean_title(title)

        # Разбиваем на слова
        words = title.split()

        # Фильтруем слова
        keywords = [word for word in words if len(word) > 3]

        return keywords

    @staticmethod
    def extract_keywords_from_description(description: str) -> List[str]:
        """
        Извлекает ключевые слова из описания объявления.

        Args:
            description: Описание объявления

        Returns:
            List[str]: Список ключевых слов
        """
        if not description:
            return []

        # Очищаем описание
        description = TextProcessors.clean_description(description)

        # Разбиваем на слова
        words = description.split()

        # Фильтруем слова
        keywords = [word for word in words if len(word) > 3]

        # Ограничиваем количество ключевых слов
        return keywords[:50]

    @staticmethod
    def extract_property_features_from_description(description: str) -> List[str]:
        """
        Извлекает особенности недвижимости из описания объявления.

        Args:
            description: Описание объявления

        Returns:
            List[str]: Список особенностей недвижимости
        """
        if not description:
            return []

        # Очищаем описание
        description = TextProcessors.clean_description(description)

        # Список особенностей недвижимости
        features = []

        # Ищем особенности в описании
        feature_patterns = [
            r'(?:с|со) свежим ремонтом',
            r'(?:с|со) свежим евроремонтом',
            r'(?:с|со) свежим дизайнерским ремонтом',
            r'(?:с|со) ремонтом',
            r'(?:с|со) евроремонтом',
            r'(?:с|со) дизайнерским ремонтом',
            r'без ремонта',
            r'требует ремонта',
            r'под ремонт',
            r'(?:с|со) мебелью',
            r'(?:с|со) техникой',
            r'(?:с|со) бытовой техникой',
            r'без мебели',
            r'без техники',
            r'без бытовой техники',
            r'(?:с|со) кондиционером',
            r'(?:с|со) балконом',
            r'(?:с|со) лоджией',
            r'(?:с|со) террасой',
            r'(?:с|со) гаражом',
            r'(?:с|со) парковкой',
            r'(?:с|со) парковочным местом',
            r'(?:с|со) подвалом',
            r'(?:с|со) кладовкой',
            r'(?:с|со) подсобным помещением',
            r'(?:с|со) садом',
            r'(?:с|со) участком',
            r'(?:с|со) бассейном',
            r'(?:с|со) сауной',
            r'(?:с|со) баней',
            r'(?:с|со) камином',
            r'(?:с|со) теплым полом',
            r'(?:с|со) автономным отоплением',
            r'(?:с|со) газовым отоплением',
            r'(?:с|со) электрическим отоплением',
            r'(?:с|со) твердотопливным отоплением',
            r'(?:с|со) центральным отоплением',
            r'(?:с|со) городским отоплением',
            r'(?:с|со) индивидуальным отоплением',
            r'(?:с|со) газовой колонкой',
            r'(?:с|со) бойлером',
            r'(?:с|со) водонагревателем',
            r'(?:с|со) счетчиками',
            r'(?:с|со) счетчиками на воду',
            r'(?:с|со) счетчиками на газ',
            r'(?:с|со) счетчиками на электричество',
            r'(?:с|со) счетчиками на отопление',
            r'(?:с|со) кабельным телевидением',
            r'(?:с|со) спутниковым телевидением',
            r'(?:с|со) интернетом',
            r'(?:с|со) высокоскоростным интернетом',
            r'(?:с|со) оптоволоконным интернетом',
            r'(?:с|со) видеонаблюдением',
            r'(?:с|со) сигнализацией',
            r'(?:с|со) охраной',
            r'(?:с|со) консьержем',
            r'(?:с|со) лифтом',
            r'(?:с|со) грузовым лифтом',
            r'(?:с|со) пассажирским лифтом',
            r'(?:с|со) детской площадкой',
            r'(?:с|со) спортивной площадкой',
            r'(?:с|со) закрытой территорией',
            r'(?:с|со) огороженной территорией',
            r'(?:с|со) охраняемой территорией',
            r'(?:с|со) видом на море',
            r'(?:с|со) видом на горы',
            r'(?:с|со) видом на лес',
            r'(?:с|со) видом на парк',
            r'(?:с|со) видом на реку',
            r'(?:с|со) видом на озеро',
            r'(?:с|со) видом на город',
            r'(?:с|со) видом на двор',
            r'(?:с|со) видом на улицу',
            r'(?:с|со) панорамным видом',
            r'(?:с|со) панорамными окнами',
            r'(?:с|со) панорамным остеклением',
            r'(?:с|со) витражными окнами',
            r'(?:с|со) витражным остеклением',
            r'(?:с|со) французскими окнами',
            r'(?:с|со) французским балконом',
            r'(?:с|со) французской лоджией',
            r'(?:с|со) французской террасой',
            r'(?:с|со) гардеробной',
            r'(?:с|со) гардеробной комнатой',
            r'(?:с|со) встроенной гардеробной',
            r'(?:с|со) встроенным шкафом',
            r'(?:с|со) встроенной мебелью',
            r'(?:с|со) встроенной кухней',
            r'(?:с|со) встроенной бытовой техникой',
            r'(?:с|со) встроенной стиральной машиной',
            r'(?:с|со) встроенной посудомоечной машиной',
            r'(?:с|со) встроенным холодильником',
            r'(?:с|со) встроенной духовкой',
            r'(?:с|со) встроенной варочной панелью',
            r'(?:с|со) встроенной вытяжкой',
            r'(?:с|со) встроенной микроволновой печью',
            r'(?:с|со) встроенным телевизором',
            r'(?:с|со) встроенной акустической системой',
            r'(?:с|со) встроенным кондиционером',
            r'(?:с|со) встроенным сейфом',
            r'(?:с|со) встроенным мини-баром',
            r'(?:с|со) встроенным винным шкафом',
            r'(?:с|со) встроенным камином',
            r'(?:с|со) встроенным аквариумом',
            r'(?:с|со) встроенным фонтаном',
            r'(?:с|со) встроенной библиотекой',
            r'(?:с|со) встроенным рабочим местом',
            r'(?:с|со) встроенным офисом',
            r'(?:с|со) встроенной детской',
            r'(?:с|со) встроенной игровой комнатой',
            r'(?:с|со) встроенной спортивной комнатой',
            r'(?:с|со) встроенным спортзалом',
            r'(?:с|со) встроенной сауной',
            r'(?:с|со) встроенной баней',
            r'(?:с|со) встроенным бассейном',
            r'(?:с|со) встроенным джакузи',
            r'(?:с|со) встроенной ванной',
            r'(?:с|со) встроенным душем',
            r'(?:с|со) встроенным туалетом',
            r'(?:с|со) встроенным биде',
            r'(?:с|со) встроенной стиральной машиной',
            r'(?:с|со) встроенной сушильной машиной',
            r'(?:с|со) встроенной гладильной доской',
            r'(?:с|со) встроенным утюгом',
            r'(?:с|со) встроенным пылесосом',
            r'(?:с|со) встроенной системой вентиляции',
            r'(?:с|со) встроенной системой кондиционирования',
            r'(?:с|со) встроенной системой отопления',
            r'(?:с|со) встроенной системой теплого пола',
            r'(?:с|со) встроенной системой видеонаблюдения',
            r'(?:с|со) встроенной системой сигнализации',
            r'(?:с|со) встроенной системой пожаротушения',
            r'(?:с|со) встроенной системой умный дом',
            r'(?:с|со) встроенной системой умный свет',
            r'(?:с|со) встроенной системой умные шторы',
            r'(?:с|со) встроенной системой умные жалюзи',
            r'(?:с|со) встроенной системой умные двери',
            r'(?:с|со) встроенной системой умные окна',
            r'(?:с|со) встроенной системой умный замок',
            r'(?:с|со) встроенной системой умный домофон',
            r'(?:с|со) встроенной системой умный звонок',
            r'(?:с|со) встроенной системой умная розетка',
            r'(?:с|со) встроенной системой умная колонка',
            r'(?:с|со) встроенной системой умный телевизор',
            r'(?:с|со) встроенной системой умный холодильник',
            r'(?:с|со) встроенной системой умная стиральная машина',
            r'(?:с|со) встроенной системой умная посудомоечная машина',
            r'(?:с|со) встроенной системой умная духовка',
            r'(?:с|со) встроенной системой умная варочная панель',
            r'(?:с|со) встроенной системой умная вытяжка',
            r'(?:с|со) встроенной системой умная микроволновая печь',
            r'(?:с|со) встроенной системой умный кондиционер',
            r'(?:с|со) встроенной системой умный бойлер',
            r'(?:с|со) встроенной системой умный водонагреватель',
            r'(?:с|со) встроенной системой умный счетчик',
            r'(?:с|со) встроенной системой умный счетчик воды',
            r'(?:с|со) встроенной системой умный счетчик газа',
            r'(?:с|со) встроенной системой умный счетчик электричества',
            r'(?:с|со) встроенной системой умный счетчик отопления'
        ]

        for pattern in feature_patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                feature = match.group(0)
                features.append(feature)

        return features

    @staticmethod
    def extract_property_type_from_text(text: str) -> Optional[str]:
        """
        Извлекает тип недвижимости из текста.

        Args:
            text: Текст

        Returns:
            Optional[str]: Тип недвижимости или None, если тип недвижимости не найден
        """
        if not text:
            return None

        # Очищаем текст
        text = clean_text(text).lower()

        # Словарь типов недвижимости
        property_types = {
            'квартира': 'Квартира',
            'комната': 'Комната',
            'дом': 'Дом',
            'дача': 'Дача',
            'коттедж': 'Коттедж',
            'таунхаус': 'Таунхаус',
            'дуплекс': 'Дуплекс',
            'участок': 'Участок',
            'земельный участок': 'Земельный участок',
            'гараж': 'Гараж',
            'паркинг': 'Паркинг',
            'парковка': 'Парковка',
            'офис': 'Офис',
            'магазин': 'Магазин',
            'склад': 'Склад',
            'производство': 'Производство',
            'помещение': 'Помещение',
            'здание': 'Здание',
            'база': 'База',
            'комплекс': 'Комплекс',
            'бизнес': 'Бизнес',
            'готовый бизнес': 'Готовый бизнес',
            'отель': 'Отель',
            'гостиница': 'Гостиница',
            'хостел': 'Хостел',
            'ресторан': 'Ресторан',
            'кафе': 'Кафе',
            'бар': 'Бар',
            'салон': 'Салон',
            'сервис': 'Сервис',
            'автосервис': 'Автосервис',
            'автомойка': 'Автомойка',
            'сто': 'СТО',
            'азс': 'АЗС',
            'заправка': 'Заправка',
            'аптека': 'Аптека',
            'клиника': 'Клиника',
            'медицинский центр': 'Медицинский центр',
            'стоматология': 'Стоматология',
            'салон красоты': 'Салон красоты',
            'парикмахерская': 'Парикмахерская',
            'спа': 'СПА',
            'фитнес': 'Фитнес',
            'спортзал': 'Спортзал',
            'тренажерный зал': 'Тренажерный зал',
            'бассейн': 'Бассейн',
            'сауна': 'Сауна',
            'баня': 'Баня',
            'детский сад': 'Детский сад',
            'школа': 'Школа',
            'учебный центр': 'Учебный центр',
            'курсы': 'Курсы',
            'автошкола': 'Автошкола',
            'ателье': 'Ателье',
            'швейный цех': 'Швейный цех',
            'производственный цех': 'Производственный цех',
            'мастерская': 'Мастерская',
            'студия': 'Студия',
            'фотостудия': 'Фотостудия',
            'видеостудия': 'Видеостудия',
            'звукозаписывающая студия': 'Звукозаписывающая студия',
            'танцевальная студия': 'Танцевальная студия',
            'художественная студия': 'Художественная студия',
            'галерея': 'Галерея',
            'выставочный зал': 'Выставочный зал',
            'шоурум': 'Шоурум',
            'павильон': 'Павильон',
            'киоск': 'Киоск',
            'ларек': 'Ларек',
            'торговая точка': 'Торговая точка',
            'торговый центр': 'Торговый центр',
            'торговый комплекс': 'Торговый комплекс',
            'бизнес центр': 'Бизнес центр',
            'офисный центр': 'Офисный центр',
            'офисное здание': 'Офисное здание',
            'офисное помещение': 'Офисное помещение',
            'офисный блок': 'Офисный блок',
            'офисный этаж': 'Офисный этаж',
            'офисный комплекс': 'Офисный комплекс',
            'логистический центр': 'Логистический центр',
            'логистический комплекс': 'Логистический комплекс',
            'складской комплекс': 'Складской комплекс',
            'складское помещение': 'Складское помещение',
            'складской терминал': 'Складской терминал',
            'производственное помещение': 'Производственное помещение',
            'производственный комплекс': 'Производственный комплекс',
            'производственная база': 'Производственная база',
            'промышленная база': 'Промышленная база',
            'промышленный комплекс': 'Промышленный комплекс',
            'промышленное помещение': 'Промышленное помещение',
            'промышленное здание': 'Промышленное здание',
            'промышленный объект': 'Промышленный объект',
            'промышленная площадка': 'Промышленная площадка',
            'промышленная зона': 'Промышленная зона',
            'промышленный парк': 'Промышленный парк',
            'технопарк': 'Технопарк',
            'индустриальный парк': 'Индустриальный парк',
            'индустриальная зона': 'Индустриальная зона',
            'индустриальный комплекс': 'Индустриальный комплекс',
            'индустриальный объект': 'Индустриальный объект',
            'индустриальное здание': 'Индустриальное здание',
            'индустриальное помещение': 'Индустриальное помещение',
            'индустриальная площадка': 'Индустриальная площадка',
            'индустриальная база': 'Индустриальная база',
            'индустриальный терминал': 'Индустриальный терминал',
            'индустриальный центр': 'Индустриальный центр',
            'индустриальный район': 'Индустриальный район',
            'индустриальный город': 'Индустриальный город',
            'индустриальный поселок': 'Индустриальный поселок',
            'индустриальная деревня': 'Индустриальная деревня',
            'индустриальный хутор': 'Индустриальный хутор',
            'индустриальный участок': 'Индустриальный участок',
            'индустриальная земля': 'Индустриальная земля',
            'индустриальный земельный участок': 'Индустриальный земельный участок',
            'индустриальная территория': 'Индустриальная территория',
            'индустриальный район': 'Индустриальный район',
            'индустриальный квартал': 'Индустриальный квартал',
            'индустриальный микрорайон': 'Индустриальный микрорайон',
            'индустриальный поселок': 'Индустриальный поселок',
            'индустриальная деревня': 'Индустриальная деревня',
            'индустриальный хутор': 'Индустриальный хутор',
            'индустриальный участок': 'Индустриальный участок',
            'индустриальная земля': 'Индустриальная земля',
            'индустриальный земельный участок': 'Индустриальный земельный участок',
            'индустриальная территория': 'Индустриальная территория',
            'индустриальный район': 'Индустриальный район',
            'индустриальный квартал': 'Индустриальный квартал',
            'индустриальный микрорайон': 'Индустриальный микрорайон'
        }

        for key, value in property_types.items():
            if key in text:
                return value

        return None

    @staticmethod
    def extract_operation_type_from_text(text: str) -> Optional[str]:
        """
        Извлекает тип операции из текста.

        Args:
            text: Текст

        Returns:
            Optional[str]: Тип операции или None, если тип операции не найден
        """
        if not text:
            return None

        # Очищаем текст
        text = clean_text(text).lower()

        # Словарь типов операций
        operation_types = {
            'продажа': 'Продажа',
            'продам': 'Продажа',
            'продаю': 'Продажа',
            'продается': 'Продажа',
            'продаётся': 'Продажа',
            'продаеться': 'Продажа',
            'продаётся': 'Продажа',
            'продаж': 'Продажа',
            'продаж': 'Продажа',
            'аренда': 'Аренда',
            'сдам': 'Аренда',
            'сдаю': 'Аренда',
            'сдается': 'Аренда',
            'сдаётся': 'Аренда',
            'сдаеться': 'Аренда',
            'сдаётся': 'Аренда',
            'сдача': 'Аренда',
            'сдача': 'Аренда',
            'аренда долгосрочная': 'Аренда долгосрочная',
            'долгосрочная аренда': 'Аренда долгосрочная',
            'аренда посуточно': 'Аренда посуточно',
            'посуточная аренда': 'Аренда посуточно',
            'посуточно': 'Аренда посуточно',
            'аренда почасово': 'Аренда почасово',
            'почасовая аренда': 'Аренда почасово',
            'почасово': 'Аренда почасово',
            'обмен': 'Обмен',
            'меняю': 'Обмен',
            'поменяю': 'Обмен',
            'обменяю': 'Обмен',
            'меняется': 'Обмен',
            'обменивается': 'Обмен'
        }

        for key, value in operation_types.items():
            if key in text:
                return value

        return None

    @staticmethod
    def extract_price_from_text(text: str) -> Tuple[Optional[float], Optional[str]]:
        """
        Извлекает цену и валюту из текста.

        Args:
            text: Текст

        Returns:
            Tuple[Optional[float], Optional[str]]: Цена и валюта или (None, None), если цена не найдена
        """
        if not text:
            return None, None

        # Очищаем текст
        text = clean_text(text)

        # Ищем цену и валюту в тексте
        price_match = re.search(r'([\d\s.,]+)\s*([^\d\s.,]+)', text)
        if not price_match:
            return None, None

        # Очищаем цену от пробелов и заменяем запятую на точку
        price_str = price_match.group(1).strip().replace(' ', '').replace(',', '.')
        currency = price_match.group(2).strip()

        try:
            return float(price_str), currency
        except ValueError:
            return None, None
