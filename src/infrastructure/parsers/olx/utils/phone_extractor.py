"""
Утилиты для извлечения телефонов из объявлений OLX.
"""
import json
import time
from typing import Dict, List, Optional, Union

import requests

from src.domain.value_objects.ad_id import AdId


class OlxPhoneExtractor:
    """
    Класс для извлечения телефонов из объявлений OLX с помощью Zyte API.
    """

    def __init__(self, base_url: str, user_agent: str, zyte_api_key: str, zyte_api_url: Optional[str] = None,
                 timeout: int = 60, retries: int = 3, delay: int = 2):
        """
        Инициализирует экстрактор телефонов.

        Args:
            base_url: Базовый URL OLX
            user_agent: User-Agent для запросов
            zyte_api_key: API ключ Zyte (обязательно)
            zyte_api_url: URL API Zyte (опционально)
            timeout: Таймаут запросов в секундах
            retries: Количество повторных попыток
            delay: Задержка между запросами в секундах
        """
        self.base_url = base_url
        self.user_agent = user_agent
        self.zyte_api_key = zyte_api_key
        self.zyte_api_url = zyte_api_url or 'https://api.zyte.com/v1/extract'
        self.timeout = timeout
        self.retries = retries
        self.delay = delay

        if not self.zyte_api_key:
            raise ValueError("Не указан API ключ Zyte. Парсер работает только с Zyte API.")

    def extract_phones(self, ad_id: Union[str, AdId]) -> List[str]:
        """
        Извлекает телефоны из объявления с помощью Zyte API.

        Args:
            ad_id: ID объявления

        Returns:
            List[str]: Список телефонов
        """
        phones = []

        try:
            # Формируем URL для API телефонов
            phone_api_url = f"{self.base_url}/api/v1/offers/{ad_id}/limited-phones/"

            # Получаем HTML страницы с телефонами через Zyte API
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': f'Basic {self.zyte_api_key}'
            }

            payload = {
                'url': phone_api_url,
                'browserHtml': False,  # Не нужен рендеринг для API запроса
                'httpResponseBody': True,  # Получаем тело ответа
                'httpResponseHeaders': True,  # Получаем заголовки ответа
                'customHttpRequestHeaders': {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }

            for attempt in range(self.retries):
                try:
                    response = requests.post(
                        self.zyte_api_url,
                        headers=headers,
                        json=payload,
                        timeout=self.timeout
                    )
                    response.raise_for_status()
                    zyte_data = response.json()

                    # Проверяем, что есть тело ответа
                    if 'httpResponseBody' in zyte_data:
                        # Парсим JSON из тела ответа
                        try:
                            data = json.loads(zyte_data['httpResponseBody'])
                            # Извлекаем телефоны
                            if 'data' in data and 'phones' in data['data']:
                                for phone_data in data['data']['phones']:
                                    if 'number' in phone_data:
                                        phones.append(phone_data['number'])
                        except json.JSONDecodeError as e:
                            print(f"Ошибка при парсинге JSON из ответа API телефонов: {e}")

                    break
                except (requests.RequestException, json.JSONDecodeError) as e:
                    print(f"Ошибка при получении телефонов для объявления {ad_id} через Zyte API (попытка {attempt + 1}/{self.retries}): {e}")
                    if attempt < self.retries - 1:
                        time.sleep(self.delay * (attempt + 1))

        except Exception as e:
            print(f"Ошибка при извлечении телефонов для объявления {ad_id}: {e}")

        return phones

    def format_phone(self, phone: str) -> str:
        """
        Форматирует телефонный номер.

        Args:
            phone: Телефонный номер

        Returns:
            str: Отформатированный телефонный номер
        """
        # Удаляем все нецифровые символы
        digits = ''.join(filter(str.isdigit, phone))

        # Если номер начинается с 0, добавляем код страны
        if digits.startswith('0') and len(digits) == 10:
            digits = '38' + digits

        # Если номер не содержит код страны, добавляем его
        if len(digits) == 10:
            digits = '38' + digits

        # Форматируем номер
        if len(digits) == 12:
            return f"+{digits[0:2]} ({digits[2:5]}) {digits[5:8]}-{digits[8:10]}-{digits[10:12]}"

        return phone
