"""
Специфичная обработка ошибок OLX.
"""
import logging
import re
import time
import functools
from typing import Callable, Optional, TypeVar, Any

import requests
from bs4 import BeautifulSoup

T = TypeVar('T')


class OlxError(Exception):
    """
    Базовый класс для ошибок OLX.
    """
    pass


class OlxParsingError(OlxError):
    """
    Ошибка парсинга OLX.
    """
    pass


class OlxNetworkError(OlxError):
    """
    Ошибка сети OLX.
    """
    pass


class OlxNotFoundError(OlxError):
    """
    Ошибка "не найдено" OLX.
    """
    pass


class OlxAccessDeniedError(OlxError):
    """
    Ошибка доступа OLX.
    """
    pass


class OlxRateLimitError(OlxError):
    """
    Ошибка превышения лимита запросов OLX.
    """
    pass


class ErrorHandling:
    """
    Класс для обработки ошибок OLX.
    """

    @staticmethod
    def check_response_for_errors(response: requests.Response) -> None:
        """
        Проверяет ответ на наличие ошибок.

        Args:
            response: Ответ

        Raises:
            OlxNotFoundError: Если страница не найдена
            OlxAccessDeniedError: Если доступ запрещен
            OlxRateLimitError: Если превышен лимит запросов
            OlxNetworkError: Если произошла ошибка сети
        """
        # Проверяем статус код
        if response.status_code == 404:
            raise OlxNotFoundError("Страница не найдена")

        if response.status_code == 403:
            raise OlxAccessDeniedError("Доступ запрещен")

        if response.status_code == 429:
            raise OlxRateLimitError("Превышен лимит запросов")

        if response.status_code is not None and response.status_code >= 400:
            raise OlxNetworkError(f"Ошибка сети: {response.status_code}")

        # Проверяем содержимое ответа
        if 'captcha' in response.text.lower():
            raise OlxAccessDeniedError("Требуется капча")

        if 'blocked' in response.text.lower() or 'заблокирован' in response.text.lower():
            raise OlxAccessDeniedError("IP заблокирован")

        if 'not found' in response.text.lower() or 'не найдено' in response.text.lower():
            raise OlxNotFoundError("Страница не найдена")

    @staticmethod
    def check_html_for_errors(html: str) -> None:
        """
        Проверяет HTML на наличие ошибок.

        Args:
            html: HTML

        Raises:
            OlxNotFoundError: Если страница не найдена
            OlxAccessDeniedError: Если доступ запрещен
            OlxRateLimitError: Если превышен лимит запросов
            OlxParsingError: Если произошла ошибка парсинга
        """
        # Проверяем на наличие ошибок
        if 'captcha' in html.lower():
            raise OlxAccessDeniedError("Требуется капча")

        if 'blocked' in html.lower() or 'заблокирован' in html.lower():
            raise OlxAccessDeniedError("IP заблокирован")

        if 'not found' in html.lower() or 'не найдено' in html.lower():
            raise OlxNotFoundError("Страница не найдена")

        if 'rate limit' in html.lower() or 'превышен лимит' in html.lower():
            raise OlxRateLimitError("Превышен лимит запросов")

        # Проверяем на наличие ключевых элементов
        soup = BeautifulSoup(html, 'html.parser')

        # Для страницы объявления
        if not soup.select_one('h1.css-1soizd2') and not soup.select_one('div[data-cy="l-card"]'):
            raise OlxParsingError("Не найдены ключевые элементы на странице")

    @staticmethod
    def retry(func: Callable[..., T], retries: int = 3, delay: int = 2,
             logger: Optional[logging.Logger] = None) -> T:
        """
        Повторяет выполнение функции при возникновении ошибок.

        Args:
            func: Функция для выполнения
            retries: Количество повторных попыток
            delay: Задержка между попытками в секундах
            logger: Логгер

        Returns:
            T: Результат выполнения функции

        Raises:
            OlxError: Если все попытки завершились ошибкой
        """
        last_error = None

        for attempt in range(retries):
            try:
                return func()
            except (OlxNetworkError, OlxRateLimitError) as e:
                last_error = e
                if logger:
                    logger.warning(f"Ошибка при выполнении функции (попытка {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(delay * (attempt + 1))
            except (OlxNotFoundError, OlxAccessDeniedError, OlxParsingError) as e:
                # Эти ошибки не стоит повторять
                raise e
            except Exception as e:
                last_error = e
                if logger:
                    logger.error(f"Неожиданная ошибка при выполнении функции (попытка {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(delay * (attempt + 1))

        if last_error:
            raise last_error

        raise OlxError("Все попытки выполнения функции завершились ошибкой")

    @staticmethod
    def is_captcha_page(html: str) -> bool:
        """
        Проверяет, является ли страница страницей с капчей.

        Args:
            html: HTML

        Returns:
            bool: True, если страница с капчей, иначе False
        """
        return 'captcha' in html.lower()

    @staticmethod
    def is_not_found_page(html: str) -> bool:
        """
        Проверяет, является ли страница страницей "не найдено".

        Args:
            html: HTML

        Returns:
            bool: True, если страница "не найдено", иначе False
        """
        return 'not found' in html.lower() or 'не найдено' in html.lower()

    @staticmethod
    def is_blocked_page(html: str) -> bool:
        """
        Проверяет, является ли страница страницей блокировки.

        Args:
            html: HTML

        Returns:
            bool: True, если страница блокировки, иначе False
        """
        return 'blocked' in html.lower() or 'заблокирован' in html.lower()

    @staticmethod
    def is_rate_limit_page(html: str) -> bool:
        """
        Проверяет, является ли страница страницей превышения лимита запросов.

        Args:
            html: HTML

        Returns:
            bool: True, если страница превышения лимита запросов, иначе False
        """
        return 'rate limit' in html.lower() or 'превышен лимит' in html.lower()


def safe_extraction(default_value: Any = None):
    """
    Декоратор для безопасного извлечения данных из HTML.
    В случае ошибки возвращает значение по умолчанию.

    Args:
        default_value: Значение по умолчанию, которое будет возвращено в случае ошибки

    Returns:
        Callable: Декоратор
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"Ошибка при извлечении данных: {e}")
                return default_value
        return wrapper
    return decorator
