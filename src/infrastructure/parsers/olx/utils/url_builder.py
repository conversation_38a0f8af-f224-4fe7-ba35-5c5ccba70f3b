"""
Утилиты для построения URL для OLX.
"""
from typing import Dict, Optional
from urllib.parse import parse_qs, urlencode, urlparse, urlunparse


def add_query_params(url: str, params: Dict[str, str]) -> str:
    """
    Добавляет параметры запроса к URL.

    Args:
        url: Исходный URL
        params: Словарь с параметрами запроса

    Returns:
        str: URL с добавленными параметрами
    """
    # Парсим URL
    parsed_url = urlparse(url)

    # Получаем текущие параметры запроса
    query_dict = parse_qs(parsed_url.query)

    # Добавляем новые параметры
    for key, value in params.items():
        query_dict[key] = [value]

    # Формируем новую строку запроса
    new_query = urlencode(query_dict, doseq=True)

    # Формируем новый URL
    return urlunparse((
        parsed_url.scheme,
        parsed_url.netloc,
        parsed_url.path,
        parsed_url.params,
        new_query,
        parsed_url.fragment
    ))


def build_ad_url(base_url: str, ad_id: str) -> str:
    """
    Формирует URL объявления.

    Args:
        base_url: Базовый URL
        ad_id: ID объявления

    Returns:
        str: URL объявления
    """
    return f"{base_url}/uk/obyavlenie/{ad_id}.html"


def build_phone_api_url(base_url: str, ad_id: str) -> str:
    """
    Формирует URL для API телефонов.

    Args:
        base_url: Базовый URL
        ad_id: ID объявления

    Returns:
        str: URL для API телефонов
    """
    return f"{base_url}/api/v1/offers/{ad_id}/limited-phones/"


def build_category_url(base_url: str, category_path: str, params: Optional[Dict[str, str]] = None) -> str:
    """
    Формирует URL категории.

    Args:
        base_url: Базовый URL
        category_path: Путь категории
        params: Параметры запроса (опционально)

    Returns:
        str: URL категории
    """
    # Формируем URL категории
    if category_path.startswith('http'):
        category_url = category_path
    else:
        category_url = f"{base_url}/{category_path.lstrip('/')}"

    # Добавляем параметры запроса
    if params:
        category_url = add_query_params(category_url, params)

    return category_url


def build_page_url(url: str, page: int) -> str:
    """
    Формирует URL страницы с пагинацией.

    Args:
        url: Исходный URL
        page: Номер страницы

    Returns:
        str: URL страницы
    """
    return add_query_params(url, {'page': str(page)})
