"""
Пакет утилит для парсинга OLX.
"""
from src.infrastructure.parsers.olx.utils.area_utils import AreaUtils
from src.infrastructure.parsers.olx.utils.building_utils import BuildingUtils
from src.infrastructure.parsers.olx.utils.contact_utils import ContactUtils
from src.infrastructure.parsers.olx.utils.date_utils import DateUtils
from src.infrastructure.parsers.olx.utils.error_handling import (
    ErrorHandling, OlxAccessDeniedError, OlxError, OlxNetworkError,
    OlxNotFoundError, OlxParsingError, OlxRateLimitError
)
# Удалено: from src.infrastructure.parsers.olx.utils.html_processor import HtmlProcessor
from src.infrastructure.parsers.olx.utils.html_utils import (
    clean_text, extract_meta_property, extract_number,
    extract_url_param, extract_url_path_segment,
    get_description, get_images, get_pagination_info, get_parameters,
    get_title, make_absolute_url, parse_html
)
from src.infrastructure.parsers.olx.utils.id_extractor import IdExtractor
from src.infrastructure.parsers.olx.utils.json_ld_processor import JsonLdProcessor
from src.infrastructure.parsers.olx.utils.location_utils import LocationUtils
# Удалено: from src.infrastructure.parsers.olx.utils.olx_html_parser import OlxHtmlParser
from src.infrastructure.parsers.olx.utils.phone_extractor import OlxPhoneExtractor
from src.infrastructure.parsers.olx.utils.price_utils import PriceUtils
from src.infrastructure.parsers.olx.utils.room_utils import RoomUtils
from src.infrastructure.parsers.olx.utils.tag_utils import TagUtils
from src.infrastructure.parsers.olx.utils.text_processors import TextProcessors
# Удалено: from src.infrastructure.parsers.olx.utils.url_builder import OlxUrlBuilder
from src.infrastructure.parsers.olx.utils.url_builder import (
    add_query_params, build_ad_url, build_category_url, build_page_url,
    build_phone_api_url
)

__all__ = [
    # Базовые утилиты
    'clean_text',
    'extract_meta_property',
    'extract_number',
    'extract_url_param',
    'extract_url_path_segment',
    'get_description',
    'get_images',
    'get_pagination_info',
    'get_parameters',
    'get_title',
    'make_absolute_url',
    'parse_html',
    'add_query_params',
    'build_ad_url',
    'build_category_url',
    'build_page_url',
    'build_phone_api_url',

    # Классы утилит
    'AreaUtils',
    'BuildingUtils',
    'ContactUtils',
    'DateUtils',
    'ErrorHandling',
    # Удалено: 'HtmlProcessor',
    'IdExtractor',
    'JsonLdProcessor',
    'LocationUtils',
    # Удалено: 'OlxHtmlParser',
    'OlxPhoneExtractor',
    'PriceUtils',
    'RoomUtils',
    'TagUtils',
    'TextProcessors',
    # Удалено: 'OlxUrlBuilder',

    # Классы ошибок
    'OlxError',
    'OlxParsingError',
    'OlxNetworkError',
    'OlxNotFoundError',
    'OlxAccessDeniedError',
    'OlxRateLimitError'
]