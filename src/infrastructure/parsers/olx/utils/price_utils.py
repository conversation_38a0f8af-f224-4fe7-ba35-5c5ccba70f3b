"""
Обработка цен OLX.
"""
import json
import logging
import re
from typing import Dict, Optional, Tuple, Union

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text

# Настройка логгера
logger = logging.getLogger(__name__)


class PriceUtils:
    """
    Класс для обработки цен OLX.
    """

    @staticmethod
    def extract_price_from_text(text: str) -> Tuple[Optional[float], Optional[str]]:
        """
        Извлекает цену и валюту из текста.

        Args:
            text: Текст с ценой

        Returns:
            Tuple[Optional[float], Optional[str]]: Цена и валюта или (None, None), если цена не найдена
        """
        try:
            if not text:
                return None, None

            # Очищаем текст
            text = clean_text(text)
            text_lower = text.lower()

            # Проверяем специальные значения
            if any(word in text_lower for word in ["безкоштовно", "бесплатно", "free"]):
                return 0.0, "UAH"

            if any(word in text_lower for word in ["договірна", "договорная", "уточняйте", "торг"]):
                return None, None

            # Ищем цену и валюту в тексте
            price_match = re.search(r'([\d\s.,]+)\s*([^\d\s.,]+)', text)
            if not price_match:
                # Пробуем альтернативный формат с валютой перед ценой
                price_match = re.search(r'([^\d\s.,]+)\s*([\d\s.,]+)', text)
                if price_match:
                    # Меняем местами группы
                    currency_symbol = price_match.group(1).strip()
                    price_str = price_match.group(2).strip().replace(' ', '').replace(',', '.')
                else:
                    # Если не нашли валюту, пробуем найти только число
                    price_match = re.search(r'([\d\s.,]+)', text)
                    if not price_match:
                        return None, None
                    price_str = price_match.group(1).strip().replace(' ', '').replace(',', '.')
                    # По умолчанию считаем, что валюта - гривны
                    currency_symbol = "грн"
            else:
                # Очищаем цену от пробелов и заменяем запятую на точку
                price_str = price_match.group(1).strip().replace(' ', '').replace(',', '.')
                currency_symbol = price_match.group(2).strip()

            # Нормализуем валюту
            currency_map = {
                '$': 'USD',
                '€': 'EUR',
                'грн': 'UAH',
                'грн.': 'UAH',
                'гривен': 'UAH',
                'гривня': 'UAH',
                'гривень': 'UAH',
                'доллар': 'USD',
                'долларов': 'USD',
                'евро': 'EUR',
                'uah': 'UAH',
                'usd': 'USD',
                'eur': 'EUR',
                '₴': 'UAH'  # Символ гривны
            }

            currency = currency_map.get(currency_symbol.lower(), currency_symbol)

            # Если валюта не соответствует допустимым, используем заглушку
            if currency not in ['USD', 'EUR', 'UAH']:
                if '$' in currency_symbol:
                    currency = 'USD'
                elif '€' in currency_symbol:
                    currency = 'EUR'
                elif '₴' in currency_symbol:
                    currency = 'UAH'
                else:
                    currency = 'UAH'  # По умолчанию используем гривны

            try:
                price = float(price_str)
                # Проверяем валидность цены
                if price <= 0 and price != 0.0:  # Разрешаем только точный ноль (бесплатно)
                    return None, None
                return price, currency
            except ValueError:
                logger.warning(f"Не удалось преобразовать цену '{price_str}' в число")
                return None, None
        except Exception as e:
            logger.error(f"Ошибка при извлечении цены из текста '{text}': {e}")
            return None, None

    @staticmethod
    def extract_price_from_html(html: str) -> Tuple[Optional[float], Optional[str]]:
        """
        Извлекает цену и валюту из HTML.

        Args:
            html: HTML строка

        Returns:
            Tuple[Optional[float], Optional[str]]: Цена и валюта или (None, None), если цена не найдена
        """
        try:
            # Создаем объект BeautifulSoup и используем метод extract_price_from_soup
            soup = BeautifulSoup(html, 'html.parser')
            return PriceUtils.extract_price_from_soup(soup)
        except Exception as e:
            logger.error(f"Ошибка при извлечении цены из HTML: {e}")
            return None, None

    @staticmethod
    def extract_price_from_soup(soup: BeautifulSoup) -> Tuple[Optional[float], Optional[str]]:
        """
        Извлекает цену и валюту из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Tuple[Optional[float], Optional[str]]: Цена и валюта или (None, None), если цена не найдена
        """
        try:
            # 1. Сначала пробуем извлечь цену из JSON-LD
            json_ld_scripts = soup.find_all('script', {'type': 'application/ld+json'})
            for script in json_ld_scripts:
                try:
                    if script.string:
                        json_ld_data = json.loads(script.string)

                        # Проверяем разные форматы JSON-LD
                        if 'offers' in json_ld_data:
                            offers = json_ld_data['offers']
                            if isinstance(offers, dict):
                                price = None
                                currency = None

                                if 'price' in offers:
                                    try:
                                        price = float(offers['price'])
                                    except (ValueError, TypeError):
                                        pass

                                if 'priceCurrency' in offers:
                                    currency = offers['priceCurrency']

                                if price is not None and currency is not None:
                                    logger.debug(f"Извлечена цена {price} {currency} из JSON-LD")
                                    return price, currency

                        # Проверяем альтернативный формат JSON-LD
                        if 'priceSpecification' in json_ld_data:
                            price_spec = json_ld_data['priceSpecification']
                            if isinstance(price_spec, dict):
                                price = None
                                currency = None

                                if 'price' in price_spec:
                                    try:
                                        price = float(price_spec['price'])
                                    except (ValueError, TypeError):
                                        pass

                                if 'priceCurrency' in price_spec:
                                    currency = price_spec['priceCurrency']

                                if price is not None and currency is not None:
                                    logger.debug(f"Извлечена цена {price} {currency} из JSON-LD (priceSpecification)")
                                    return price, currency
                except (json.JSONDecodeError, AttributeError) as e:
                    logger.warning(f"Ошибка при парсинге JSON-LD: {e}")

            # 2. Пробуем разные селекторы для цены
            selectors = [
                'h3.css-fqcbii',  # Новый селектор из примера
                'div[data-testid="ad-price-container"] h3',  # Новый селектор из примера
                'div[data-cy="ad-price-container"] h3',  # Еще один вариант селектора
                'h3.css-ddweki',  # Старый селектор
                'h3.css-*',  # Любой h3 с классом, начинающимся с css-
                'div[data-testid="ad-price-container"]',  # По data-атрибуту
                'div.css-* > h3',  # Вложенный h3
                'div.price-label',  # Старый формат
                'div.price'  # Еще один возможный селектор
            ]

            for selector in selectors:
                try:
                    price_element = soup.select_one(selector)
                    if price_element:
                        price_text = price_element.text
                        # Извлекаем цену и валюту
                        price, currency = PriceUtils.extract_price_from_text(price_text)
                        if price is not None and currency is not None:
                            logger.debug(f"Извлечена цена {price} {currency} из селектора {selector}")
                            return price, currency
                except Exception as e:
                    logger.debug(f"Ошибка при извлечении цены из селектора {selector}: {e}")
                    continue

            # 3. Если не нашли по селекторам, пробуем найти через мета-теги
            try:
                from src.infrastructure.parsers.olx.utils.html_utils import extract_meta_property
                meta_price = extract_meta_property(soup, 'product:price:amount')
                meta_currency = extract_meta_property(soup, 'product:price:currency')

                if meta_price and meta_currency:
                    try:
                        price = float(meta_price)
                        logger.debug(f"Извлечена цена {price} {meta_currency} из мета-тегов")
                        return price, meta_currency
                    except ValueError as e:
                        logger.warning(f"Ошибка при преобразовании цены из мета-тегов: {e}")
            except Exception as e:
                logger.warning(f"Ошибка при извлечении цены из мета-тегов: {e}")

            # 4. Если все методы не сработали, пробуем найти цену в описании
            try:
                from src.infrastructure.parsers.olx.utils.html_utils import get_description
                description = get_description(soup)
                if description:
                    # Ищем цену в описании
                    price_match = re.search(r'(\d[\d\s.,]*\d)\s*([\$€грн₴]|USD|EUR|UAH)', description)
                    if price_match:
                        price_str = price_match.group(1).strip().replace(' ', '').replace(',', '.')
                        currency_str = price_match.group(2).strip()

                        # Нормализуем валюту
                        currency_map = {
                            '$': 'USD',
                            '€': 'EUR',
                            'грн': 'UAH',
                            'грн.': 'UAH',
                            '₴': 'UAH'  # Символ гривны
                        }
                        currency = currency_map.get(currency_str, currency_str)

                        try:
                            price = float(price_str)
                            logger.debug(f"Извлечена цена {price} {currency} из описания")
                            return price, currency
                        except ValueError as e:
                            logger.warning(f"Ошибка при преобразовании цены из описания: {e}")
            except Exception as e:
                logger.warning(f"Ошибка при извлечении цены из описания: {e}")

            logger.warning("Не удалось извлечь цену из страницы")
            return None, None
        except Exception as e:
            logger.error(f"Ошибка при извлечении цены из страницы: {e}")
            return None, None

    @staticmethod
    def format_price(price: Union[float, int], currency: str) -> str:
        """
        Форматирует цену и валюту в строку.

        Args:
            price: Цена
            currency: Валюта

        Returns:
            str: Отформатированная строка с ценой и валютой
        """
        try:
            # Проверяем специальные случаи
            if price == 0:
                return "Бесплатно"

            # Форматируем цену с разделителями тысяч
            if isinstance(price, int) or (isinstance(price, float) and price.is_integer()):
                formatted_price = f"{int(price):,}".replace(',', ' ')
            else:
                formatted_price = f"{price:,.2f}".replace(',', ' ').replace('.', ',')

            # Нормализуем валюту
            currency = currency.upper() if currency else 'UAH'

            # Форматируем в зависимости от валюты
            if currency == 'USD':
                return f"${formatted_price}"
            elif currency == 'EUR':
                return f"€{formatted_price}"
            elif currency == 'UAH':
                return f"{formatted_price} ГРН"
            else:
                return f"{formatted_price} {currency}"
        except Exception as e:
            logger.error(f"Ошибка при форматировании цены {price} {currency}: {e}")
            # В случае ошибки возвращаем простой формат
            return f"{price} {currency}"

    @staticmethod
    def convert_currency(price: float, from_currency: str, to_currency: str,
                        exchange_rates: Dict[str, float]) -> Optional[float]:
        """
        Конвертирует цену из одной валюты в другую.

        Args:
            price: Цена
            from_currency: Исходная валюта
            to_currency: Целевая валюта
            exchange_rates: Словарь с курсами валют

        Returns:
            Optional[float]: Сконвертированная цена или None, если конвертация невозможна
        """
        if from_currency == to_currency:
            return price

        if from_currency not in exchange_rates or to_currency not in exchange_rates:
            return None

        # Конвертируем через базовую валюту (обычно USD)
        from_rate = exchange_rates[from_currency]
        to_rate = exchange_rates[to_currency]

        return price * (to_rate / from_rate)

    @staticmethod
    def normalize_currency(currency: str) -> str:
        """
        Нормализует название валюты.

        Args:
            currency: Название валюты

        Returns:
            str: Нормализованное название валюты
        """
        currency = currency.upper().strip()

        # Словарь соответствий
        currency_map = {
            'ГРН': 'UAH',
            'ГРН.': 'UAH',
            'ГРВ': 'UAH',
            'ГРВ.': 'UAH',
            'Р': 'RUB',
            'РУБ': 'RUB',
            'РУБ.': 'RUB',
            '$': 'USD',
            'ДОЛЛ': 'USD',
            'ДОЛЛ.': 'USD',
            'ДОЛЛАР': 'USD',
            'ДОЛЛАРЫ': 'USD',
            '€': 'EUR',
            'ЕВРО': 'EUR',
            'Є': 'EUR'
        }

        return currency_map.get(currency, currency)

    @staticmethod
    def is_negotiable(text: str) -> bool:
        """
        Проверяет, указано ли, что цена договорная.

        Args:
            text: Текст с ценой

        Returns:
            bool: True, если цена договорная, иначе False
        """
        text = text.lower()

        negotiable_phrases = [
            'договорная',
            'договорная цена',
            'цена договорная',
            'торг',
            'торг уместен',
            'возможен торг'
        ]

        return any(phrase in text for phrase in negotiable_phrases)
