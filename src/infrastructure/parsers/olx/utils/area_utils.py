"""
Обработка площади OLX.
Включает функции для извлечения и валидации:
- общей площади
- жилой площади
- площади кухни
- площади участка
"""
import logging
import re
from typing import Dict, Optional, Tuple

from bs4 import BeautifulSoup

from src.infrastructure.parsers.olx.utils.html_utils import clean_text

# Настройка логгера
logger = logging.getLogger(__name__)

# Шаблоны для поиска площадей
TOTAL_AREA_PATTERNS = [
    r'(?:Общая|Общ)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)',
    r'(?:Площадь)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)',
    r'(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)'
]

LIVING_AREA_PATTERNS = [
    r'(?:Жилая|Жил)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)',
    r'(?:Жилое)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)'
]

KITCHEN_AREA_PATTERNS = [
    r'(?:Кухня)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)',
    r'(?:Площадь кухни)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:м²|м2|кв\.?м|м\s*кв)'
]

LAND_AREA_PATTERNS = [
    r'(?:Участок)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:сот|соток|сотки|га|м²|м2|кв\.?м|м\s*кв)',
    r'(?:Площадь участка)[^\d]+(\d+(?:[.,]\d+)?)\s*(?:сот|соток|сотки|га|м²|м2|кв\.?м|м\s*кв)'
]


class AreaUtils:
    """
    Класс для обработки площади OLX.
    """

    @staticmethod
    def extract_area_from_text(text: str) -> Optional[float]:
        """
        Извлекает площадь из текста.

        Args:
            text: Текст с площадью

        Returns:
            Optional[float]: Площадь или None, если площадь не найдена
        """
        area_str = ""
        try:
            if not text:
                return None

            # Очищаем текст
            text = clean_text(text)

            # Ищем площадь в тексте с единицей измерения
            area_match = re.search(r'([\d.,]+)\s*(?:м²|кв\.м|м2|кв\.м\.|м\s*кв)', text)
            if not area_match:
                # Пробуем найти просто число
                area_match = re.search(r'([\d.,]+)', text)
                if not area_match:
                    return None

            # Очищаем площадь от запятых
            area_str = area_match.group(1).replace(',', '.')

            # Преобразуем в число
            area = float(area_str)

            # Проверяем валидность площади
            if area <= 0 or area > 10000:  # Максимальная площадь 10000 м² (1 гектар)
                logger.warning(f"Недопустимое значение площади: {area} м²")
                return None

            # Округляем до 2 знаков после запятой
            return round(area, 2)
        except ValueError:
            logger.warning(f"Не удалось преобразовать площадь '{area_str}' в число")
            return None
        except Exception as e:
            logger.error(f"Ошибка при извлечении площади из текста '{text}': {e}")
            return None

    @staticmethod
    def extract_area_info_from_text(text: str) -> Dict[str, Optional[float]]:
        """
        Извлекает информацию о площадях из текста.

        Args:
            text: Текст для анализа

        Returns:
            Dict с ключами 'total_area', 'living_area', 'kitchen_area', 'land_area'.
            Значения могут быть None.
        """
        result: Dict[str, Optional[float]] = {
            'total_area': None,
            'living_area': None,
            'kitchen_area': None,
            'land_area': None
        }

        if not text:
            return result

        # Очищаем текст
        text = clean_text(text)

        # Поиск общей площади
        for pattern in TOTAL_AREA_PATTERNS:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    area = float(match.group(1).replace(',', '.'))
                    if 0 < area <= 1000:  # Проверка валидности
                        result['total_area'] = round(area, 2)
                        break
                except (ValueError, IndexError):
                    continue

        # Поиск жилой площади
        for pattern in LIVING_AREA_PATTERNS:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    area = float(match.group(1).replace(',', '.'))
                    if 0 < area <= 500:  # Проверка валидности
                        result['living_area'] = round(area, 2)
                        break
                except (ValueError, IndexError):
                    continue

        # Поиск площади кухни
        for pattern in KITCHEN_AREA_PATTERNS:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    area = float(match.group(1).replace(',', '.'))
                    if 0 < area <= 100:  # Проверка валидности
                        result['kitchen_area'] = round(area, 2)
                        break
                except (ValueError, IndexError):
                    continue

        # Поиск площади участка
        for pattern in LAND_AREA_PATTERNS:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    area_str = match.group(1).replace(',', '.')
                    area = float(area_str)

                    # Проверяем единицу измерения
                    unit_match = re.search(r'(?:сот|соток|сотки|га)', match.group(0))
                    if unit_match:
                        unit = unit_match.group(0)
                        # Конвертируем в квадратные метры
                        if unit.startswith('сот'):  # Сотки
                            area *= 100
                        elif unit == 'га':  # Гектары
                            area *= 10000

                    if 0 < area <= 100000:  # Проверка валидности (максимум 10 гектаров)
                        result['land_area'] = round(area, 2)
                        break
                except (ValueError, IndexError):
                    continue

        return result

    @staticmethod
    def extract_areas_from_parameters(params: Dict[str, str]) -> Dict[str, float]:
        """
        Извлекает площади из параметров объявления.

        Args:
            params: Словарь с параметрами объявления

        Returns:
            Dict[str, float]: Словарь с площадями
        """
        result = {}

        # Общая площадь
        if 'Общая площадь' in params:
            total_area = AreaUtils.extract_area_from_text(params['Общая площадь'])
            if total_area is not None:
                result['total_area'] = total_area

        # Жилая площадь
        if 'Жилая площадь' in params:
            living_area = AreaUtils.extract_area_from_text(params['Жилая площадь'])
            if living_area is not None:
                result['living_area'] = living_area

        # Площадь кухни
        if 'Площадь кухни' in params:
            kitchen_area = AreaUtils.extract_area_from_text(params['Площадь кухни'])
            if kitchen_area is not None:
                result['kitchen_area'] = kitchen_area

        # Площадь участка
        if 'Площадь участка' in params:
            land_area = AreaUtils.extract_area_from_text(params['Площадь участка'])
            if land_area is not None:
                result['land_area'] = land_area

        return result

    @staticmethod
    def extract_areas_from_soup(soup: BeautifulSoup) -> Dict[str, float]:
        """
        Извлекает площади из объекта BeautifulSoup.

        Args:
            soup: Объект BeautifulSoup

        Returns:
            Dict[str, float]: Словарь с площадями
        """
        result: Dict[str, float] = {}

        try:
            # Используем функцию из html_utils для получения параметров
            from src.infrastructure.parsers.olx.utils.html_utils import get_parameters
            params = get_parameters(soup)

            # Извлекаем площади из параметров
            result = AreaUtils.extract_areas_from_parameters(params)

            # Пробуем найти в заголовке
            from src.infrastructure.parsers.olx.utils.html_utils import get_title
            title = get_title(soup)
            if title:
                # Используем новый метод для извлечения площадей из заголовка
                title_areas = AreaUtils.extract_area_info_from_text(title)
                # Добавляем найденные площади в результат
                for key, value in title_areas.items():
                    if value is not None and key not in result:
                        result[key] = value

            # Пробуем найти в описании
            from src.infrastructure.parsers.olx.utils.html_utils import get_description
            description = get_description(soup)
            if description:
                # Используем новый метод для извлечения всех площадей из текста
                desc_areas = AreaUtils.extract_area_info_from_text(description)
                # Добавляем найденные площади в результат
                for key, value in desc_areas.items():
                    if value is not None and key not in result:
                        result[key] = value

        except Exception as e:
            logger.error(f"Ошибка при извлечении площадей из объекта BeautifulSoup: {e}")

        return result

    @staticmethod
    def validate_area_info(
        total_area: Optional[float],
        living_area: Optional[float],
        kitchen_area: Optional[float],
        land_area: Optional[float]
    ) -> bool:
        """
        Проверяет корректность информации о площадях.

        Args:
            total_area: Общая площадь
            living_area: Жилая площадь
            kitchen_area: Площадь кухни
            land_area: Площадь участка

        Returns:
            True если данные корректны, False иначе
        """
        # Проверяем что все площади положительные
        areas = [a for a in [total_area, living_area, kitchen_area, land_area] if a is not None]
        if any(a <= 0 for a in areas):
            return False

        # Проверяем что жилая площадь меньше общей
        if total_area and living_area and living_area >= total_area:
            return False

        # Проверяем что площадь кухни меньше общей
        if total_area and kitchen_area and kitchen_area >= total_area:
            return False

        # Проверяем разумные пределы площадей (в квадратных метрах)
        if total_area and total_area > 1000:  # Максимум 1000 м²
            return False

        if living_area and living_area > 500:  # Максимум 500 м²
            return False

        if kitchen_area and kitchen_area > 100:  # Максимум 100 м²
            return False

        if land_area and land_area > 100000:  # Максимум 10 гектаров (100000 м²)
            return False

        return True

    @staticmethod
    def normalize_area(area: Optional[float]) -> Optional[float]:
        """
        Нормализует значение площади.

        Args:
            area: Площадь для нормализации

        Returns:
            Нормализованное значение площади или None
        """
        if area is None:
            return None

        # Округляем до 2 знаков после запятой
        return round(area, 2)

    @staticmethod
    def format_area(area: float, unit: str = 'м²', convert_to_sotki: bool = False) -> str:
        """
        Форматирует площадь.

        Args:
            area: Площадь
            unit: Единица измерения (по умолчанию м²)
            convert_to_sotki: Конвертировать ли в сотки для больших площадей

        Returns:
            str: Отформатированная площадь
        """
        try:
            # Конвертируем в сотки для больших площадей участков
            if convert_to_sotki and area >= 100 and unit == 'м²':
                area = area / 100  # Переводим в сотки
                unit = 'сот.'

            # Конвертируем в гектары для очень больших площадей
            if convert_to_sotki and area >= 100 and unit == 'сот.':
                area = area / 100  # Переводим в гектары
                unit = 'га'

            # Округляем до целого, если площадь целая
            if area.is_integer():
                return f"{int(area)} {unit}"
            else:
                # Округляем до 1 знака после запятой и заменяем точку на запятую
                return f"{area:.1f} {unit}".replace('.', ',')
        except Exception as e:
            logger.error(f"Ошибка при форматировании площади {area}: {e}")
            return f"{area} {unit}"

    @staticmethod
    def extract_land_area(text: str) -> Tuple[Optional[float], Optional[str]]:
        """
        Извлекает площадь участка и единицу измерения из текста.

        Args:
            text: Текст с площадью участка

        Returns:
            Tuple[Optional[float], Optional[str]]: Площадь участка и единица измерения или (None, None), если площадь не найдена
        """
        if not text:
            return None, None

        # Очищаем текст
        text = clean_text(text)

        # Ищем площадь и единицу измерения в тексте
        area_match = re.search(r'([\d.,]+)\s*(сот(?:ок|ки)?|га|м²|кв\.м|м2|кв\.м\.)', text)
        if not area_match:
            # Пробуем найти просто число
            area_match = re.search(r'([\d.,]+)', text)
            if not area_match:
                return None, None

            # Если единица измерения не указана, предполагаем сотки
            area_str = area_match.group(1).replace(',', '.')
            unit = 'сот'
        else:
            area_str = area_match.group(1).replace(',', '.')
            unit = area_match.group(2)

        try:
            area = float(area_str)

            # Нормализуем единицу измерения
            if unit.startswith('сот'):
                unit = 'сот'
            elif unit == 'га':
                unit = 'га'
            else:
                unit = 'м²'

            return area, unit
        except ValueError:
            return None, None

    @staticmethod
    def convert_land_area(area: float, from_unit: str, to_unit: str) -> float:
        """
        Конвертирует площадь участка из одной единицы измерения в другую.

        Args:
            area: Площадь участка
            from_unit: Исходная единица измерения
            to_unit: Целевая единица измерения

        Returns:
            float: Сконвертированная площадь участка
        """
        # Коэффициенты конвертации
        conversion_factors = {
            'м²': {
                'м²': 1,
                'сот': 0.01,
                'га': 0.0001
            },
            'сот': {
                'м²': 100,
                'сот': 1,
                'га': 0.01
            },
            'га': {
                'м²': 10000,
                'сот': 100,
                'га': 1
            }
        }

        if from_unit not in conversion_factors or to_unit not in conversion_factors[from_unit]:
            return area

        return area * conversion_factors[from_unit][to_unit]
