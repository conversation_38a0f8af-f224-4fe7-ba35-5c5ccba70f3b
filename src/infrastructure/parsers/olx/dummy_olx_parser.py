"""
Заглушка для парсера OLX.
"""
import logging
from typing import Dict, List, Optional

from src.application.interfaces.parser import IParser
from src.domain.entities.property import Property
from src.domain.value_objects.ad_id import AdId


class DummyOlxParser(IParser):
    """
    Заглушка для парсера OLX.
    """

    def __init__(self, config: Optional[Dict] = None, logger: Optional[logging.Logger] = None):
        """
        Инициализирует заглушку парсера.

        Args:
            config: Конфигурация парсера (опционально)
            logger: Логгер (опционально)
        """
        self._logger = logger or logging.getLogger(__name__)
        self._logger.info("Инициализирована заглушка парсера OLX")

    def get_ad_ids(self, category: str, limit: Optional[int] = None, chunk_size: Optional[int] = None) -> List[AdId]:
        """
        Заглушка для получения списка ID объявлений.

        Args:
            category: Категория объявлений
            limit: Максимальное количество ID для получения (опционально)
            chunk_size: Размер чанка для групповой обработки (опционально)

        Returns:
            List[AdId]: Пустой список ID объявлений
        """
        self._logger.info(f"Вызов заглушки парсера для получения ID объявлений в категории {category}")
        return []

    def get_ad_details(self, ad_id: AdId) -> Optional[Dict]:
        """
        Заглушка для получения деталей объявления.

        Args:
            ad_id: ID объявления

        Returns:
            Optional[Dict]: None
        """
        self._logger.info(f"Вызов заглушки парсера для получения деталей объявления {ad_id}")
        return None



    async def parse_category(self, category: str, max_pages: int = 5, max_ads: int = 100) -> List[Property]:
        """
        Заглушка для парсинга категории.

        Args:
            category: Категория для парсинга
            max_pages: Максимальное количество страниц для парсинга
            max_ads: Максимальное количество объявлений для парсинга

        Returns:
            List[Property]: Пустой список объектов недвижимости
        """
        self._logger.info(f"Вызов заглушки парсера для категории {category}")
        return []

    async def parse_ad(self, url: str) -> Optional[Property]:
        """
        Заглушка для парсинга объявления.

        Args:
            url: URL объявления

        Returns:
            Optional[Property]: None
        """
        self._logger.info(f"Вызов заглушки парсера для объявления {url}")
        return None
