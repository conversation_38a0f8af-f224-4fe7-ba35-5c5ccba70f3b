"""
Реализация сервиса локализации на основе YAML-файлов с оптимизацией производительности.
"""
import os
import time
import yaml
import logging
from typing import Dict, Any, Optional, List, Tuple, Set

from src.domain.interfaces.localization_service import ILocalizationService
from src.domain.value_objects.localization import Language


class YamlLocalizationService(ILocalizationService):
    """
    Реализация сервиса локализации на основе YAML-файлов с оптимизацией производительности.

    Оптимизации:
    - Ленивая загрузка файлов локализации (загрузка при первом обращении)
    - Кэширование локализованных строк
    - Отслеживание изменений файлов локализации для автоматической перезагрузки
    """

    def __init__(self, localization_dir: str, available_languages: Optional[List[Language]] = None,
                 default_language: str = 'ru', cache_size: int = 1000, cache_ttl: int = 3600):
        """
        Инициализирует сервис локализации.

        Args:
            localization_dir: Путь к директории с файлами локализации
            available_languages: Список доступных языков (опционально)
            default_language: Язык по умолчанию
            cache_size: Максимальный размер кэша
            cache_ttl: Время жизни кэша в секундах
        """
        self._localization_dir = localization_dir
        self._available_languages = available_languages or []
        self._default_language = default_language
        self._texts: Dict[str, Dict[str, str]] = {}
        self._templates: Dict[str, Dict[str, str]] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._loaded = False

        # Настройки кэша
        self._cache_size = cache_size
        self._cache_ttl = cache_ttl
        self._text_cache: Dict[Tuple[str, str], Tuple[str, float]] = {}  # (key, language) -> (value, timestamp)
        self._template_cache: Dict[Tuple[str, str], Tuple[str, float]] = {}  # (key, language) -> (value, timestamp)
        self._format_cache: Dict[Tuple[str, frozenset], Tuple[str, float]] = {}  # (template, frozenset(params.items())) -> (value, timestamp)

        # Логгер
        self._logger = logging.getLogger(__name__)

    def _ensure_loaded(self) -> None:
        """
        Убеждается, что файлы локализации загружены.
        Если файлы еще не загружены или изменились, загружает их.
        """
        # Проверяем, нужно ли загружать файлы
        if not self._loaded or self._files_changed():
            self._load_localization_files()
            self._loaded = True

    def _files_changed(self) -> bool:
        """
        Проверяет, изменились ли файлы локализации.

        Returns:
            bool: True, если файлы изменились, иначе False
        """
        # Проверяем, что директория существует
        if not os.path.exists(self._localization_dir):
            return False

        # Проверяем файлы локализации
        for filename in os.listdir(self._localization_dir):
            if not filename.endswith('.yaml') and not filename.endswith('.yml'):
                continue

            file_path = os.path.join(self._localization_dir, filename)

            # Получаем время последнего изменения файла
            try:
                mtime = os.path.getmtime(file_path)

                # Если файл новый или изменился, возвращаем True
                if file_path not in self._file_timestamps or mtime > self._file_timestamps[file_path]:
                    return True
            except OSError:
                # Если произошла ошибка при получении времени изменения, считаем, что файл изменился
                return True

        return False

    def _load_localization_files(self) -> None:
        """
        Загружает файлы локализации.
        """
        # Проверяем, что директория существует
        if not os.path.exists(self._localization_dir):
            raise FileNotFoundError(f"Директория локализации не найдена: {self._localization_dir}")

        # Очищаем старые данные
        self._texts = {}
        self._templates = {}
        self._file_timestamps = {}

        # Очищаем кэш
        self._text_cache = {}
        self._template_cache = {}
        self._format_cache = {}

        # Загружаем файлы локализации
        for filename in os.listdir(self._localization_dir):
            if not filename.endswith('.yaml') and not filename.endswith('.yml'):
                continue

            # Получаем код языка из имени файла
            language = os.path.splitext(filename)[0]

            # Загружаем файл
            file_path = os.path.join(self._localization_dir, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)

                # Сохраняем время последнего изменения файла
                self._file_timestamps[file_path] = os.path.getmtime(file_path)

                # Инициализируем словари для языка, если их еще нет
                if language not in self._texts:
                    self._texts[language] = {}
                if language not in self._templates:
                    self._templates[language] = {}

                # Загружаем тексты и шаблоны
                if data and 'texts' in data:
                    self._texts[language].update(data['texts'])
                    self._logger.debug(f"Загружены тексты для языка {language}: {list(data['texts'].keys())}")
                if data and 'templates' in data:
                    self._templates[language].update(data['templates'])
                    self._logger.debug(f"Загружены шаблоны для языка {language}: {list(data['templates'].keys())}")

                self._logger.debug(f"Загружен файл локализации: {file_path}")
            except Exception as e:
                self._logger.error(f"Ошибка при загрузке файла локализации {file_path}: {e}")

    def _clean_cache(self) -> None:
        """
        Очищает устаревшие записи из кэша.
        """
        current_time = time.time()

        # Очищаем кэш текстов
        expired_keys = []
        for key, (_, timestamp) in self._text_cache.items():
            if current_time - timestamp > self._cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._text_cache[key]

        # Очищаем кэш шаблонов
        expired_keys = []
        for key, (_, timestamp) in self._template_cache.items():
            if current_time - timestamp > self._cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._template_cache[key]

        # Очищаем кэш форматирования
        expired_keys = []
        for key, (_, timestamp) in self._format_cache.items():
            if current_time - timestamp > self._cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._format_cache[key]

        # Если кэш слишком большой, удаляем самые старые записи
        if len(self._text_cache) > self._cache_size:
            sorted_items = sorted(self._text_cache.items(), key=lambda x: x[1][1])
            self._text_cache = dict(sorted_items[-self._cache_size:])

        if len(self._template_cache) > self._cache_size:
            sorted_items = sorted(self._template_cache.items(), key=lambda x: x[1][1])
            self._template_cache = dict(sorted_items[-self._cache_size:])

        if len(self._format_cache) > self._cache_size:
            sorted_items = sorted(self._format_cache.items(), key=lambda x: x[1][1])
            self._format_cache = dict(sorted_items[-self._cache_size:])

    def get_text(self, key: str, language: str, default: Optional[str] = None) -> str:
        """
        Получает локализованный текст по ключу.

        Args:
            key: Ключ текста
            language: Код языка
            default: Значение по умолчанию, если текст не найден

        Returns:
            str: Локализованный текст
        """
        # Дополнительное логирование для отладки
        self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Запрос текста для ключа '{key}', язык '{language}'")

        # Проверяем кэш
        cache_key = (key, language)
        if cache_key in self._text_cache:
            result = self._text_cache[cache_key][0]
            self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Найдено в кэше: '{result}'")
            return result

        # Убеждаемся, что файлы локализации загружены
        self._ensure_loaded()

        # Проверяем, что язык существует
        if language not in self._texts:
            self._logger.debug(f"Язык {language} не найден в словаре текстов. Доступные языки: {list(self._texts.keys())}")
            self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Язык '{language}' не найден. Доступные языки: {list(self._texts.keys())}")
            result = default or key
        else:
            # Получаем текст
            result = self._texts[language].get(key, default or key)
            if key not in self._texts[language]:
                self._logger.debug(f"Ключ {key} не найден в текстах для языка {language}. Доступные ключи: {list(self._texts[language].keys())}")
                self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Ключ '{key}' не найден для языка '{language}'. Доступные ключи: {list(self._texts[language].keys())}")
            else:
                self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Найден текст для ключа '{key}', язык '{language}': '{result}'")

        # Сохраняем в кэш
        self._text_cache[cache_key] = (result, time.time())

        # Периодически очищаем кэш
        if len(self._text_cache) % 100 == 0:
            self._clean_cache()

        self._logger.info(f"DEBUG: YamlLocalizationService.get_text - Возвращаем результат: '{result}'")
        return result

    def get_template(self, key: str, language: str, default: Optional[str] = None) -> str:
        """
        Получает локализованный шаблон по ключу.

        Args:
            key: Ключ шаблона
            language: Код языка
            default: Значение по умолчанию, если шаблон не найден

        Returns:
            str: Локализованный шаблон
        """
        # Проверяем кэш
        cache_key = (key, language)
        if cache_key in self._template_cache:
            return self._template_cache[cache_key][0]

        # Убеждаемся, что файлы локализации загружены
        self._ensure_loaded()

        # Проверяем, что язык существует
        if language not in self._templates:
            result = default or key
        else:
            # Получаем шаблон
            result = self._templates[language].get(key, default or key)

        # Сохраняем в кэш
        self._template_cache[cache_key] = (result, time.time())

        # Периодически очищаем кэш
        if len(self._template_cache) % 100 == 0:
            self._clean_cache()

        return result

    def format_template(self, template: str, params: Dict[str, Any]) -> str:
        """
        Форматирует шаблон с параметрами.

        Args:
            template: Шаблон
            params: Параметры для форматирования

        Returns:
            str: Отформатированный текст
        """
        # Если параметров нет, возвращаем шаблон как есть
        if not params:
            return template

        # Создаем ключ для кэша
        # Используем frozenset для хеширования словаря параметров
        cache_key = (template, frozenset(params.items()))

        # Проверяем кэш
        if cache_key in self._format_cache:
            return self._format_cache[cache_key][0]

        # Форматируем шаблон
        try:
            result = template.format(**params)
        except KeyError as e:
            # Если в шаблоне есть плейсхолдер, которого нет в параметрах,
            # возвращаем шаблон как есть и логируем ошибку
            self._logger.warning(f"Ошибка форматирования шаблона: {e}. Шаблон: {template}, Параметры: {params}")
            result = template
        except Exception as e:
            # Если произошла другая ошибка, возвращаем шаблон как есть и логируем ошибку
            self._logger.error(f"Ошибка форматирования шаблона: {e}. Шаблон: {template}, Параметры: {params}")
            result = template

        # Сохраняем в кэш
        self._format_cache[cache_key] = (result, time.time())

        # Периодически очищаем кэш
        if len(self._format_cache) % 100 == 0:
            self._clean_cache()

        return result

    def get_available_languages(self) -> List[Language]:
        """
        Возвращает список доступных языков.

        Returns:
            List[Language]: Список доступных языков
        """
        return self._available_languages

    def reload(self) -> None:
        """
        Принудительно перезагружает файлы локализации.
        """
        self._load_localization_files()
        self._loaded = True
