"""
Модель для хранения информации об обработанных объявлениях.
"""
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, Optional


@dataclass
class ProcessedAd:
    """
    Модель для хранения информации об обработанных объявлениях.
    """
    ad_id: str  # Числовой ID объявления
    processed_at: Optional[datetime] = None
    url_id: Optional[str] = None  # Строковый ID из URL (опционально)

    def __post_init__(self):
        """Инициализация после создания объекта."""
        if self.processed_at is None:
            self.processed_at = datetime.now(timezone.utc)

    def to_dict(self) -> Dict:
        """
        Преобразует объект в словарь для сохранения в JSON.

        Returns:
            Dict: Словарь с данными объекта
        """
        result = {
            "ad_id": self.ad_id,
            "processed_at": self.processed_at.isoformat() if self.processed_at else datetime.now(timezone.utc).isoformat()
        }

        # Добавляем строковый ID, если он есть
        if self.url_id:
            result["url_id"] = self.url_id

        return result

    @classmethod
    def from_dict(cls, data: Dict) -> Optional['ProcessedAd']:
        """
        Создает объект из словаря.

        Args:
            data: Словарь с данными

        Returns:
            Optional[ProcessedAd]: Объект ProcessedAd или None, если данные некорректны
        """
        if not data or 'ad_id' not in data:
            return None

        processed_at = None
        if 'processed_at' in data:
            try:
                processed_at = datetime.fromisoformat(data['processed_at'])
            except (ValueError, TypeError):
                # Если не удалось преобразовать дату, используем текущую
                processed_at = datetime.now(timezone.utc)

        # Получаем строковый ID, если он есть
        url_id = data.get('url_id')

        return cls(
            ad_id=data['ad_id'],
            processed_at=processed_at,
            url_id=url_id
        )
