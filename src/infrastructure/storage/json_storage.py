"""
Хранилище данных в JSON файле.
"""
import json
import logging
import os
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Union

from src.infrastructure.storage.models.processed_ad import ProcessedAd


class JsonStorage:
    """
    Хранилище данных в JSON файле.
    Используется для хранения информации об обработанных объявлениях.
    """

    def __init__(self, file_path: str, logger: Optional[logging.Logger] = None):
        """
        Инициализирует хранилище.

        Args:
            file_path: Путь к JSON файлу
            logger: Логгер (опционально)
        """
        self.file_path = file_path
        self._logger = logger or logging.getLogger(__name__)
        self._ensure_file_exists()

    def _ensure_file_exists(self) -> None:
        """
        Проверяет существование файла и создает его, если он не существует.
        """
        try:
            # Создаем директорию, если она не существует
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)

            # Если файл не существует, создаем его с пустым списком
            if not os.path.exists(self.file_path):
                self._logger.info(f"Создание нового файла хранилища: {self.file_path}")
                with open(self.file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
        except Exception as e:
            self._logger.error(f"Ошибка при создании файла хранилища {self.file_path}: {e}")
            raise

    def _read_data(self) -> List[Dict]:
        """
        Читает данные из файла.

        Returns:
            List[Dict]: Список словарей с данными
        """
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data if isinstance(data, list) else []
        except json.JSONDecodeError:
            self._logger.error(f"Ошибка декодирования JSON в файле {self.file_path}")
            return []
        except Exception as e:
            self._logger.error(f"Ошибка при чтении файла {self.file_path}: {e}")
            return []

    def _write_data(self, data: List[Dict]) -> bool:
        """
        Записывает данные в файл.

        Args:
            data: Список словарей с данными

        Returns:
            bool: True, если запись успешна, иначе False
        """
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self._logger.error(f"Ошибка при записи в файл {self.file_path}: {e}")
            return False

    def add(self, processed_ad: Union[ProcessedAd, Dict]) -> bool:
        """
        Добавляет информацию об обработанном объявлении.

        Args:
            processed_ad: Объект ProcessedAd или словарь с данными

        Returns:
            bool: True, если добавление успешно, иначе False
        """
        # Преобразуем в словарь, если это объект ProcessedAd
        if isinstance(processed_ad, ProcessedAd):
            processed_ad_dict = processed_ad.to_dict()
            self._logger.debug(f"Преобразован объект ProcessedAd в словарь: {processed_ad_dict}")
        else:
            processed_ad_dict = processed_ad
            self._logger.debug(f"Используется словарь: {processed_ad_dict}")

        # Проверяем наличие обязательных полей
        if 'ad_id' not in processed_ad_dict:
            self._logger.error("Отсутствует обязательное поле 'ad_id'")
            return False

        # Добавляем поле processed_at, если его нет
        if 'processed_at' not in processed_ad_dict:
            current_time = datetime.now(timezone.utc)
            processed_ad_dict['processed_at'] = current_time.isoformat()
            self._logger.debug(f"Добавлено поле processed_at: {current_time.isoformat()}")

        self._logger.info(f"Попытка добавления ID {processed_ad_dict['ad_id']} в хранилище {self.file_path}")

        # Читаем текущие данные
        data = self._read_data()
        self._logger.debug(f"В хранилище {self.file_path} найдено {len(data)} записей")

        # Проверяем, есть ли уже такой ID
        for item in data:
            if item.get('ad_id') == processed_ad_dict['ad_id']:
                self._logger.info(f"ID {processed_ad_dict['ad_id']} уже существует в хранилище, дата обработки: {item.get('processed_at')}")
                return True

        # Добавляем новый элемент
        data.append(processed_ad_dict)
        self._logger.info(f"Добавлен новый ID {processed_ad_dict['ad_id']} в хранилище, дата обработки: {processed_ad_dict['processed_at']}")

        # Записываем обновленные данные
        result = self._write_data(data)
        if result:
            self._logger.info(f"Успешно записаны обновленные данные в хранилище {self.file_path}")
        else:
            self._logger.error(f"Ошибка при записи обновленных данных в хранилище {self.file_path}")
        return result

    def exists(self, ad_id: str) -> bool:
        """
        Проверяет, существует ли информация об обработанном объявлении.

        Args:
            ad_id: ID объявления

        Returns:
            bool: True, если информация существует, иначе False
        """
        self._logger.debug(f"[Проверка существования ID] Проверка существования ID '{ad_id}' в хранилище {self.file_path}")

        # Читаем текущие данные
        data = self._read_data()
        self._logger.debug(f"[Проверка существования ID] В хранилище {self.file_path} найдено {len(data)} записей")

        # Извлекаем числовой ID из строкового ID, если возможно
        import re
        numeric_id_match = re.search(r'^\d+$', ad_id)
        numeric_id = ad_id if numeric_id_match else None

        # Извлекаем ID в формате ID* из строкового ID, если возможно
        id_pattern = re.compile(r'ID([A-Za-z0-9]+)')
        id_match = id_pattern.search(ad_id)
        id_part = id_match.group(0) if id_match else None

        # Проверяем наличие ID
        for item in data:
            # Проверяем по основному ID (ad_id)
            item_ad_id = item.get('ad_id')
            if item_ad_id == ad_id:
                self._logger.debug(f"[Проверка существования ID] ID '{ad_id}' найден в хранилище как ad_id, дата обработки: {item.get('processed_at')}")
                return True

            # Проверяем по строковому ID (url_id)
            item_url_id = item.get('url_id')
            if item_url_id and item_url_id == ad_id:
                self._logger.debug(f"[Проверка существования ID] ID '{ad_id}' найден в хранилище как url_id, дата обработки: {item.get('processed_at')}")
                return True

            # Проверяем по числовому ID, если он есть
            if numeric_id and item_ad_id == numeric_id:
                self._logger.debug(f"[Проверка существования ID] Числовой ID '{numeric_id}' найден в хранилище как ad_id, дата обработки: {item.get('processed_at')}")
                return True

            # Проверяем по ID в формате ID*, если он есть
            if id_part and item_url_id and id_part in item_url_id:
                self._logger.debug(f"[Проверка существования ID] ID в формате ID* '{id_part}' найден в url_id '{item_url_id}', дата обработки: {item.get('processed_at')}")
                return True

        self._logger.debug(f"[Проверка существования ID] ID '{ad_id}' не найден в хранилище")
        return False

    def get_all(self) -> List[ProcessedAd]:
        """
        Возвращает список всех обработанных объявлений.

        Returns:
            List[ProcessedAd]: Список объектов ProcessedAd
        """
        # Читаем текущие данные
        data = self._read_data()

        # Преобразуем словари в объекты ProcessedAd
        result = []
        for item in data:
            processed_ad = ProcessedAd.from_dict(item)
            if processed_ad:
                result.append(processed_ad)

        return result

    def remove(self, ad_id: str) -> bool:
        """
        Удаляет информацию об обработанном объявлении.

        Args:
            ad_id: ID объявления

        Returns:
            bool: True, если удаление успешно, иначе False
        """
        # Читаем текущие данные
        data = self._read_data()

        # Ищем элемент для удаления
        for i, item in enumerate(data):
            if item.get('ad_id') == ad_id:
                # Удаляем элемент
                data.pop(i)
                # Записываем обновленные данные
                return self._write_data(data)

        # Элемент не найден
        return False

    def clear(self) -> bool:
        """
        Очищает хранилище.

        Returns:
            bool: True, если очистка успешна, иначе False
        """
        return self._write_data([])

    def count(self) -> int:
        """
        Возвращает количество элементов в хранилище.

        Returns:
            int: Количество элементов
        """
        return len(self._read_data())

    def remove_old_ads(self, max_storage_days: int = 30) -> int:
        """
        Удаляет старые записи об обработанных объявлениях.

        Args:
            max_storage_days: Максимальное время хранения записей в днях (по умолчанию 30)

        Returns:
            int: Количество удаленных записей
        """
        # Если max_storage_days равен 0, то не удаляем ничего
        if max_storage_days <= 0:
            self._logger.debug(f"Удаление старых записей отключено (max_storage_days={max_storage_days})")
            return 0

        # Читаем текущие данные
        data = self._read_data()

        # Вычисляем дату, старше которой записи будут удалены
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=max_storage_days)

        # Фильтруем записи
        new_data = []
        removed_count = 0

        for item in data:
            # Получаем дату обработки
            processed_at_str = item.get('processed_at')

            # Если дата не указана, сохраняем запись
            if not processed_at_str:
                new_data.append(item)
                continue

            try:
                # Преобразуем строку в дату
                try:
                    # Пробуем преобразовать строку в дату с информацией о часовом поясе
                    processed_at = datetime.fromisoformat(processed_at_str.replace('Z', '+00:00'))
                except ValueError:
                    # Если не получилось, пробуем преобразовать без информации о часовом поясе
                    processed_at = datetime.fromisoformat(processed_at_str)
                    # Добавляем информацию о часовом поясе UTC
                    processed_at = processed_at.replace(tzinfo=timezone.utc)

                # Если дата обработки новее граничной даты, сохраняем запись
                if processed_at > cutoff_date:
                    new_data.append(item)
                else:
                    # Иначе удаляем запись
                    removed_count += 1
                    self._logger.debug(f"Удалена старая запись: {item.get('ad_id')} (обработана {processed_at_str})")
            except (ValueError, TypeError):
                # Если не удалось преобразовать дату, сохраняем запись
                # Не выводим предупреждение, чтобы не засорять логи
                # self._logger.warning(f"Ошибка при обработке даты {processed_at_str}")
                new_data.append(item)

        # Если есть удаленные записи, записываем обновленные данные
        if removed_count > 0:
            self._write_data(new_data)
            self._logger.info(f"Удалено {removed_count} старых записей (старше {max_storage_days} дней)")
        else:
            self._logger.debug(f"Старых записей не найдено (старше {max_storage_days} дней)")

        return removed_count
