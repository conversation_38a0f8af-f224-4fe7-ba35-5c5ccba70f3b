"""
Пользовательские форматтеры для логов.
"""
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Optional, Any


class JsonFormatter(logging.Formatter):
    """
    Форматтер для вывода логов в формате JSON.
    """

    def __init__(self, include_timestamp: bool = True, include_logger_name: bool = True,
                include_level_name: bool = True, include_path: bool = False,
                include_function: bool = False, include_process: bool = False,
                include_thread: bool = False, include_extra: bool = True):
        """
        Инициализирует форматтер.

        Args:
            include_timestamp: Включать временную метку (по умолчанию True)
            include_logger_name: Включать имя логгера (по умолчанию True)
            include_level_name: Включать имя уровня логирования (по умолчанию True)
            include_path: Включать путь к файлу (по умолчанию False)
            include_function: Включать имя функции (по умолчанию False)
            include_process: Включать ID процесса (по умолчанию False)
            include_thread: Включать ID потока (по умолчанию False)
            include_extra: Включать дополнительные поля (по умолчанию True)
        """
        super().__init__()
        self.include_timestamp = include_timestamp
        self.include_logger_name = include_logger_name
        self.include_level_name = include_level_name
        self.include_path = include_path
        self.include_function = include_function
        self.include_process = include_process
        self.include_thread = include_thread
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """
        Форматирует запись лога в JSON.

        Args:
            record: Запись лога

        Returns:
            str: Отформатированная запись лога в формате JSON
        """
        log_data: Dict[str, Any] = {}

        # Добавляем временную метку
        if self.include_timestamp:
            log_data['timestamp'] = datetime.fromtimestamp(record.created).isoformat()

        # Добавляем имя логгера
        if self.include_logger_name:
            log_data['logger'] = record.name

        # Добавляем уровень логирования
        if self.include_level_name:
            log_data['level'] = record.levelname

        # Добавляем сообщение
        log_data['message'] = record.getMessage()

        # Добавляем путь к файлу
        if self.include_path:
            log_data['path'] = f"{record.pathname}:{record.lineno}"

        # Добавляем имя функции
        if self.include_function:
            log_data['function'] = record.funcName

        # Добавляем ID процесса
        if self.include_process:
            log_data['process'] = record.process

        # Добавляем ID потока
        if self.include_thread:
            log_data['thread'] = record.thread

        # Добавляем дополнительные поля
        # В LogRecord нет стандартного атрибута 'extra', но мы можем добавить его вручную
        if self.include_extra and hasattr(record, 'extra'):
            # Используем getattr с защитой от None
            extra = getattr(record, 'extra', None)
            if extra and isinstance(extra, dict):
                log_data.update(extra)

        # Добавляем информацию об исключении, если есть
        if record.exc_info and record.exc_info[0] is not None:
            exc_type = record.exc_info[0]
            exc_value = record.exc_info[1]
            exc_tb = record.exc_info[2]

            log_data['exception'] = {
                'type': exc_type.__name__ if exc_type else 'Unknown',
                'message': str(exc_value) if exc_value else '',
                'traceback': traceback.format_exception(exc_type, exc_value, exc_tb) if all([exc_type, exc_value, exc_tb]) else []
            }

        return json.dumps(log_data, ensure_ascii=False)


class DetailedFormatter(logging.Formatter):
    """
    Форматтер для вывода детальной информации в логах.
    """

    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None):
        """
        Инициализирует форматтер.

        Args:
            fmt: Формат сообщения (опционально)
            datefmt: Формат даты (опционально)
        """
        if fmt is None:
            fmt = "%(asctime)s - %(name)s - %(levelname)s - [%(pathname)s:%(lineno)d] - %(message)s"
        super().__init__(fmt=fmt, datefmt=datefmt)

    def formatException(self, ei) -> str:
        """
        Форматирует информацию об исключении.

        Args:
            ei: Информация об исключении

        Returns:
            str: Отформатированная информация об исключении
        """
        # Получаем стандартное форматирование
        result = super().formatException(ei)

        # Добавляем разделитель
        result = f"\n{'=' * 80}\nEXCEPTION DETAILS:\n{result}\n{'=' * 80}"

        return result
