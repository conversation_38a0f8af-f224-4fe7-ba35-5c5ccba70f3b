"""
Обработчик для записи логов в файл.
"""
import logging
import os
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from typing import Optional


def get_file_handler(log_file: str, log_level: int = logging.INFO, log_format: Optional[str] = None,
                    max_file_size: int = 10 * 1024 * 1024, backup_count: int = 5) -> logging.Handler:
    """
    Создает обработчик для записи логов в файл с ротацией по размеру.
    
    Args:
        log_file: Путь к файлу лога
        log_level: Уровень логирования
        log_format: Формат сообщений лога (опционально)
        max_file_size: Максимальный размер файла лога в байтах (по умолчанию 10 МБ)
        backup_count: Количество файлов ротации (по умолчанию 5)
        
    Returns:
        logging.Handler: Обработчик для записи логов в файл
    """
    # Создаем директорию для лога, если она не существует
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Создаем обработчик с ротацией файлов по размеру
    handler = RotatingFileHandler(
        log_file, maxBytes=max_file_size, backupCount=backup_count, encoding='utf-8'
    )
    handler.setLevel(log_level)
    
    # Устанавливаем формат сообщений
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    formatter = logging.Formatter(log_format)
    handler.setFormatter(formatter)
    
    return handler


def get_timed_file_handler(log_file: str, log_level: int = logging.INFO, log_format: Optional[str] = None,
                          when: str = 'midnight', interval: int = 1, backup_count: int = 7) -> logging.Handler:
    """
    Создает обработчик для записи логов в файл с ротацией по времени.
    
    Args:
        log_file: Путь к файлу лога
        log_level: Уровень логирования
        log_format: Формат сообщений лога (опционально)
        when: Интервал ротации ('S', 'M', 'H', 'D', 'W0'-'W6', 'midnight')
        interval: Количество интервалов между ротациями
        backup_count: Количество файлов ротации (по умолчанию 7)
        
    Returns:
        logging.Handler: Обработчик для записи логов в файл
    """
    # Создаем директорию для лога, если она не существует
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Создаем обработчик с ротацией файлов по времени
    handler = TimedRotatingFileHandler(
        log_file, when=when, interval=interval, backupCount=backup_count, encoding='utf-8'
    )
    handler.setLevel(log_level)
    
    # Устанавливаем формат сообщений
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    formatter = logging.Formatter(log_format)
    handler.setFormatter(formatter)
    
    return handler
