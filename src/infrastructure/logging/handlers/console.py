"""
Обработчик для вывода логов в консоль.
"""
import logging
import sys
from typing import Optional


def get_console_handler(log_level: int = logging.INFO, log_format: Optional[str] = None,
                       use_stderr: bool = False) -> logging.Handler:
    """
    Создает обработчик для вывода логов в консоль.
    
    Args:
        log_level: Уровень логирования
        log_format: Формат сообщений лога (опционально)
        use_stderr: Использовать stderr вместо stdout (по умолчанию False)
        
    Returns:
        logging.Handler: Обработчик для вывода логов в консоль
    """
    # Создаем обработчик для вывода в консоль
    handler = logging.StreamHandler(sys.stderr if use_stderr else sys.stdout)
    handler.setLevel(log_level)
    
    # Устанавливаем формат сообщений
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    formatter = logging.Formatter(log_format)
    handler.setFormatter(formatter)
    
    return handler


def get_colored_console_handler(log_level: int = logging.INFO, log_format: Optional[str] = None,
                              use_stderr: bool = False) -> logging.Handler:
    """
    Создает обработчик для вывода цветных логов в консоль.
    
    Args:
        log_level: Уровень логирования
        log_format: Формат сообщений лога (опционально)
        use_stderr: Использовать stderr вместо stdout (по умолчанию False)
        
    Returns:
        logging.Handler: Обработчик для вывода цветных логов в консоль
    """
    # Создаем обработчик для вывода в консоль
    handler = logging.StreamHandler(sys.stderr if use_stderr else sys.stdout)
    handler.setLevel(log_level)
    
    # Цвета для разных уровней логирования
    colors = {
        logging.DEBUG: '\033[94m',     # Синий
        logging.INFO: '\033[92m',      # Зеленый
        logging.WARNING: '\033[93m',   # Желтый
        logging.ERROR: '\033[91m',     # Красный
        logging.CRITICAL: '\033[91m\033[1m'  # Красный жирный
    }
    reset = '\033[0m'
    
    # Создаем класс форматтера с цветами
    class ColoredFormatter(logging.Formatter):
        def format(self, record):
            levelno = record.levelno
            if levelno in colors:
                record.levelname = f"{colors[levelno]}{record.levelname}{reset}"
                record.msg = f"{colors[levelno]}{record.msg}{reset}"
            return super().format(record)
    
    # Устанавливаем формат сообщений
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    formatter = ColoredFormatter(log_format)
    handler.setFormatter(formatter)
    
    return handler
