"""
Базовый класс для форматтеров.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union


class BaseFormatter(ABC):
    """
    Базовый класс для форматтеров.
    """
    
    @abstractmethod
    def format(self, data: Any) -> Dict[str, Any]:
        """
        Форматирует данные.
        
        Args:
            data: Данные для форматирования
            
        Returns:
            Dict[str, Any]: Отформатированные данные
        """
        pass
