"""
Модуль для фильтрации полей перед отправкой в Telegram.
"""
from typing import Dict, Any, List


class FieldFilter:
    """
    Класс для фильтрации полей перед отправкой в Telegram.
    Скрывает поля со значением "не указан" и его склонениями.
    """

    # Список значений, которые нужно скрыть
    _HIDDEN_VALUES = [
        "не указан", "не указана", "не указано", "не указаны",
        "Не указан", "Не указана", "Не указано", "Не указаны"
    ]

    # Список полей, которые всегда должны отображаться, даже если их значение "не указано"
    _ALWAYS_SHOW_FIELDS = [
        "id", "title", "url", "description", "price", "location", "published_at"
    ]

    # Флаг, указывающий, включена ли фильтрация
   #  _enabled = True
    _enabled = False

    @classmethod
    def enable(cls):
        """Включает фильтрацию полей."""
        cls._enabled = True

    @classmethod
    def disable(cls):
        """Отключает фильтрацию полей."""
        cls._enabled = False

    @classmethod
    def is_enabled(cls) -> bool:
        """
        Проверяет, включена ли фильтрация полей.

        Returns:
            bool: True, если фильтрация включена, иначе False
        """
        return cls._enabled

    @classmethod
    def filter_formatted_data(cls, formatted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Фильтрует отформатированные данные, удаляя поля со значением "не указан" и его склонениями.

        Args:
            formatted_data: Отформатированные данные объекта недвижимости

        Returns:
            Dict[str, Any]: Отфильтрованные данные
        """
        # Если фильтрация отключена, возвращаем исходные данные
        if not cls._enabled:
            return formatted_data

        filtered_data = {}

        for key, value in formatted_data.items():
            # Если поле в списке обязательных или его значение не в списке скрываемых
            if key in cls._ALWAYS_SHOW_FIELDS or (isinstance(value, str) and value not in cls._HIDDEN_VALUES):
                filtered_data[key] = value
            else:
                # Для полей, которые не в списке обязательных, но их значение не в списке скрываемых
                if not isinstance(value, str) or value not in cls._HIDDEN_VALUES:
                    filtered_data[key] = value

        return filtered_data

    @classmethod
    def filter_template_fields(cls, template: str, formatted_data: Dict[str, Any]) -> str:
        """
        Фильтрует шаблон, удаляя строки с полями, которые имеют значение "не указан" и его склонения.

        Args:
            template: Шаблон для форматирования
            formatted_data: Отформатированные данные объекта недвижимости

        Returns:
            str: Отфильтрованный шаблон
        """
        # Если фильтрация отключена, возвращаем исходный шаблон
        if not cls._enabled:
            return template

        # Список строк, которые нужно удалить из шаблона
        lines_to_remove = []

        # Проходим по всем полям
        for key, value in formatted_data.items():
            # Если значение поля в списке скрываемых и поле не в списке обязательных
            if isinstance(value, str) and value in cls._HIDDEN_VALUES and key not in cls._ALWAYS_SHOW_FIELDS:
                # Ищем строки в шаблоне, содержащие это поле
                field_placeholder = f"{{{key}}}"

                # Разбиваем шаблон на строки
                template_lines = template.split("\n")

                # Ищем строки, содержащие плейсхолдер поля
                for i, line in enumerate(template_lines):
                    if field_placeholder in line:
                        lines_to_remove.append(line)

        # Удаляем найденные строки из шаблона
        for line in lines_to_remove:
            template = template.replace(line + "\n", "")
            # Также проверяем случай, когда строка в конце шаблона (без \n в конце)
            template = template.replace(line, "")

        return template
