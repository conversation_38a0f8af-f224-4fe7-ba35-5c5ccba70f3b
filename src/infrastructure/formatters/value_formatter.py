"""
Форматтер для значений.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union


class ValueFormatter:
    """
    Форматтер для значений.
    """

    @staticmethod
    def format_price(amount: Optional[float], currency: Optional[str] = None) -> str:
        """
        Форматирует цену.

        Args:
            amount: Сумма
            currency: Валюта

        Returns:
            str: Отформатированная цена
        """
        if amount is None:
            return "Цена не указана"

        # Форматируем сумму с разделителями тысяч
        formatted_amount = "{:,.0f}".format(amount).replace(",", " ")

        # Добавляем валюту, если она указана
        if currency:
            return f"{formatted_amount} {currency}"

        return formatted_amount

    @staticmethod
    def format_area(area: Optional[float], unit: str = "м²") -> str:
        """
        Форматирует площадь.

        Args:
            area: Площадь
            unit: Единица измерения

        Returns:
            str: Отформатированная площадь
        """
        if area is None:
            return "Площадь не указана"

        # Если единица измерения "соток" и площадь в квадратных метрах, преобразуем в сотки
        if unit == "соток" and area > 50:  # Если площадь больше 50, скорее всего она в кв.м
            area = area / 100  # Переводим из кв.м в сотки

        # Форматируем площадь с одним знаком после запятой
        formatted_area = "{:.1f}".format(area).replace(".", ",")

        return f"{formatted_area} {unit}"

    @staticmethod
    def format_date(date_str: Optional[str]) -> str:
        """
        Форматирует дату.

        Args:
            date_str: Строка с датой

        Returns:
            str: Отформатированная дата
        """
        if not date_str:
            return "Дата не указана"

        try:
            # Пытаемся распарсить дату
            date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")

            # Проверяем, если время 00:00, то показываем только дату
            if date_obj.hour == 0 and date_obj.minute == 0:
                return date_obj.strftime("%d.%m.%Y")
            else:
                # Форматируем дату в удобочитаемый вид с временем
                return date_obj.strftime("%d.%m.%Y %H:%M")

        except (ValueError, TypeError):
            # Если не удалось распарсить дату, возвращаем исходную строку
            return date_str

    @staticmethod
    def format_floor_info(floor: Optional[int], total_floors: Optional[int]) -> str:
        """
        Форматирует информацию о этаже.

        Args:
            floor: Этаж
            total_floors: Количество этажей

        Returns:
            str: Отформатированная информация о этаже
        """
        if floor is None and total_floors is None:
            return "не указан"

        if floor is not None and total_floors is not None:
            return f"{floor}/{total_floors}"
        elif floor is not None:
            return f"{floor} / не указано"
        else:
            return f"не указан / {total_floors}"

    @staticmethod
    def format_rooms_count(rooms_count: Optional[int]) -> str:
        """
        Форматирует количество комнат.

        Args:
            rooms_count: Количество комнат

        Returns:
            str: Отформатированное количество комнат
        """
        if rooms_count is None:
            return "Количество комнат не указано"

        return str(rooms_count)

    @staticmethod
    def format_contact_info(name: Optional[str], phones: Optional[List[str]]) -> str:
        """
        Форматирует контактную информацию.

        Args:
            name: Имя контакта
            phones: Список телефонов

        Returns:
            str: Отформатированная контактная информация
        """
        if not name and not phones:
            return "Информация не указана"

        contact_parts = []
        if name:
            contact_parts.append(f"Имя: {name}")
        if phones:
            contact_parts.append(f"Телефон: {', '.join(phones)}")

        return "\n".join(contact_parts)

    @staticmethod
    def format_description(description: Optional[str], max_length: int = 250) -> str:
        """
        Форматирует описание.

        Args:
            description: Описание
            max_length: Максимальная длина описания

        Returns:
            str: Отформатированное описание
        """
        if not description:
            return "Описание отсутствует"

        if len(description) > max_length:
            return description[:max_length - 3] + "..."

        return description
