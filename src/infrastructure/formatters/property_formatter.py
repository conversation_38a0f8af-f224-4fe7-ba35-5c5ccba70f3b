"""
Форматтер для объектов недвижимости.
"""
from typing import Any, Dict, List, Optional, Union

from src.domain.entities.property import Property
from src.infrastructure.formatters.base_formatter import BaseFormatter
from src.infrastructure.formatters.field_filter import FieldFilter
from src.infrastructure.formatters.value_formatter import ValueFormatter


class PropertyFormatter(BaseFormatter):
    """
    Форматтер для объектов недвижимости.
    """

    def __init__(self, value_formatter: Optional[ValueFormatter] = None):
        """
        Инициализирует форматтер объектов недвижимости.

        Args:
            value_formatter: Форматтер значений (опционально)
        """
        self.value_formatter = value_formatter or ValueFormatter()

    def format(self, data: Union[Dict[str, Any], Property], format_type: str = 'full', template: Optional[str] = None) -> Dict[str, Any]:
        """
        Форматирует данные объекта недвижимости.

        Args:
            data: Данные объекта недвижимости или объект Property
            format_type: Тип форматирования ('full', 'short', 'list')
            template: Шаблон для форматирования (опционально)

        Returns:
            Dict[str, Any]: Отформатированные данные объекта недвижимости
        """
        # Если передан объект Property, преобразуем его в словарь
        if isinstance(data, Property):
            return self._format_property_object(data, format_type, template)

        # Иначе форматируем словарь с данными
        return self._format_property_dict(data, format_type)

    def _format_property_dict(self, data: Dict[str, Any], format_type: str) -> Dict[str, Any]:
        """
        Форматирует словарь с данными объекта недвижимости.

        Args:
            data: Словарь с данными объекта недвижимости
            format_type: Тип форматирования ('full', 'short', 'list')

        Returns:
            Dict[str, Any]: Отформатированные данные объекта недвижимости
        """
        formatted_data = {}

        # ID объявления
        if 'id' in data:
            formatted_data['id'] = data['id']

        # URL объявления
        if 'url' in data:
            formatted_data['url'] = data['url']

        # Заголовок объявления
        if 'title' in data:
            formatted_data['title'] = data['title']

        # Описание объявления
        if 'description' in data:
            formatted_data['description'] = self.value_formatter.format_description(data['description'])

        # Цена
        if 'price' in data and isinstance(data['price'], dict):
            price_data = data['price']
            amount = price_data.get('amount')
            currency = price_data.get('currency')
            formatted_data['price'] = self.value_formatter.format_price(amount, currency)

        # Местоположение
        if 'location' in data:
            formatted_data['location'] = data['location']

        # Параметры объекта недвижимости
        if format_type == 'full' or format_type == 'short':
            # Площадь
            area_info = []
            if 'total_area' in data and data['total_area']:
                area_info.append(f"общая: {self.value_formatter.format_area(data['total_area'])}")
                formatted_data['total_area'] = data['total_area']

            if 'kitchen_area' in data and data['kitchen_area'] and format_type == 'full':
                area_info.append(f"кухня: {self.value_formatter.format_area(data['kitchen_area'])}")
                formatted_data['kitchen_area'] = data['kitchen_area']

            if 'living_area' in data and data['living_area'] and format_type == 'full':
                area_info.append(f"жилая: {self.value_formatter.format_area(data['living_area'])}")
                formatted_data['living_area'] = data['living_area']

            if 'land_area' in data and data['land_area']:
                area_info.append(f"участок: {self.value_formatter.format_area(data['land_area'], 'соток')}")
                # Если площадь участка в кв.м, преобразуем в сотки
                if data['land_area'] > 50:  # Если площадь больше 50, скорее всего она в кв.м
                    formatted_data['land_area'] = data['land_area'] / 100  # Переводим из кв.м в сотки
                else:
                    formatted_data['land_area'] = data['land_area']

            if area_info:
                formatted_data['area_info'] = ", ".join(area_info)

            # Комнаты
            if 'rooms_count' in data:
                formatted_data['rooms_count'] = self.value_formatter.format_rooms_count(data['rooms_count'])
                formatted_data['rooms_info'] = f"<b>🚪 Комнат:</b> {formatted_data['rooms_count']}\n" if data['rooms_count'] else ""

            # Этаж
            if 'floor' in data or 'total_floors' in data:
                floor = data.get('floor')
                total_floors = data.get('total_floors')
                formatted_data['floor_info'] = f"<b>🏢 Этаж:</b> {self.value_formatter.format_floor_info(floor, total_floors)}\n" if (floor or total_floors) else ""

            # Тип здания
            if 'building_type' in data and data['building_type']:
                formatted_data['building_type'] = data['building_type']
                formatted_data['building_info'] = f"<b>🏗️ Тип здания:</b> {data['building_type']}\n" if data['building_type'] else ""

        # Контактная информация
        if 'contact' in data and isinstance(data['contact'], dict) and format_type == 'full':
            contact_data = data['contact']
            name = contact_data.get('name')
            phones = contact_data.get('phones', [])
            formatted_data['contact_info'] = self.value_formatter.format_contact_info(name, phones)

        # Дата публикации
        if 'published_at' in data and format_type == 'full':
            formatted_data['published_at'] = self.value_formatter.format_date(data['published_at'])

        # Изображения
        if 'images' in data and isinstance(data['images'], list):
            formatted_data['images'] = data['images']

        return formatted_data

    def _format_property_object(self, property_obj: Property, format_type: str, template: Optional[str] = None) -> Dict[str, Any]:
        """
        Форматирует объект Property.

        Args:
            property_obj: Объект Property
            format_type: Тип форматирования ('full', 'short', 'list')
            template: Шаблон для форматирования (опционально)

        Returns:
            Dict[str, Any]: Отформатированные данные объекта недвижимости
        """
        formatted_data = {}

        # Базовая информация
        # Используем числовой ID, если он есть, иначе ID из URL
        if hasattr(property_obj, 'numeric_id') and property_obj.numeric_id:
            formatted_data['id'] = property_obj.numeric_id
        else:
            formatted_data['id'] = str(property_obj.ad_id)
        formatted_data['title'] = property_obj.title or "Объект недвижимости"
        formatted_data['url'] = property_obj.url or "#"
        formatted_data['description'] = self.value_formatter.format_description(property_obj.description)

        # Цена
        if property_obj.price:
            formatted_data['price'] = self.value_formatter.format_price(
                property_obj.price.amount,
                property_obj.price.currency
            )
        else:
            formatted_data['price'] = "Цена не указана"

        # Местоположение
        if property_obj.address:
            formatted_data['location'] = str(property_obj.address)
        else:
            formatted_data['location'] = "Адрес не указан"

        # Параметры объекта недвижимости для полного и краткого форматов
        if format_type == 'full' or format_type == 'short':
            # Площадь
            area_info = []
            if property_obj.area:
                if property_obj.area.total:
                    area_info.append(f"общая: {self.value_formatter.format_area(property_obj.area.total)}")
                    formatted_data['total_area'] = str(property_obj.area.total) if property_obj.area.total is not None else "не указана"

                if property_obj.area.living and format_type == 'full':
                    area_info.append(f"жилая: {self.value_formatter.format_area(property_obj.area.living)}")
                    formatted_data['living_area'] = str(property_obj.area.living)

                if property_obj.area.kitchen and format_type == 'full':
                    area_info.append(f"кухня: {self.value_formatter.format_area(property_obj.area.kitchen)}")
                    formatted_data['kitchen_area'] = str(property_obj.area.kitchen) if property_obj.area.kitchen is not None else "не указана"

                if property_obj.area.land:
                    area_info.append(f"участок: {self.value_formatter.format_area(property_obj.area.land, 'соток')}")
                    # Если площадь участка в кв.м, преобразуем в сотки
                    if property_obj.area.land > 50:  # Если площадь больше 50, скорее всего она в кв.м
                        land_area_value = property_obj.area.land / 100  # Переводим из кв.м в сотки
                        formatted_data['land_area'] = str(land_area_value)
                    else:
                        formatted_data['land_area'] = str(property_obj.area.land)

            if area_info:
                formatted_data['area_info'] = ", ".join(area_info)
                formatted_data['area_info'] = f"<b>📏 Площадь:</b> {formatted_data['area_info']}\n"
            else:
                formatted_data['area_info'] = ""
                formatted_data['total_area'] = "не указана"
                formatted_data['kitchen_area'] = "не указана"

            # Комнаты
            if property_obj.rooms is not None:
                formatted_data['rooms_count'] = str(property_obj.rooms)
                formatted_data['rooms_info'] = f"<b>🚪 Комнат:</b> {formatted_data['rooms_count']}\n"
            else:
                formatted_data['rooms_count'] = "не указано"
                formatted_data['rooms_info'] = ""

            # Этаж
            # Всегда устанавливаем значения для переменных floor и total_floors, даже если они не указаны
            formatted_data['floor'] = str(property_obj.floor) if property_obj.floor is not None else ""
            formatted_data['total_floors'] = str(property_obj.total_floors) if property_obj.total_floors is not None else ""

            if property_obj.floor is not None or property_obj.total_floors is not None:
                formatted_data['floor_info'] = f"<b>🏢 Этаж:</b> {self.value_formatter.format_floor_info(property_obj.floor, property_obj.total_floors)}\n"
            else:
                formatted_data['floor_info'] = ""

            # Тип здания
            building_type = None
            import logging
            logger = logging.getLogger('property_formatter')
            logger.setLevel(logging.DEBUG)
            logger.info(f"PropertyFormatter: Проверка типа дома. hasattr(building_type)={hasattr(property_obj, 'building_type')}, building_type={property_obj.building_type if hasattr(property_obj, 'building_type') else None}")
            logger.info(f"PropertyFormatter: additional_info={property_obj.additional_info}")

            if hasattr(property_obj, 'building_type') and property_obj.building_type is not None:
                building_type = property_obj.building_type
                formatted_data['building_type'] = building_type
                formatted_data['building_info'] = f"<b>🏗️ Тип здания:</b> {building_type}\n"
                logger.info(f"PropertyFormatter: Используем building_type из объекта: {building_type}")
            elif property_obj.additional_info and 'building_type' in property_obj.additional_info:
                building_type = property_obj.additional_info['building_type']
                formatted_data['building_type'] = building_type
                formatted_data['building_info'] = f"<b>🏗️ Тип здания:</b> {building_type}\n"
                logger.info(f"PropertyFormatter: Используем building_type из additional_info: {building_type}")
            elif property_obj.property_type is not None:
                formatted_data['building_type'] = property_obj.property_type
                formatted_data['building_info'] = f"<b>🏗️ Тип здания:</b> {property_obj.property_type}\n"
                logger.info(f"PropertyFormatter: Используем property_type: {property_obj.property_type}")
            else:
                formatted_data['building_type'] = "не указан"
                formatted_data['building_info'] = ""
                logger.info("PropertyFormatter: Тип дома не указан")

        # Контактная информация для полного формата
        if format_type == 'full':
            if property_obj.contact:
                name = property_obj.contact.name
                phones = property_obj.contact.phones if property_obj.contact.phones else []
                formatted_data['contact_info'] = self.value_formatter.format_contact_info(name, phones)
            else:
                formatted_data['contact_info'] = "Информация не указана"

            # Дата публикации
            if property_obj.published_at:
                formatted_data['published_at'] = self.value_formatter.format_date(property_obj.published_at.strftime("%Y-%m-%d %H:%M:%S"))
            else:
                formatted_data['published_at'] = "Дата не указана"

        # Дополнительные поля для шаблонов категорий
        formatted_data['property_type'] = property_obj.property_type if hasattr(property_obj, 'property_type') and property_obj.property_type else "не указан"
        formatted_data['purpose'] = "не указано"

        # Добавляем поля, которые могут использоваться в шаблонах
        # Площади
        formatted_data['kitchen_area'] = property_obj.area.kitchen if property_obj.area and property_obj.area.kitchen else "не указана"

        # Площадь участка - проверяем в additional_info, если нет в area
        if property_obj.area and property_obj.area.land:
            # Если площадь участка в кв.м, преобразуем в сотки
            if property_obj.area.land > 50:  # Если площадь больше 50, скорее всего она в кв.м
                formatted_data['land_area'] = property_obj.area.land / 100  # Переводим из кв.м в сотки
            else:
                formatted_data['land_area'] = property_obj.area.land
        elif property_obj.additional_info and 'land_area' in property_obj.additional_info:
            # Удаляем слово "соток" из значения, так как оно уже добавлено в шаблоне
            land_area = property_obj.additional_info['land_area']
            if isinstance(land_area, str) and 'соток' in land_area:
                # Извлекаем число из строки вида "10 соток"
                import re
                match = re.search(r'(\d+(?:[.,]\d+)?)', land_area)
                if match:
                    formatted_data['land_area'] = match.group(1)
                else:
                    formatted_data['land_area'] = land_area.replace('соток', '').strip()
            else:
                formatted_data['land_area'] = land_area
        else:
            formatted_data['land_area'] = "не указана"

        formatted_data['total_area'] = property_obj.area.total if property_obj.area and property_obj.area.total else "не указана"
        formatted_data['living_area'] = property_obj.area.living if property_obj.area and property_obj.area.living else "не указана"

        # Контакты
        formatted_data['contact_name'] = property_obj.contact.name if property_obj.contact and property_obj.contact.name else "не указано"
        formatted_data['contact_phone'] = property_obj.contact.phones[0] if property_obj.contact and property_obj.contact.phones else "не указано"

        # Этажи и тип дома
        # Этаж
        if property_obj.floor is not None:
            formatted_data['floor'] = str(property_obj.floor)
        else:
            formatted_data['floor'] = "не указан"

        # Этажность
        if property_obj.total_floors is not None:
            formatted_data['total_floors'] = str(property_obj.total_floors)
        else:
            formatted_data['total_floors'] = "не указано"

        # Информация об этаже
        formatted_data['floor_info'] = self.value_formatter.format_floor_info(property_obj.floor, property_obj.total_floors)

        # Тип дома
        if hasattr(property_obj, 'building_type') and property_obj.building_type is not None:
            formatted_data['building_type'] = property_obj.building_type
        elif property_obj.property_type:
            formatted_data['building_type'] = property_obj.property_type
        else:
            formatted_data['building_type'] = "не указан"

        # Комнаты
        if property_obj.rooms is not None:
            formatted_data['rooms_count'] = str(property_obj.rooms)
        else:
            formatted_data['rooms_count'] = "не указано"

        # Дата публикации для всех форматов
        if property_obj.published_at:
            formatted_data['published_at'] = self.value_formatter.format_date(property_obj.published_at.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            formatted_data['published_at'] = "Дата не указана"

        return formatted_data

    def format_to_string(self, data: Union[Dict[str, Any], Property], template: str, format_type: str = 'full') -> str:
        """
        Форматирует данные объекта недвижимости в строку по шаблону.

        Args:
            data: Данные объекта недвижимости или объект Property
            template: Шаблон для форматирования
            format_type: Тип форматирования ('full', 'short', 'list')

        Returns:
            str: Отформатированная строка
        """
        # Получаем отформатированные данные
        formatted_data = self.format(data, format_type)

        # Фильтруем данные, удаляя поля со значением "не указан" и его склонениями
        filtered_data = FieldFilter.filter_formatted_data(formatted_data)

        # Фильтруем шаблон, удаляя строки с полями, которые имеют значение "не указан" и его склонения
        filtered_template = FieldFilter.filter_template_fields(template, formatted_data)

        # Форматируем строку с использованием отфильтрованных данных и шаблона
        return filtered_template.format(**filtered_data)
