"""
Загрузка переменных окружения из .env файла.
"""
import os
from pathlib import Path
from typing import Dict, Optional

from dotenv import load_dotenv


def load_env(env_file: Optional[str] = None) -> Dict[str, str]:
    """
    Загружает переменные окружения из разных источников:
    1. Секреты Cloud66 (из директории /mnt/cloud66/secrets)
    2. Переменные окружения системы
    3. Файл .env

    Args:
        env_file: Путь к .env файлу (опционально)

    Returns:
        Dict[str, str]: Словарь с переменными окружения
    """
    # Проверяем наличие ключевых переменных окружения
    key_vars = ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_IDS', 'TELEGRAM_ADMIN_USERS']
    if any(key in os.environ for key in key_vars):
        print("[Инфо] Переменные окружения уже установлены, пропускаем загрузку из других источников")
        return dict(os.environ)

    # Проверяем наличие секретов Cloud66
    cloud66_secrets_dir = '/mnt/cloud66/secrets'
    if os.path.exists(cloud66_secrets_dir):
        print(f"[Инфо] Найдена директория секретов Cloud66: {cloud66_secrets_dir}")
        # Загружаем секреты в переменные окружения
        for secret_file in os.listdir(cloud66_secrets_dir):
            secret_path = os.path.join(cloud66_secrets_dir, secret_file)
            if os.path.isfile(secret_path):
                try:
                    with open(secret_path, 'r') as f:
                        secret_value = f.read().strip()
                        os.environ[secret_file] = secret_value
                        print(f"[Инфо] Загружен секрет: {secret_file}")
                except Exception as e:
                    print(f"[Ошибка] Не удалось загрузить секрет {secret_file}: {e}")

        # Проверяем, загружен ли секрет TELEGRAM_ERROR_CHAT_ID
        if 'TELEGRAM_ERROR_CHAT_ID' in os.environ:
            print(f"[Инфо] Секрет TELEGRAM_ERROR_CHAT_ID успешно загружен: {os.environ['TELEGRAM_ERROR_CHAT_ID']}")
        else:
            print("[Предупреждение] Секрет TELEGRAM_ERROR_CHAT_ID не найден в директории секретов")

    # Проверяем, загружены ли ключевые переменные из секретов
    if any(key in os.environ for key in key_vars):
        print("[Инфо] Ключевые переменные загружены из секретов Cloud66")
        return dict(os.environ)

    # Если не нашли переменные в секретах, пробуем загрузить из .env
    # Определяем путь к .env файлу
    if env_file is None:
        # Ищем .env файл в корне проекта
        project_root = Path(__file__).parent.parent.parent
        env_file = str(project_root / '.env')

    # Проверяем существование файла .env
    if os.path.exists(env_file):
        print(f"[Инфо] Загрузка переменных окружения из файла: {env_file}")
        # Загружаем переменные окружения
        load_dotenv(env_file)
    else:
        print(f"[Предупреждение] Файл .env не найден: {env_file}")
        print("[Инфо] Используются только переменные окружения из системы")

    # Возвращаем словарь с переменными окружения
    return dict(os.environ)


def get_env(key: str, default: Optional[str] = None) -> Optional[str]:
    """
    Возвращает значение переменной окружения.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)

    Returns:
        Optional[str]: Значение переменной окружения или значение по умолчанию
    """
    return os.environ.get(key, default)


def get_env_bool(key: str, default: bool = False) -> bool:
    """
    Возвращает булево значение переменной окружения.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)

    Returns:
        bool: Булево значение переменной окружения или значение по умолчанию
    """
    value = get_env(key)
    if value is None:
        return default

    return value.lower() in ('true', 'yes', '1', 'y', 't')


def get_env_int(key: str, default: Optional[int] = None) -> Optional[int]:
    """
    Возвращает целочисленное значение переменной окружения.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)

    Returns:
        Optional[int]: Целочисленное значение переменной окружения или значение по умолчанию
    """
    value = get_env(key)
    if value is None:
        return default

    try:
        return int(value)
    except ValueError:
        return default


def get_env_float(key: str, default: Optional[float] = None) -> Optional[float]:
    """
    Возвращает значение переменной окружения с плавающей точкой.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)

    Returns:
        Optional[float]: Значение переменной окружения с плавающей точкой или значение по умолчанию
    """
    value = get_env(key)
    if value is None:
        return default

    try:
        return float(value)
    except ValueError:
        return default


def get_env_list(key: str, default: Optional[list] = None, separator: str = ',') -> Optional[list]:
    """
    Возвращает список значений переменной окружения.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)
        separator: Разделитель значений (опционально)

    Returns:
        Optional[list]: Список значений переменной окружения или значение по умолчанию
    """
    value = get_env(key)
    if value is None:
        return default

    # Получаем список строк
    items = [item.strip() for item in value.split(separator) if item.strip()]

    # Пытаемся преобразовать строки в числа, если это возможно
    # Это особенно важно для TELEGRAM_CHAT_IDS
    if key == 'TELEGRAM_CHAT_IDS':
        try:
            return [int(item) for item in items]
        except (ValueError, TypeError):
            # Если не удалось преобразовать, возвращаем исходный список строк
            pass

    return items


def get_env_dict(key: str, default: Optional[Dict] = None, separator: str = ',',
                key_value_separator: str = '=') -> Optional[Dict]:
    """
    Возвращает словарь значений переменной окружения.

    Args:
        key: Ключ переменной окружения
        default: Значение по умолчанию (опционально)
        separator: Разделитель пар ключ-значение (опционально)
        key_value_separator: Разделитель ключа и значения (опционально)

    Returns:
        Optional[Dict]: Словарь значений переменной окружения или значение по умолчанию
    """
    value = get_env(key)
    if value is None:
        return default

    result = {}
    for item in value.split(separator):
        if key_value_separator in item:
            k, v = item.split(key_value_separator, 1)
            result[k.strip()] = v.strip()

    return result
