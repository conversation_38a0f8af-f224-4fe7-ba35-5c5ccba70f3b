"""
Загрузка и валидация настроек.
"""
import json
import os
from typing import Any, Dict, Optional

import yaml

from src.config.constants import (CONFIG_DIR, DEFAULT_LOG_FILE, DEFAULT_LOG_LEVEL,
                                DEFAULT_STORAGE_FILE, LOGGING_CONFIG_FILE,
                                OLX_CONFIG_FILE, ROOT_DIR, SCHEDULER_CONFIG_FILE,
                                STORAGE_CONFIG_FILE, TELEGRAM_CONFIG_FILE, WEBHOOK_CONFIG_FILE)
from src.config.dotenv import (get_env, get_env_bool, get_env_int,
                             get_env_list, load_env)


def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """
    Загружает конфигурацию из YAML файла.

    Args:
        file_path: Путь к файлу конфигурации

    Returns:
        Dict[str, Any]: Словарь с конфигурацией
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            # Проверяем, что загруженная конфигурация является словарем
            if isinstance(config, dict):
                return config
            else:
                print(f"Предупреждение: конфигурация из {file_path} не является словарем, возвращаем пустой словарь")
                return {}
    except Exception as e:
        print(f"Ошибка при загрузке конфигурации из {file_path}: {e}")
        return {}


def get_env_var(name: str, default: Optional[str] = None) -> Optional[str]:
    """
    Получает значение переменной окружения.

    Args:
        name: Имя переменной окружения
        default: Значение по умолчанию (опционально)

    Returns:
        Optional[str]: Значение переменной окружения или значение по умолчанию
    """
    # Загружаем переменные окружения из .env файла
    load_env()

    return get_env(name, default)


def load_storage_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию хранилища.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией хранилища
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, STORAGE_CONFIG_FILE)

    # Загружаем конфигурацию из файла
    config = load_yaml_config(config_path)

    # Используем значения из файла конфигурации
    if 'file_path' not in config:
        config['file_path'] = DEFAULT_STORAGE_FILE

    # Настройка максимального времени хранения ID объявлений
    if 'max_storage_days' not in config:
        config['max_storage_days'] = 30  # По умолчанию 30 дней

    # Настройки форматирования JSON
    if 'formatting' not in config:
        config['formatting'] = {}

    # Устанавливаем значения по умолчанию, если они не указаны
    if 'indent' not in config['formatting']:
        config['formatting']['indent'] = 2

    if 'ensure_ascii' not in config['formatting']:
        config['formatting']['ensure_ascii'] = False

    return config


def load_logging_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию логирования.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией логирования
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, LOGGING_CONFIG_FILE)

    # Загружаем конфигурацию из файла
    config = load_yaml_config(config_path)

    # Используем значения из файла конфигурации
    if 'file' not in config:
        config['file'] = DEFAULT_LOG_FILE

    if 'level' not in config:
        config['level'] = DEFAULT_LOG_LEVEL

    # Настройки форматирования
    if 'format' not in config:
        config['format'] = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    if 'date_format' not in config:
        config['date_format'] = '%Y-%m-%d %H:%M:%S'

    return config


def load_telegram_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию Telegram.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией Telegram
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, TELEGRAM_CONFIG_FILE)

    # Загружаем конфигурацию из файла
    config = load_yaml_config(config_path)

    # Преобразуем chat_ids в целые числа, если они есть в конфигурации
    if 'chat_ids' in config and isinstance(config['chat_ids'], list):
        try:
            normalized_chat_ids = []
            for chat_id in config['chat_ids']:
                if isinstance(chat_id, str):
                    normalized_chat_ids.append(int(chat_id))
                else:
                    normalized_chat_ids.append(chat_id)
            config['chat_ids'] = normalized_chat_ids
        except (ValueError, TypeError) as e:
            print(f"Ошибка при преобразовании ID чата из YAML в число: {e}")

    # Устанавливаем значения из переменных окружения, если они указаны
    token = get_env_var('TELEGRAM_BOT_TOKEN')
    if token:
        config['token'] = token

    # Получаем список ID чатов
    chat_ids = get_env_list('TELEGRAM_CHAT_IDS')
    if chat_ids:
        # Преобразуем строки в целые числа
        try:
            config['chat_ids'] = [int(chat_id) for chat_id in chat_ids]
        except ValueError as e:
            print(f"Ошибка при преобразовании ID чата в число: {e}")

    # Получаем ID чата для отправки уведомлений об ошибках
    error_chat_id = get_env_var('TELEGRAM_ERROR_CHAT_ID')
    if error_chat_id:
        try:
            config['error_chat_id'] = int(error_chat_id)
        except ValueError as e:
            print(f"Ошибка при преобразовании ID чата для ошибок в число: {e}")

    # Получаем список разрешенных пользователей
    allowed_users = get_env_list('TELEGRAM_ALLOWED_USERS')
    if allowed_users:
        # Преобразуем строки в целые числа
        config['allowed_users'] = [int(user_id) for user_id in allowed_users if user_id.isdigit()]

    # Получаем список администраторов
    admin_users = get_env_list('TELEGRAM_ADMIN_USERS')
    if admin_users:
        # Преобразуем строки в целые числа
        config['admin_users'] = [int(user_id) for user_id in admin_users if user_id.isdigit()]

    # Получаем ID суперадминистратора
    super_admin_id = get_env_var('TELEGRAM_SUPER_ADMIN_ID')
    if super_admin_id and super_admin_id.isdigit():
        config['super_admin_id'] = int(super_admin_id)

    return config


def get_timezone() -> str:
    """
    Получает часовой пояс из конфигурации или переменной окружения.

    Returns:
        str: Идентификатор часового пояса
    """
    # Сначала проверяем переменную окружения TZ
    tz_from_env = os.environ.get('TZ')
    if tz_from_env:
        return tz_from_env

    # Затем пытаемся получить из конфигурации
    try:
        config = load_olx_config()
        tz_from_config = config.get('system', {}).get('timezone')
        if tz_from_config:
            return tz_from_config
    except Exception:
        # Если не удалось загрузить конфигурацию, используем значение по умолчанию
        pass

    # По умолчанию используем Europe/Kiev
    return 'Europe/Kiev'


def load_olx_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию OLX.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией OLX
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, OLX_CONFIG_FILE)

    # Загружаем конфигурацию из файла
    config = load_yaml_config(config_path)

    # Загружаем категории из JSON-файла
    categories_file = os.path.join(os.path.dirname(__file__), "data/categories.json")
    if not os.path.exists(categories_file):
        categories_file = os.path.join(ROOT_DIR, "config/data/categories.json")

    if os.path.exists(categories_file):
        try:
            with open(categories_file, 'r', encoding='utf-8') as f:
                categories_data = json.load(f)
                # Преобразуем список категорий в словарь для совместимости с существующим кодом
                categories = {}
                for category in categories_data.get("categories", []):
                    if category.get("active", False):
                        category_id = category.get("id")
                        categories[category_id] = {
                            "url": category.get("url", ""),
                            "name": category.get("name", ""),
                            "type": category.get("type", ""),
                            "subtype": category.get("subtype", ""),
                            "active": True
                        }
                # Заменяем категории в конфигурации
                config["categories"] = categories
                print(f"Загружено {len(categories)} категорий из {categories_file}")
        except Exception as e:
            print(f"Ошибка при загрузке категорий из {categories_file}: {e}")

    # Настройки Zyte API
    zyte_api_key = get_env_var('ZYTE_API_KEY')
    zyte_api_url = get_env_var('ZYTE_API_URL')

    if zyte_api_key or zyte_api_url:
        if 'zyte' not in config:
            config['zyte'] = {}

        if zyte_api_key:
            config['zyte']['api_key'] = zyte_api_key

        if zyte_api_url:
            config['zyte']['api_url'] = zyte_api_url

    # Настройки авторизации OLX
    olx_login = get_env_var('OLX_LOGIN')
    olx_password = get_env_var('OLX_PASSWORD')

    if olx_login or olx_password:
        if 'auth' not in config:
            config['auth'] = {}

        if olx_login:
            config['auth']['login'] = olx_login

        if olx_password:
            config['auth']['password'] = olx_password

    # Настройки парсера и запросов берутся из файла конфигурации

    return config


def load_scheduler_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию планировщика.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией планировщика
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, SCHEDULER_CONFIG_FILE)

    # Базовая конфигурация
    config = {
        'enabled': True,
        'interval': 3600,  # 1 час
        'start_delay': 0,  # Без задержки
        'categories': [],
        'schedule': {
            'enabled': True,
            'start_time': "08:00",
            'end_time': "20:00",
            'interval_hours': 2
        }
    }

    # Загружаем конфигурацию из файла, если он существует
    if os.path.exists(config_path):
        file_config = load_yaml_config(config_path)
        # Объединяем базовую конфигурацию с загруженной из файла
        config.update(file_config)

    # Устанавливаем значения из переменных окружения, если они указаны
    if 'SCHEDULER_ENABLED' in os.environ:
        config['enabled'] = get_env_bool('SCHEDULER_ENABLED')

    interval = get_env_int('SCHEDULER_INTERVAL')
    if interval:
        config['interval'] = interval

    start_delay = get_env_int('SCHEDULER_START_DELAY')
    if start_delay is not None:
        config['start_delay'] = start_delay

    categories = get_env_list('SCHEDULER_CATEGORIES')
    if categories:
        config['categories'] = categories

    # Настройки расписания из переменных окружения
    if 'SCHEDULER_SCHEDULE_ENABLED' in os.environ:
        config['schedule']['enabled'] = get_env_bool('SCHEDULER_SCHEDULE_ENABLED')

    start_time = get_env_var('SCHEDULER_START_TIME')
    if start_time:
        config['schedule']['start_time'] = start_time

    end_time = get_env_var('SCHEDULER_END_TIME')
    if end_time:
        config['schedule']['end_time'] = end_time

    interval_hours = get_env_int('SCHEDULER_INTERVAL_HOURS')
    if interval_hours:
        config['schedule']['interval_hours'] = interval_hours

    return config


def load_webhook_config() -> Dict[str, Any]:
    """
    Загружает конфигурацию вебхука.

    Returns:
        Dict[str, Any]: Словарь с конфигурацией вебхука
    """
    # Путь к файлу конфигурации
    config_path = os.path.join(CONFIG_DIR, WEBHOOK_CONFIG_FILE)

    # Базовая конфигурация
    config = {
        'enabled': False,
        'url': '',
        'http': {
            'timeout': 10,
            'retries': 3,
            'retry_delay': 1,
            'headers': {
                'Content-Type': 'application/json'
            }
        }
    }

    # Загружаем конфигурацию из файла, если он существует
    if os.path.exists(config_path):
        file_config = load_yaml_config(config_path)
        # Объединяем базовую конфигурацию с загруженной из файла
        config.update(file_config)

    # Устанавливаем значения из переменных окружения, если они указаны
    if 'WEBHOOK_NOTIFICATION_ENABLED' in os.environ:
        config['enabled'] = get_env_bool('WEBHOOK_NOTIFICATION_ENABLED')

    webhook_url = get_env_var('WEBHOOK_NOTIFICATION_URL')
    if webhook_url:
        config['url'] = webhook_url

    # Настройки HTTP-запроса из переменных окружения
    timeout = get_env_int('WEBHOOK_HTTP_TIMEOUT')
    if timeout:
        config['http']['timeout'] = timeout

    retries = get_env_int('WEBHOOK_HTTP_RETRIES')
    if retries is not None:
        config['http']['retries'] = retries

    retry_delay = get_env_int('WEBHOOK_HTTP_RETRY_DELAY')
    if retry_delay is not None:
        config['http']['retry_delay'] = retry_delay

    return config


def load_config() -> Dict[str, Any]:
    """
    Загружает все конфигурации.

    Returns:
        Dict[str, Any]: Словарь со всеми конфигурациями
    """
    return {
        'storage': load_storage_config(),
        'logging': load_logging_config(),
        'telegram': load_telegram_config(),
        'olx': load_olx_config(),
        'scheduler': load_scheduler_config(),
        'webhook': load_webhook_config()
    }
