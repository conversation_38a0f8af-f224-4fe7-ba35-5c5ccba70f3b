"""
Централизованные настройки приложения.

Этот модуль предоставляет единую точку доступа к настройкам приложения
через класс Settings, который реализует паттерн Singleton.
"""
import os
import logging
from typing import Any, Dict, List, Optional

from src.config.settings import load_config


class Settings:
    """
    Класс для централизованного управления настройками приложения.
    """
    _instance = None

    def __new__(cls):
        """
        Реализация паттерна Singleton для настроек.
        """
        if cls._instance is None:
            cls._instance = super(Settings, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """
        Инициализирует настройки приложения.
        """
        if self._initialized:
            return

        # Загружаем все конфигурации
        self._config = load_config()

        # Инициализируем свойства для удобного доступа
        self._storage = self._config.get('storage', {})
        self._logging = self._config.get('logging', {})
        self._telegram = self._config.get('telegram', {})
        self._olx = self._config.get('olx', {})
        self._scheduler = self._config.get('scheduler', {})
        self._webhook = self._config.get('webhook', {})

        self._initialized = True

        # Настраиваем логирование
        self._setup_logging()

    def _setup_logging(self):
        """
        Настраивает логирование на основе загруженных настроек.
        """
        log_level = self.get_log_level()
        log_file = self.get_log_file_path()

        # Создаем директорию для логов, если она не существует
        if log_file:
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

        # Настраиваем корневой логгер
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=self._logging.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt=self._logging.get('date_format', '%Y-%m-%d %H:%M:%S'),
            filename=log_file,
            filemode='a'
        )

        # Добавляем вывод в консоль
        console = logging.StreamHandler()
        console.setLevel(getattr(logging, log_level))
        formatter = logging.Formatter(
            self._logging.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            self._logging.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
        console.setFormatter(formatter)
        logging.getLogger('').addHandler(console)

    @property
    def storage(self) -> Dict[str, Any]:
        """
        Возвращает настройки хранилища.

        Returns:
            Dict[str, Any]: Настройки хранилища
        """
        return self._storage

    @property
    def logging(self) -> Dict[str, Any]:
        """
        Возвращает настройки логирования.

        Returns:
            Dict[str, Any]: Настройки логирования
        """
        return self._logging

    @property
    def telegram(self) -> Dict[str, Any]:
        """
        Возвращает настройки Telegram.

        Returns:
            Dict[str, Any]: Настройки Telegram
        """
        return self._telegram

    @property
    def olx(self) -> Dict[str, Any]:
        """
        Возвращает настройки OLX.

        Returns:
            Dict[str, Any]: Настройки OLX
        """
        return self._olx

    @property
    def scheduler(self) -> Dict[str, Any]:
        """
        Возвращает настройки планировщика.

        Returns:
            Dict[str, Any]: Настройки планировщика
        """
        return self._scheduler

    def reload(self) -> None:
        """
        Перезагружает все настройки.
        """
        self._config = load_config()
        self._storage = self._config.get('storage', {})
        self._logging = self._config.get('logging', {})
        self._telegram = self._config.get('telegram', {})
        self._olx = self._config.get('olx', {})
        self._scheduler = self._config.get('scheduler', {})
        self._webhook = self._config.get('webhook', {})

        # Обновляем логирование
        self._setup_logging()

    # Методы для получения конкретных настроек

    def get_storage_file_path(self) -> str:
        """
        Возвращает путь к файлу хранилища.

        Returns:
            str: Путь к файлу хранилища
        """
        return self._storage.get('file_path', 'processed_ads.json')

    def get_storage_max_days(self) -> int:
        """
        Возвращает максимальное время хранения ID объявлений в днях.

        Returns:
            int: Максимальное время хранения в днях
        """
        return self._storage.get('max_storage_days', 30)

    def get_log_file_path(self) -> Optional[str]:
        """
        Возвращает путь к файлу логов.

        Returns:
            Optional[str]: Путь к файлу логов
        """
        return self._logging.get('file')

    def get_log_level(self) -> str:
        """
        Возвращает уровень логирования.

        Returns:
            str: Уровень логирования
        """
        return self._logging.get('level', 'INFO')

    def get_telegram_token(self) -> Optional[str]:
        """
        Возвращает токен Telegram бота.

        Returns:
            Optional[str]: Токен Telegram бота
        """
        return self._telegram.get('token')

    def get_telegram_admin_users(self) -> List[int]:
        """
        Возвращает список ID администраторов Telegram бота.

        Returns:
            List[int]: Список ID администраторов
        """
        return self._telegram.get('admin_users', [])

    def get_telegram_chat_ids(self) -> List[int]:
        """
        Возвращает список ID чатов Telegram.

        Returns:
            List[int]: Список ID чатов Telegram
        """
        chat_ids = self._telegram.get('chat_ids') or []

        # Преобразуем все ID чатов в целые числа
        normalized_chat_ids = []
        for chat_id in chat_ids:
            try:
                if isinstance(chat_id, str):
                    normalized_chat_ids.append(int(chat_id))
                else:
                    normalized_chat_ids.append(chat_id)
            except (ValueError, TypeError):
                # Пропускаем некорректные ID чатов
                pass

        return normalized_chat_ids

    def get_telegram_error_chat_id(self) -> Optional[int]:
        """
        Возвращает ID чата Telegram для отправки уведомлений об ошибках.

        Returns:
            Optional[int]: ID чата Telegram для отправки уведомлений об ошибках или None, если не указан
        """
        # Сначала проверяем наличие переменной окружения
        env_error_chat_id = os.environ.get('TELEGRAM_ERROR_CHAT_ID')
        if env_error_chat_id:
            print(f"DEBUG: Используется переменная окружения TELEGRAM_ERROR_CHAT_ID: {env_error_chat_id}")
            try:
                return int(env_error_chat_id)
            except (ValueError, TypeError):
                print(f"DEBUG: Неверный формат ID чата в переменной окружения: {env_error_chat_id}")

        # Затем проверяем наличие секрета в директории секретов
        cloud66_secrets_dir = '/mnt/cloud66/secrets'
        if os.path.exists(cloud66_secrets_dir):
            secret_path = os.path.join(cloud66_secrets_dir, 'TELEGRAM_ERROR_CHAT_ID')
            if os.path.isfile(secret_path):
                try:
                    with open(secret_path, 'r') as f:
                        secret_error_chat_id = f.read().strip()
                        print(f"DEBUG: Загружен секрет TELEGRAM_ERROR_CHAT_ID из файла: {secret_error_chat_id}")
                        try:
                            return int(secret_error_chat_id)
                        except (ValueError, TypeError):
                            print(f"DEBUG: Неверный формат ID чата в секрете: {secret_error_chat_id}")
                except Exception as e:
                    print(f"DEBUG: Ошибка при чтении секрета TELEGRAM_ERROR_CHAT_ID: {e}")

        # Наконец, проверяем наличие в конфигурации
        error_chat_id = self._telegram.get('error_chat_id')
        if error_chat_id:
            print(f"DEBUG: Используется значение из конфигурации: {error_chat_id}")
            try:
                if isinstance(error_chat_id, str):
                    return int(error_chat_id)
                return error_chat_id
            except (ValueError, TypeError):
                print(f"DEBUG: Неверный формат ID чата в конфигурации: {error_chat_id}")

        print("DEBUG: Не найден ID чата для отправки уведомлений об ошибках")
        return None

    def get_olx_parser_config(self) -> Dict[str, Any]:
        """
        Возвращает конфигурацию парсера OLX.

        Returns:
            Dict[str, Any]: Конфигурация парсера OLX
        """
        return self._olx

    def is_webhook_enabled(self) -> bool:
        """
        Возвращает статус включения вебхука.

        Returns:
            bool: True, если вебхук включен, иначе False
        """
        return self._webhook.get('enabled', False)

    def get_webhook_url(self) -> str:
        """
        Возвращает URL вебхука.

        Returns:
            str: URL вебхука
        """
        return self._webhook.get('url', '')

    def get_webhook_http_config(self) -> Dict[str, Any]:
        """
        Возвращает конфигурацию HTTP-запроса для вебхука.

        Returns:
            Dict[str, Any]: Конфигурация HTTP-запроса
        """
        return self._webhook.get('http', {})

    def get_scheduler_interval(self) -> int:
        """
        Возвращает интервал планировщика в секундах.

        Returns:
            int: Интервал планировщика в секундах
        """
        return self._scheduler.get('interval', 3600)

    def get_scheduler_enabled(self) -> bool:
        """
        Возвращает статус включения планировщика.

        Returns:
            bool: True, если планировщик включен, иначе False
        """
        return self._scheduler.get('enabled', True)

    def get_scheduler_categories(self) -> List[str]:
        """
        Возвращает список категорий для планировщика.

        Returns:
            List[str]: Список категорий для планировщика
        """
        return self._scheduler.get('categories', [])


# Создаем глобальный экземпляр настроек
settings = Settings()