"""
Пути к директориям и файлам проекта.
"""
import os

# Корневая директория проекта
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# Директории
CONFIG_DIR = os.path.join(ROOT_DIR, 'config')
LOGS_DIR = os.path.join(ROOT_DIR, 'logs')
DATA_DIR = os.path.join(ROOT_DIR, 'data')
SRC_DIR = os.path.join(ROOT_DIR, 'src')
TESTS_DIR = os.path.join(ROOT_DIR, 'tests')

# Создаем директории, если они не существуют
for directory in [CONFIG_DIR, LOGS_DIR, DATA_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

# Файлы конфигу<PERSON><PERSON><PERSON>ии
def get_config_path(filename: str) -> str:
    """
    Возвращает полный путь к файлу конфигурации.
    
    Args:
        filename: Имя файла конфигурации
        
    Returns:
        str: Полный путь к файлу конфигурации
    """
    return os.path.join(CONFIG_DIR, filename)

# Файлы данных
def get_data_path(filename: str) -> str:
    """
    Возвращает полный путь к файлу данных.
    
    Args:
        filename: Имя файла данных
        
    Returns:
        str: Полный путь к файлу данных
    """
    return os.path.join(DATA_DIR, filename)

# Файлы логов
def get_log_path(filename: str) -> str:
    """
    Возвращает полный путь к файлу лога.
    
    Args:
        filename: Имя файла лога
        
    Returns:
        str: Полный путь к файлу лога
    """
    return os.path.join(LOGS_DIR, filename)
