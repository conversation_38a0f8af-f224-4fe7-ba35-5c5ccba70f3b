"""
Конфигурация логирования для проекта Parser OLX.
"""
import logging
import os
from logging.handlers import RotatingFileHandler

# Проверяем доступность модулей Sentry и JSON Logger
# Префикс _ указывает, что мы не используем эти импорты напрямую, а только проверяем их наличие
try:
    # Проверяем только наличие модулей, импортируем их внутри функций
    import sentry_sdk as _sentry_sdk  # noqa: F401
    import pythonjsonlogger.jsonlogger as _jsonlogger  # noqa: F401
    SENTRY_AVAILABLE = True
except ImportError:
    SENTRY_AVAILABLE = False


def setup_sentry():
    """Настройка Sentry."""
    if not SENTRY_AVAILABLE:
        return

    # Импортируем sentry_sdk внутри функции, чтобы избежать ошибок типизации
    import sentry_sdk

    sentry_dsn = os.getenv("SENTRY_DSN")
    if not sentry_dsn:
        return

    sentry_sdk.init(
        dsn=sentry_dsn,
        traces_sample_rate=1.0,
        environment=os.getenv("ENVIRONMENT", "development"),
    )


def clear_log_file(log_file):
    """
    Очищает файл логов перед началом работы.

    Args:
        log_file: Путь к файлу логов.
    """
    if not log_file:
        return

    # Проверяем, существует ли директория для логов
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # Очищаем файл логов, если он существует
    try:
        # Открываем файл в режиме записи, что автоматически очищает его
        with open(log_file, 'w', encoding='utf-8') as f:
            # Записываем заголовок для нового файла логов
            f.write(f"=== Log file cleared at {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None))} ===\n")
    except Exception as e:
        print(f"Ошибка при очистке файла логов {log_file}: {e}")


def setup_logging(log_level=logging.INFO, log_file=None, logger_name="parser_olx", clear_logs=True):
    """
    Настройка логирования.

    Args:
        log_level: Уровень логирования.
        log_file: Путь к файлу логов.
        logger_name: Имя логгера.
        clear_logs: Очищать ли файл логов перед началом работы.

    Returns:
        Logger: Настроенный логгер.
    """
    # Очищаем файл логов, если нужно
    if clear_logs and log_file:
        clear_log_file(log_file)

    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)

    # Очистка обработчиков
    if logger.handlers:
        logger.handlers.clear()

    # Форматтер для консоли
    console_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Обработчик для консоли
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # Обработчик для файла
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        if SENTRY_AVAILABLE:
            # Форматтер для JSON
            # Импортируем jsonlogger внутри функции, чтобы избежать ошибок типизации
            from pythonjsonlogger import jsonlogger
            json_formatter = jsonlogger.JsonFormatter(
                "%(asctime)s %(name)s %(levelname)s %(message)s %(filename)s %(funcName)s %(lineno)d"
            )
            file_handler = RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
            file_handler.setFormatter(json_formatter)
        else:
            file_handler = RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5
            )
            file_handler.setFormatter(console_formatter)

        logger.addHandler(file_handler)

    return logger
