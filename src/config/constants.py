"""
Константы приложения.
"""
import os

# Пути
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
CONFIG_DIR = os.path.join(ROOT_DIR, 'config')
LOGS_DIR = os.path.join(ROOT_DIR, 'logs')
DATA_DIR = os.path.join(ROOT_DIR, 'data')

# Имена файлов конфигурации
STORAGE_CONFIG_FILE = 'storage.yaml'
LOGGING_CONFIG_FILE = 'logging/default.yaml'
TELEGRAM_CONFIG_FILE = 'telegram/bot.yaml'
OLX_CONFIG_FILE = 'parsers/olx.yaml'
SCHEDULER_CONFIG_FILE = 'scheduler.yaml'
WEBHOOK_CONFIG_FILE = 'webhook/notification.yaml'

# Значения по умолчанию
DEFAULT_STORAGE_FILE = os.path.join(DATA_DIR, 'processed_ads.json')
DEFAULT_LOG_FILE = os.path.join(LOGS_DIR, 'olx_parser.log')
DEFAULT_LOG_LEVEL = 'INFO'

# Таймауты
DEFAULT_REQUEST_TIMEOUT = 30  # секунды
DEFAULT_RETRY_TIMEOUT = 5  # секунды
MAX_RETRIES = 3

# Лимиты
DEFAULT_ADS_LIMIT = 100
MAX_ADS_LIMIT = 1000

# Форматы
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Telegram
TELEGRAM_PARSE_MODE = 'HTML'
TELEGRAM_MAX_MESSAGE_LENGTH = 4096
