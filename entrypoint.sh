#!/bin/bash

# Создание директорий и установка прав доступа
mkdir -p /app/logs /app/data
chmod -R 777 /app/logs /app/data
chown -R parser:parser /app/logs /app/data

# Вывод информации о правах доступа
echo "Permissions for /app/logs:"
ls -la /app/logs
echo "Permissions for /app/data:"
ls -la /app/data

# Вывод информации о времени
echo "Current date and time: $(date)"
echo "Timezone: $(cat /etc/timezone)"

# Запуск команды от имени пользователя parser
exec gosu parser "$@"
