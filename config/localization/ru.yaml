texts:
  # Тексты для главного меню
  main_menu_status: "📊 Статус"
  main_menu_notifications: "🔔 Уведомления"
  main_menu_categories: "🏠 Категории"
  main_menu_help: "❓ Помощь"
  main_menu_language: "🌐 Язык"
  main_menu_title: "Главное меню:"

  # Тексты для меню уведомлений
  notifications_menu_title: "🔔 Уведомления"
  notifications_menu_enable: "🔔 Включить уведомления"
  notifications_menu_disable: "🔕 Отключить уведомления"
  notifications_menu_back: "◀️ Назад в главное меню"

  # Тексты для категорий недвижимости
  category_apartments: "🏢 Квартиры"
  category_houses: "🏠 Дома"
  category_rooms: "🚪 Комнаты"
  category_garages: "🚗 Гаражи"
  category_dacha: "🏡 Дачи"
  category_land: "🌳 Земля"
  category_commercial: "🏭 Коммерческая"

  # Тексты для действий с категориями
  category_all: "✅ Все категории"
  category_clear: "❌ Очистить все"
  category_sale: "🛒 Продажа"
  category_rent: "💰 Аренда"
  category_back: "◀️ Назад к категориям"
  category_subscribe: "➕ Подписаться"
  category_unsubscribe: "➖ Отписаться"
  category_back_to_types: "◀️ Назад к типам"

  # Тексты для меню помощи
  help_commands: "📚 Команды"
  help_categories: "🏠 Категории"
  help_about: "❓ О боте"

  # Тексты для меню администратора
  admin_menu_title: "Меню администратора:"
  admin_menu_users: "👥 Пользователи"
  admin_menu_requests: "📝 Запросы"
  admin_menu_stats: "📊 Статистика"
  admin_menu_broadcast: "📢 Рассылка"
  admin_menu_notification_settings: "🔔 Настройки уведомлений"

  # Тексты для управления пользователями
  users_menu_title: "Управление пользователями:"
  users_menu_list: "📋 Список пользователей"
  users_menu_blocked: "🔒 Заблокированные"
  users_menu_add: "➕ Добавить пользователя"
  users_menu_remove: "➖ Удалить пользователя"

  # Тексты для списка пользователей
  users_list_title: "Список пользователей:"
  users_list_empty: "Список пользователей пуст."
  users_list_response: "Список пользователей"
  subscribed_to_categories: "Подписан на категории:"
  user_language: "Язык бота:"

  # Тексты для заблокированных пользователей
  blocked_list_title: "Заблокированные пользователи:"
  blocked_list_empty: "Список заблокированных пользователей пуст."
  no_blocked_users: "Нет заблокированных пользователей."
  blocked_list_response: "Список заблокированных пользователей"
  username_label: "Имя пользователя:"
  name_label: "Имя:"

  # Тексты для управления запросами
  requests_menu_title: "Управление запросами на доступ:"
  requests_menu_pending: "📋 Ожидающие запросы"
  requests_menu_approved: "✅ Одобренные запросы"
  requests_menu_rejected: "❌ Отклоненные запросы"
  requests_menu_refresh: "🔄 Обновить"
  requests_refresh_response: "Список запросов обновлен"
  requests_pending_response: "Список ожидающих запросов"
  requests_approved_response: "Список одобренных запросов"
  requests_rejected_response: "Список отклоненных запросов"
  requests_pending_title: "Ожидающие запросы на доступ:"
  requests_approved_title: "Одобренные запросы на доступ:"
  requests_rejected_title: "Отклоненные запросы на доступ:"
  requests_id: "ID:"
  requests_username: "Имя пользователя:"
  requests_name: "Имя:"
  requests_no_pending: "Нет ожидающих запросов."
  requests_no_approved: "Нет одобренных запросов."
  requests_no_rejected: "Нет отклоненных запросов."

  # Тексты для рассылки
  broadcast_menu_title: "Выберите группу пользователей для рассылки или используйте команду:\n/broadcast [all|users|admins] [текст сообщения]\n\nПример: /broadcast admins Завтра сборы"
  broadcast_all: "👥 Всем пользователям"
  broadcast_users: "👤 Только пользователям"
  broadcast_admins: "👑 Только администраторам"

  # Тексты для настроек уведомлений
  notification_settings_title: "Настройки уведомлений:"
  notification_status_enabled: "Включены"
  notification_status_disabled: "Отключены"
  notification_access_requests: "Уведомления о запросах на доступ"
  notification_subscribe: "🔔 Подписаться на запросы"
  notification_unsubscribe: "🔕 Отписаться от запросов"

  # Тексты для статистики
  stats_title: "📊 Статистика бота:"
  total_users: "👥 Всего пользователей:"
  admin_users: "👑 Администраторов:"
  regular_users: "👤 Обычных пользователей:"
  access_requests: "📝 Запросы на доступ:"
  pending_requests: "⏳ Ожидающие:"
  approved_requests: "✅ Одобренные:"
  rejected_requests: "❌ Отклоненные:"
  active_categories_count: "🏠 Активные категории:"

  # Тексты для команды admininfo
  admininfo_not_available: "Эта команда доступна только суперадминистратору."
  admininfo_super_admins: "Суперадминистраторы:"
  admininfo_current_admins: "Текущие администраторы:"
  admininfo_management: "Управление администраторами:"
  admininfo_super_admin_instructions: "Вы являетесь суперадминистратором и можете управлять администраторами с помощью следующих команд:"
  admininfo_add_admin: "добавить нового администратора"
  admininfo_remove_admin: "удалить администратора"
  admininfo_config_title: "Настройка через конфигурацию:"
  admininfo_config_file: "Изменить файл"
  admininfo_env_vars: "Или изменить переменные окружения:"
  admininfo_restart_required: "После изменения необходимо перезапустить бота."

  # Тексты для выбора языка
  language_menu_title: "Выберите язык:"
  language_ru: "🇷🇺 Русский"
  language_ua: "🇺🇦 Українська"
  language_ru_button: "🇷🇺 Русский"
  language_ua_button: "🇺🇦 Українська"
  language_changed: "✅ Ваш язык успешно изменен на {language}"
  language_not_supported: "Язык {language} не поддерживается"
  language_changed_instruction: "Язык изменен. Для обновления клавиатуры нажмите на любую кнопку или отправьте команду /start"
  welcome_message: "Привет! Добро пожаловать в бот! 👋\nВыберите язык:"
  current_language: "Текущий язык: {language}"
  select_language: "Выберите язык:"

  # Дополнительные тексты
  help_menu_title: "Выберите раздел справки:"
  categories_menu_title: "Выберите категорию:"
  categories_active: "Активные категории:"
  categories_empty: "У вас нет активных категорий."
  add_category_help: "Используйте команду /category add <категория> для добавления категории."
  status_running: "Запущен"
  bot_status: "Статус бота:"
  notifications_stopped: "Уведомления остановлены. Выберите категории для подписки или вернитесь в главное меню."
  category_type_menu_title: "Выберите тип операции для категории '{category_name}':"
  back_to_categories: "Возврат к выбору категорий"
  category_subscribed: "Вы подписались на категорию: {category}"
  active_categories: "Ваши активные категории:"
  choose_more_categories: "Выберите другие категории или вернитесь в главное меню:"
  categories_clear_message: "Все категории удалены. Выберите категории для подписки:"
  subscribed_all_categories: "Вы подписались на все категории ({count} шт.)"
  subscribed_all_categories_title: "Вы подписались на все категории:"
  help_categories_response: "Справка по категориям"
  available_categories: "Доступные категории:"
  open_ad_button: "Открыть объявление"
  open_ad_button_fallback: "Открыть объявление"
  keyboard_update_instruction: "Клавиатура обновлена. Нажмите на любую кнопку, чтобы увидеть изменения."

  # Тексты для запросов на доступ
  access_request_sent: "Ваш запрос на доступ отправлен администратору. Пожалуйста, ожидайте ответ."
  access_request_pending: "Ваш запрос на доступ находится на рассмотрении. Пожалуйста, ожидайте ответ администратора."
  access_request_approved: "Ваш запрос на доступ подтвержден. Теперь вы можете использовать бота."
  access_request_rejected: "Ваш запрос на доступ отклонен."
  access_request_approved_admin: "Запрос на доступ от пользователя {user_id} подтвержден."
  access_request_rejected_admin: "Запрос на доступ от пользователя {user_id} отклонен."
  access_revoked: "Ваш доступ к боту был отозван администратором."
  access_denied: "Извините, ваш доступ к боту был отклонен администратором."

templates:
  # Шаблоны для команд бота
  command_start: |
    Привет, {name}! Я бот для уведомлений о новых объявлениях OLX.

    Используйте команду /help для получения справки.

  command_help: |
    Доступные команды:

    /start - Начать работу с ботом
    /help - Показать справку
    /status - Показать статус бота
    /stop - Остановить уведомления
    /category add <категория> - Добавить категорию для уведомлений
    /category remove <категория> - Удалить категорию для уведомлений
    /category list - Показать список категорий для уведомлений
    /language - Изменить язык бота

    Доступные категории:
    - apartments_sale - Продажа квартир
    - apartments_rent - Аренда квартир
    - houses_sale - Продажа домов
    - houses_rent - Аренда домов
    - rooms_sale - Продажа комнат
    - rooms_rent - Аренда комнат
    - garages_sale - Продажа гаражей
    - garages_rent - Аренда гаражей
    - dacha_sale - Продажа дач
    - dacha_rent - Аренда дач
    - land_sale - Продажа земельных участков
    - commercial_sale - Продажа коммерческой недвижимости
    - commercial_rent - Аренда коммерческой недвижимости

  command_status: |
    Статус бота: {status}

    {categories_info}

  command_category_added: |
    Категория {category} добавлена. Вы будете получать уведомления о новых объявлениях в этой категории.

  command_category_removed: |
    Категория {category} удалена. Вы больше не будете получать уведомления о новых объявлениях в этой категории.

  command_category_list: |
    Активные категории:
    {categories}

  command_category_not_found: |
    Категория {category} не найдена. Используйте команду /help для просмотра списка доступных категорий.

  command_category_empty: |
    У вас нет активных категорий. Используйте команду /category add <категория> для добавления категории.

  command_error: |
    Произошла ошибка при обработке команды. Пожалуйста, попробуйте еще раз.

  # Шаблоны для уведомлений
  notification_new_property: |
    {property_info}

  notification_price_changed: |
    <b>💰 Изменение цены!</b>

    Объект: <b>{title}</b>
    Старая цена: {old_price}
    Новая цена: {new_price}
    Разница: {price_diff}

    <a href="{url}">Подробнее на OLX</a>

  notification_status: |
    <b>📊 Статус парсинга</b>

    Категория: <b>{category}</b>
    Новых объявлений: {new_count}
    Обработано: {processed_count}
    Ошибок: {error_count}

    Время выполнения: {execution_time}

  # Шаблон для информации о боте
  about_bot: |
    О боте:

    Этот бот предназначен для отправки уведомлений о новых объявлениях на OLX.

    Вы можете подписаться на различные категории недвижимости и получать уведомления о новых объявлениях в этих категориях.

    Версия: 1.0.0
    Разработчик: @vitalii820
