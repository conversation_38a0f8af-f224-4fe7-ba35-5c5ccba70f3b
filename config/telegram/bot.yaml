# Конфигурация Telegram бота

# ===== ИНСТРУКЦИЯ ПО ИСПОЛЬЗОВАНИЮ TELEGRAM БОТА =====

# 1. Настройка бота:
#    - Укажите токен бота в переменной окружения TELEGRAM_BOT_TOKEN или в поле token ниже
#    - Укажите ID чата для отправки уведомлений в переменной TELEGRAM_CHAT_IDS или в поле chat_ids
#    - Укажите ID администраторов в переменной TELEGRAM_ADMIN_USERS или в поле admin_users
#
# 2. Запуск бота:
#    - Запуск через модуль __main__.py: python -m src.presentation.telegram
#    - Запуск через модуль bot.py: python -m src.presentation.telegram.bot
#
# 3. Система подтверждения новых пользователей:
#    - Когда новый пользователь отправляет команду /start, бот создает запрос на доступ
#    - Администраторы получают уведомление с кнопками подтверждения/отклонения
#    - После подтверждения пользователь автоматически добавляется в список разрешенных
#
# 4. Команды бота:
#    - /start - Начать работу с ботом
#    - /help - Показать справку
#    - /status - Показать статус бота
#    - /stop - Остановить уведомления
#    - /category add <категория> - Добавить категорию для уведомлений
#    - /category remove <категория> - Удалить категорию для уведомлений
#    - /category list - Показать список категорий для уведомлений
#    - /category all - Подписаться сразу на все категории
#    - /filter on - Включить фильтр полей со значением 'не указан'
#    - /filter off - Выключить фильтр полей со значением 'не указан'
#
# 5. Доступные категории недвижимости:
#    - apartments_sale - Продажа квартир
#    - apartments_rent - Аренда квартир
#    - houses_sale - Продажа домов
#    - houses_rent - Аренда домов
#    - rooms_sale - Продажа комнат
#    - rooms_rent - Аренда комнат
#    - garages_sale - Продажа гаражей
#    - garages_rent - Аренда гаражей
#    - dacha_sale - Продажа дач
#    - dacha_rent - Аренда дач
#    - land_sale - Продажа земельных участков
#    - commercial_sale - Продажа коммерческой недвижимости
#    - commercial_rent - Аренда коммерческой недвижимости

# Токен бота (лучше указывать через переменную окружения TELEGRAM_BOT_TOKEN)
token: ""

# ID чата для отправки уведомлений (можно указать несколько)
# Лучше указывать через переменную окружения TELEGRAM_CHAT_IDS
chat_ids:           # Основной чат для уведомлений

# ID чата для отправки уведомлений об ошибках
# Лучше указывать через переменную окружения TELEGRAM_ERROR_CHAT_ID
error_chat_id: ""  # Чат для уведомлений об ошибках

# Разрешенные пользователи (список ID пользователей, которые могут использовать бота)
allowed_users:    # Ваш ID пользователя

# Суперадминистратор (пользователь, который может управлять администраторами)
super_admin_id:   # Ваш ID пользователя

# Администраторы бота (список ID пользователей, которые могут подтверждать доступ новых пользователей)
admin_users:     # Ваш ID пользователя

# Настройки сообщений
messages:
  # Режим форматирования текста (HTML, Markdown, MarkdownV2)
  parse_mode: HTML

  # Отключить предпросмотр ссылок
  disable_web_page_preview: false

  # Максимальное количество изображений в одном сообщении
  max_images: 1
