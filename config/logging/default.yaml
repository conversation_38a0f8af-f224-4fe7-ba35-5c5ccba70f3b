# Конфигурация логирования

# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
level: DEBUG

# Путь к файлу лога
file: logs/olx_parser.log

# Формат сообщений лога
format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Максимальный размер файла лога (в байтах)
max_file_size: 10485760  # 10 МБ

# Количество файлов ротации
backup_count: 5

# Очищать ли файл логов перед началом работы
clear_logs: true

# Настройки логгеров
loggers:
  # Корневой логгер
  root:
    level: DEBUG
    file: logs/olx_parser.log
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    clear_logs: true

  # Логгер парсера
  parser:
    level: DEBUG
    file: logs/parser.log
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    clear_logs: true

  # Логгер хранилища
  storage:
    level: DEBUG
    file: logs/storage.log
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    clear_logs: true

  # Логгер уведомлений
  notification:
    level: INFO
    file: logs/notification.log
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    clear_logs: true
