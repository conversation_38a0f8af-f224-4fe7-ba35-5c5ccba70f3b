# Конфигурация вебхука для отправки уведомлений

# ===== ИНСТРУКЦИЯ ПО ИСПОЛЬЗОВАНИЮ ВЕБХУКА =====

# 1. Настройка вебхука:
#    - Укажите URL вебхука в переменной окружения WEBHOOK_NOTIFICATION_URL или в поле url ниже
#    - Вебхук должен принимать POST-запросы с JSON-данными
#    - Формат данных описан ниже

# 2. Формат данных:
#    - type: Тип уведомления (text, image, images, error)
#    - message: Текст сообщения
#    - chat_id: ID чата/пользователя (используется как идентификатор)
#    - parse_mode: Режим форматирования текста (HTML, Markdown, MarkdownV2)
#    - image_url: URL изображения (для типа image)
#    - image_urls: Список URL изображений (для типа images)
#    - disable_web_page_preview: Отключить предпросмотр ссылок (для типа text)
#    - reply_markup: Разметка для интерактивных кнопок (опционально)

# Включить/выключить отправку уведомлений через вебхук
enabled: true

# URL вебхука для отправки уведомлений (лучше указывать через переменную окружения WEBHOOK_NOTIFICATION_URL)
url: ""

# Настройки HTTP-запроса
http:
  # Таймаут запроса в секундах
  timeout: 10

  # Количество попыток отправки при ошибке
  retries: 3

  # Задержка между попытками в секундах
  retry_delay: 1

  # Заголовки запроса
  headers:
    Content-Type: application/json
    # Можно добавить дополнительные заголовки, например, для авторизации
    # Authorization: Bearer your_token_here
