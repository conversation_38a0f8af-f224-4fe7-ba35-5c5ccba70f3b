# Конфигурация планировщика
#
# Примеры запуска планировщика:
#
# 1. Запуск без параметров (используются настройки из этого файла):
# python -m src.presentation.scheduler.main
# Принудительный запуск вне рабочего времени:
# python -m src.presentation.scheduler.main --force
#
# 2. Запуск с указанием категорий и интервала:
# python -m src.presentation.scheduler.main --categories apartments_sale,apartments_rent --interval 7200 --delay 0
#
# 3. Запуск с ограничением количества объявлений:
# python -m src.presentation.scheduler.main --categories commercial_sale,commercial_rent --interval 10800 --limit 15
#
# 4. Запуск с задержкой перед первым запуском:
# python -m src.presentation.scheduler.main --categories houses_sale,houses_rent --interval 10800 --delay 30
#
# 5. Запуск всех категорий с подробным выводом:
# python -m src.presentation.scheduler.main --categories apartments_sale,apartments_rent,houses_sale,houses_rent,rooms_sale,rooms_rent,garages_sale,garages_rent,dacha_sale,dacha_rent,land_sale,commercial_sale,commercial_rent --interval 7200 --verbose
#
# Оптимизация парсинга:
# Настройка parse_only_subscribed_categories позволяет запускать парсинг только для тех категорий,
# на которые подписаны пользователи в Telegram боте. Это позволяет снизить нагрузку на сервер
# и уменьшить количество запросов к OLX. Если у категории нет подписчиков, она будет пропущена.

# Основные настройки
enabled: true                # Включен ли планировщик
# enabled: false

# parse_only_subscribed_categories: true  # Парсить только категории, на которые подписаны пользователи
parse_only_subscribed_categories: false

# Расписание работы
schedule:
  enabled: true              # Включено ли расписание
  start_time: "08:00"        # Время начала работы планировщика
  end_time: "19:00"          # Время окончания работы планировщика
  interval_hours: 2          # Интервал запуска в часах
  days_of_week: [1, 2, 3, 4, 5, 6, 7]  # Дни недели (1-7, где 1 - понедельник, 7 - воскресенье)


# Общие настройки
interval: 7200               # Интервал запуска в секундах (2 часа)
start_delay: 0               # Задержка перед первым запуском в минутах
category_offset: 1          # Смещение времени запуска между категориями в минутах
max_concurrent_tasks: 13      # Максимальное количество одновременно выполняемых задач

# Категории для парсинга
categories:
  - apartments_sale
  - apartments_rent
  - houses_sale
  - houses_rent
  - rooms_sale
  - rooms_rent
  - garages_sale
  - garages_rent
  - dacha_sale
  - dacha_rent
  - land_sale
  - commercial_sale
  - commercial_rent

# Индивидуальные настройки для категорий
category_intervals:
  apartments_sale: 7200      # 2 часа
  apartments_rent: 7200      # 2 часа
  houses_sale: 10800         # 3 часа
  houses_rent: 259200         # 3 дня
  rooms_sale: 10800           # 3 часа
  rooms_rent: 14400           # 4 часа
  garages_sale: 14400        # 4 часа
  garages_rent: 14400        # 4 часа
  dacha_sale: 14400          # 4 часа
  dacha_rent: 14400          # 4 часа
  land_sale: 14400           # 4 часа
  commercial_sale: 10800     # 3 часа
  commercial_rent: 10800     # 3 часа

# Настройки повторных попыток
retry:
  enabled: true              # Включены ли повторные попытки
  max_attempts: 3            # Максимальное количество попыток
  delay: 300                 # Задержка между попытками в секундах
