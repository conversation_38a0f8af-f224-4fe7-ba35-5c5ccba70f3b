---
environment_variables:
  # Общие переменные окружения для всех сервисов
  DEFAULT:
    PYTHONUNBUFFERED: 1
    PYTHONDONTWRITEBYTECODE: 1
    TZ: Europe/Kiev

  # Переменные окружения для конкретных сервисов
  scheduler:
    # Специфичные для планировщика переменные
    SCHEDULER_ENABLED: true

  telegram-bot:
    # Специфичные для Telegram бота переменные
    TELEGRAM_BOT_ENABLED: true

  metrics:
    # Специфичные для сервиса метрик переменные
    METRICS_ENABLED: true

# Настройки для томов данных
volumes:
  data:
    type: directory
    path: /app/data
    permissions: 0777
  logs:
    type: directory
    path: /app/logs
    permissions: 0777

# Настройки для развертывания
deploy:
  # Стратегия развертывания
  strategy: rolling_update
  # Проверки работоспособности
  health_check:
    # Проверка для сервиса метрик
    metrics:
      type: http
      endpoint: /metrics
      protocol: http
      port: 8000
      initial_delay: 30
      period: 10
      timeout: 5
      success_threshold: 1
      failure_threshold: 3
